<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class RecalculateEndtimes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'endtime:recalculate 
                            {--status=ongoing : Status filter (ongoing, all)}
                            {--lot-id= : Specific lot ID to recalculate}
                            {--dry-run : Show what would be updated without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Recalculate endtimes for existing lots using corrected logic';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 Starting endtime recalculation with corrected logic...');
        
        $isDryRun = $this->option('dry-run');
        $status = $this->option('status');
        $specificLotId = $this->option('lot-id');
        
        if ($isDryRun) {
            $this->warn('🔍 DRY RUN MODE - No changes will be saved');
        }
        
        // Build query based on options
        $query = DB::table('endtime');
        
        if ($specificLotId) {
            $query->where('lot_id', $specificLotId);
            $this->info("🎯 Targeting specific lot: {$specificLotId}");
        } elseif ($status === 'ongoing') {
            $query->where('status', '!=', 'Submitted');
            $this->info('📋 Targeting ongoing lots only');
        } else {
            $this->info('📋 Targeting all lots');
        }
        
        $lots = $query->get();
        $this->info("📊 Found " . count($lots) . " lot(s) to process");
        
        $recalculatedCount = 0;
        $skippedCount = 0;
        $changes = [];
        
        $progressBar = $this->output->createProgressBar(count($lots));
        $progressBar->start();
        
        foreach ($lots as $lot) {
            try {
                // Get equipment assignments for this lot
                $equipmentAssignments = [];
                
                for ($i = 1; $i <= 10; $i++) {
                    $eqpField = 'eqp_' . $i;
                    $startTimeField = 'start_time_' . $i;
                    $ngPercentField = 'ng_percent_' . $i;
                    
                    if (!empty($lot->$eqpField) && !empty($lot->$startTimeField)) {
                        $equipmentAssignments[] = [
                            'eqp_no' => $lot->$eqpField,
                            'start_time' => $lot->$startTimeField,
                            'ng_percent' => $lot->$ngPercentField ?: 0
                        ];
                    }
                }
                
                if (empty($equipmentAssignments)) {
                    $skippedCount++;
                    $progressBar->advance();
                    continue;
                }
                
                // Get equipment data for calculations
                $profiles = [];
                foreach ($equipmentAssignments as $assignment) {
                    $equipment = DB::table('equipment')
                        ->where('eqp_no', $assignment['eqp_no'])
                        ->first();
                    
                    if (!$equipment || !$equipment->oee_capa) {
                        continue;
                    }
                    
                    $dailyCapacity = floatval($equipment->oee_capa);
                    if ($dailyCapacity <= 0) continue;
                    
                    $ngPercent = max(0, min(100, floatval($assignment['ng_percent'])));
                    $grossRatePerMinute = $dailyCapacity / 1440;
                    $netGoodRatePerMinute = $grossRatePerMinute * (1 - $ngPercent / 100);
                    
                    if ($netGoodRatePerMinute <= 0) continue;
                    
                    $startTime = Carbon::parse($assignment['start_time']);
                    
                    $profiles[] = [
                        'eqp_no' => $assignment['eqp_no'],
                        'start_time' => $startTime,
                        'rate_per_minute' => $netGoodRatePerMinute,
                        'oee_capacity' => $dailyCapacity,
                        'ng_percent' => $ngPercent,
                    ];
                }
                
                if (empty($profiles)) {
                    $skippedCount++;
                    $progressBar->advance();
                    continue;
                }
                
                // Sort profiles by start time
                usort($profiles, function($a, $b) {
                    return $a['start_time']->timestamp <=> $b['start_time']->timestamp;
                });
                
                $earliestStart = $profiles[0]['start_time'];
                $totalRate = array_sum(array_column($profiles, 'rate_per_minute'));
                
                if ($totalRate <= 0) {
                    $skippedCount++;
                    $progressBar->advance();
                    continue;
                }
                
                // CORRECTED LOGIC: Calculate completion time based on earliest start and total rate
                $baselineMinutes = $lot->lot_qty / $totalRate;
                $newEstimatedEndtime = $earliestStart->copy()->addMinutes($baselineMinutes);
                
                $oldEndtime = $lot->est_endtime ? Carbon::parse($lot->est_endtime) : null;
                
                // Check if there's a significant difference
                $significantChange = false;
                if ($oldEndtime) {
                    $diffMinutes = abs($newEstimatedEndtime->diffInMinutes($oldEndtime));
                    $significantChange = $diffMinutes > 5; // Consider 5+ minutes as significant
                }
                
                $changes[] = [
                    'lot_id' => $lot->lot_id,
                    'old_endtime' => $oldEndtime ? $oldEndtime->format('Y-m-d H:i:s') : 'NULL',
                    'new_endtime' => $newEstimatedEndtime->format('Y-m-d H:i:s'),
                    'difference_minutes' => $oldEndtime ? $newEstimatedEndtime->diffInMinutes($oldEndtime, false) : 0,
                    'equipment_count' => count($profiles),
                    'significant_change' => $significantChange
                ];
                
                // Update the lot with the new estimated endtime (unless dry run)
                if (!$isDryRun) {
                    DB::table('endtime')
                        ->where('id', $lot->id)
                        ->update([
                            'est_endtime' => $newEstimatedEndtime,
                            'updated_at' => now()
                        ]);
                }
                
                $recalculatedCount++;
                
            } catch (\Exception $e) {
                $this->error("\nError processing lot {$lot->lot_id}: " . $e->getMessage());
                $skippedCount++;
            }
            
            $progressBar->advance();
        }
        
        $progressBar->finish();
        $this->newLine(2);
        
        // Show summary
        $this->info("✅ Recalculation completed:");
        $this->table(['Metric', 'Count'], [
            ['Total lots processed', count($lots)],
            ['Successfully recalculated', $recalculatedCount],
            ['Skipped (no equipment/data)', $skippedCount],
        ]);
        
        // Show significant changes
        $significantChanges = collect($changes)->where('significant_change', true);
        if ($significantChanges->count() > 0) {
            $this->warn("\n🔍 Lots with significant time changes (>5 minutes):");
            $this->table(['Lot ID', 'Old Endtime', 'New Endtime', 'Diff (min)', 'Equipment'], 
                $significantChanges->map(function($change) {
                    return [
                        $change['lot_id'],
                        $change['old_endtime'],
                        $change['new_endtime'],
                        $change['difference_minutes'] > 0 ? '+' . $change['difference_minutes'] : $change['difference_minutes'],
                        $change['equipment_count']
                    ];
                })->toArray()
            );
        }
        
        if ($isDryRun) {
            $this->info("\n💡 To apply these changes, run the command without --dry-run");
        } else {
            $this->info("\n✅ All changes have been applied to the database");
        }
        
        return 0;
    }
}