<?php

namespace App\Http\Controllers;

use App\Models\LotRequest;
use App\Models\Equipment;
use App\Models\User;
use App\Models\AvailableLotsCache;
use App\Services\LotManagementService;
// Notifications are optional - will be loaded if they exist
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class LotRequestController extends Controller
{
    protected $lotManagementService;
    
    public function __construct(LotManagementService $lotManagementService)
    {
        $this->lotManagementService = $lotManagementService;
        $this->middleware('auth');
        // All authenticated users can access lot request functions
        // Authorization is handled at the method level for specific actions
    }

    /**
     * Get the authenticated user as a User model instance
     * 
     * @return User
     */
    private function getAuthenticatedUser(): User
    {
        return Auth::user();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = $this->getAuthenticatedUser();
        
        if ($user->isUser()) {
            // Users can see their own requests with priority calculation
            $lotRequests = LotRequest::with(['user', 'lotRequestItems.equipment', 'assignedManager', 'lotAssignments'])
                ->where('user_id', $user->id)
                ->latest()
                ->paginate(10);
        } else {
            // Managers and Admins see prioritized management queue
            $query = LotRequest::with(['user', 'lotRequestItems.equipment', 'assignedManager', 'lotAssignments', 'priority']);
            
            // Apply priority-based sorting for management: Pending Assignment first, then by elapsed time (highest first)
            $query->leftJoin('lot_request_priorities', 'lot_requests.id', '=', 'lot_request_priorities.lot_request_id')
                  ->select('lot_requests.*')
                  // First: Pending Assignment (status = 'pending') at the top
                  ->orderByRaw('CASE WHEN lot_requests.status = "pending" THEN 0 ELSE 1 END')
                  // Second: Within pending, urgent requests first
                  ->orderByRaw('CASE WHEN lot_requests.status = "pending" AND lot_requests.is_urgent = 1 THEN 0 ELSE 1 END')
                  // Third: Within pending, sort by elapsed time (oldest first = highest priority)
                  ->orderBy('lot_requests.request_date', 'asc')
                  // Fourth: For non-pending, maintain existing priority sorting
                  ->orderBy('lot_requests.priority_score', 'desc')
                  ->orderBy('lot_request_priorities.fifo_order', 'asc');
            
            $lotRequests = $query->paginate(15); // Show more for managers
            
            // Update priority scores for all visible requests
            foreach ($lotRequests as $request) {
                $calculatedScore = $this->lotManagementService->calculateRequestPriority($request);
                $request->update(['priority_score' => $calculatedScore]);
            }
        }
        
        return view('lot-requests.index', compact('lotRequests'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $equipment = Equipment::all();
        return view('lot-requests.create', compact('equipment'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Check for potential duplicate submission by checking recent requests
        $userId = Auth::user()->id;
        $recentDuplicate = LotRequest::where('user_id', $userId)
            ->where('created_at', '>=', now()->subMinutes(2))
            ->orderBy('created_at', 'desc')
            ->first();
            
        if ($recentDuplicate && $this->isDuplicateRequest($request, $recentDuplicate)) {
            return redirect()->route('lot-requests.index')
                ->with('info', 'This request appears to be a duplicate of a recent submission. If this was intentional, please wait a few minutes before submitting again.');
        }
        
        // Validation rules - all users can now select customers
        $rules = [
            'equipment_items' => 'required|array|min:1',
            'equipment_items.*.equipment_numbers' => 'required|string',
            'equipment_items.*.quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:1000',
            'user_id' => 'nullable|exists:users,id', // Allow all users to select customers, but it's optional
        ];
        
        $request->validate($rules);
        
        // Additional validation for equipment numbers
        $validationErrors = [];
        foreach ($request->equipment_items as $index => $item) {
            $equipmentNumbers = array_filter(array_map('trim', explode(',', $item['equipment_numbers'])));
            
            if (empty($equipmentNumbers)) {
                $validationErrors["equipment_items.{$index}.equipment_numbers"] = 'At least one equipment number is required.';
                continue;
            }
            
            foreach ($equipmentNumbers as $equipmentNumber) {
                if (!Equipment::where('eqp_no', $equipmentNumber)->exists()) {
                    $validationErrors["equipment_items.{$index}.equipment_numbers"] = "Equipment number '{$equipmentNumber}' does not exist.";
                    break;
                }
            }
        }
        
        if (!empty($validationErrors)) {
            return back()->withErrors($validationErrors)->withInput();
        }

        // Determine the user_id for the lot request
        // If a customer is selected, use that; otherwise, use the current user
        if ($request->filled('user_id')) {
            $userId = $request->user_id;
        } else {
            $userId = Auth::user()->id;
        }

        // Extract first equipment number for request number generation
        $firstEquipmentNumbers = array_filter(array_map('trim', explode(',', $request->equipment_items[0]['equipment_numbers'])));
        $firstEquipmentNumber = !empty($firstEquipmentNumbers) ? $firstEquipmentNumbers[0] : null;
        
        DB::beginTransaction();
        
        try {
            // Create lot request with equipment-based request number
            $lotRequest = LotRequest::create([
                'request_number' => LotRequest::generateRequestNumber($firstEquipmentNumber),
                'user_id' => $userId,
                'notes' => $request->notes,
                'request_date' => now(),
            ]);

            // Create lot request items
            foreach ($request->equipment_items as $item) {
                // Parse comma-separated equipment numbers
                $equipmentNumbers = array_filter(array_map('trim', explode(',', $item['equipment_numbers'])));
                
                if (empty($equipmentNumbers)) {
                    continue; // Skip if no equipment numbers
                }
                
                // Create separate lot request items for each equipment number in the group
                foreach ($equipmentNumbers as $equipmentNumber) {
                    $equipment = Equipment::where('eqp_no', $equipmentNumber)->first();
                    
                        if ($equipment) {
                            // Compose equipment code: TYPE-CLASS-WORK (e.g., COLOR-6S-NORMAL)
                            $composedCode = implode('-', array_filter([
                                $equipment->cam_class ?? null,
                                $equipment->insp_type ?? null,
                                $equipment->alloc_type ?? null,
                            ]));

                            $lotRequest->lotRequestItems()->create([
                                'equipment_number' => $equipmentNumber,
                                'equipment_code' => $composedCode ?: 'N/A',
                                'size' => $equipment->size ?? null,
                                'cam_class' => $equipment->cam_class ?? null,
                                'insp_type' => $equipment->insp_type ?? null,
                                'alloc_type' => $equipment->alloc_type ?? null,
                                'quantity' => $item['quantity'], // Same quantity for all equipment in the group
                            ]);
                        }
                }
            }
            
            DB::commit();
            
            return redirect()->route('lot-requests.index')
                ->with('success', 'Lot request created successfully!');
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()->withErrors([
                'error' => 'Failed to create lot request. Please try again.'
            ])->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(LotRequest $lotRequest)
    {
        // Deprecated page view – kept for backward compatibility (no route).
        $lotRequest->load(['lotRequestItems', 'user', 'lotAssignments.assignedBy']);
        $assignedLotIds = $lotRequest->lotAssignments->pluck('lot_id')->toArray();
        $wipDetails = [];
        if (!empty($assignedLotIds)) {
            $wipDetails = \App\Models\UpdateWip::whereIn('lot_id', $assignedLotIds)->get()->keyBy('lot_id');
        }
        return view('lot-requests.partials.details', compact('lotRequest', 'wipDetails'));
    }

    /**
     * Return modal-friendly details partial
     */
    public function details(LotRequest $lotRequest)
    {
        $lotRequest->load(['lotRequestItems.equipment', 'user', 'lotAssignments.assignedBy']);
        $assignedLotIds = $lotRequest->lotAssignments->pluck('lot_id')->toArray();
        $wipDetails = [];
        if (!empty($assignedLotIds)) {
            $wipDetails = \App\Models\UpdateWip::whereIn('lot_id', $assignedLotIds)->get()->keyBy('lot_id');
        }
        return view('lot-requests.partials.details', compact('lotRequest', 'wipDetails'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(LotRequest $lotRequest)
    {
        // Allow all authenticated users to edit lot requests
        // (Authorization can be added later if needed based on business requirements)
        
        $equipment = Equipment::all();
        $lotRequest->load(['lotRequestItems']);
        
        // Get available WIP data that matches the equipment codes
        $availableWip = $this->getAvailableWipForRequest($lotRequest);
        
        return view('lot-requests.edit', compact('lotRequest', 'equipment', 'availableWip'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, LotRequest $lotRequest)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Authentication required'], 401);
            }
            return redirect()->route('login')
                ->with('error', 'You must be logged in to update lot requests.');
        }

        // Allow all authenticated users to update lot requests
        // (Authorization can be added later if needed based on business requirements)
        
        // Role-based validation for status updates
        $user = $this->getAuthenticatedUser();
        if ($user->role !== 'admin' && $user->role !== 'manager') {
            // Regular users have limited status update options
            $request->validate([
                'status' => 'required|in:pending,cancelled',
                'notes' => 'nullable|string|max:1000',
            ]);
        } else {
            // Admins and managers can set any status
            $request->validate([
                'status' => 'required|in:pending,in_process,completed,cancelled',
                'notes' => 'nullable|string|max:1000',
            ]);
        }

        try {
            $updateData = [
                'status' => $request->status,
                'notes' => $request->notes,
            ];
            
            // Set timestamps based on status changes
            $oldStatus = $lotRequest->status;
            $newStatus = $request->status;
            
            // If moving from pending to in_process, set responded_at
            if ($oldStatus === 'pending' && $newStatus === 'in_process' && !$lotRequest->responded_at) {
                $updateData['responded_at'] = now();
            }
            
            // If moving to completed, set completed_at
            if ($newStatus === 'completed' && !$lotRequest->completed_at) {
                $updateData['completed_at'] = now();
                // Also set responded_at if not already set
                if (!$lotRequest->responded_at) {
                    $updateData['responded_at'] = now();
                }
            }
            
            // If moving back from completed or in_process to pending, clear timestamps
            if ($newStatus === 'pending') {
                $updateData['responded_at'] = null;
                $updateData['completed_at'] = null;
            }
            
            // If moving from completed back to in_process, clear completed_at
            if ($oldStatus === 'completed' && $newStatus === 'in_process') {
                $updateData['completed_at'] = null;
            }
            
            $lotRequest->update($updateData);

            return redirect()->route('lot-requests.index')
                ->with('success', 'Lot request updated successfully!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update lot request. Please try again.')
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(LotRequest $lotRequest)
    {
        $lotRequest->delete();
        
        return redirect()->route('lot-requests.index')
            ->with('success', 'Lot request deleted successfully!');
    }

    /**
     * Search employees by emp_no or emp_name
     */
    public function searchEmployees(Request $request)
    {
        $query = $request->get('q', '');
        
        if (strlen($query) < 2) {
            return response()->json([]);
        }
        
        // Search all users regardless of role - allow all users to be customers
        $users = User::where(function($q) use ($query) {
                $q->where('emp_no', 'LIKE', "%{$query}%")
                  ->orWhere('emp_name', 'LIKE', "%{$query}%");
            })
            ->limit(10)
            ->get(['id', 'emp_no', 'emp_name', 'role']);
            
        return response()->json($users);
    }

    /**
     * Get equipment code by equipment number
     */
    public function getEquipmentCode(Request $request)
    {
        $equipmentNumber = $request->get('equipment_number');
        
        if (!$equipmentNumber) {
            return response()->json(['error' => 'Equipment number is required'], 400);
        }
        
        $equipment = Equipment::where('eqp_no', $equipmentNumber)->first();
        
        if (!$equipment) {
            return response()->json(['error' => 'Equipment not found'], 404);
        }
        
        return response()->json([
            'equipment_code' => $equipment->eqp_code ?? '',
            'equipment_info' => [
                'line' => $equipment->eqp_line,
                'area' => $equipment->eqp_area,
                'type' => $equipment->eqp_type,
                'class' => $equipment->eqp_class,
                'size' => $equipment->size,
                'cam_class' => $equipment->cam_class,
                'insp_type' => $equipment->insp_type,
                'alloc_type' => $equipment->alloc_type,
            ]
        ]);
    }
    
    /**
     * Get available WIP data that matches equipment codes in the lot request
     */
    private function getAvailableWipForRequest($lotRequest)
    {
        // Get unique equipment codes from the lot request
        $equipmentCodes = $lotRequest->lotRequestItems
            ->pluck('equipment_code')
            ->filter()
            ->unique()
            ->toArray();
        
        if (empty($equipmentCodes)) {
            return collect();
        }
        
        // Get lot IDs that are already assigned to exclude them
        $assignedLotIds = \App\Models\LotAssignment::pluck('lot_id')->toArray();
        
        // Get WIP data that matches any of the equipment codes and is not already assigned
        $availableWip = \App\Models\UpdateWip::whereIn('lot_code', $equipmentCodes)
            ->whereIn('wip_status', ['Newlot Standby', 'Rework Lot Standby'])
            ->whereNotIn('lot_id', $assignedLotIds) // Exclude already assigned lots
            ->select(
                'lot_id',
                'model_15',
                'lot_size',
                'lot_qty',
                'stagnant_tat',
                'qty_class',
                'work_type',
                'wip_status',
                'lot_status',
                'hold',
                'auto_yn',
                'lipas_yn',
                'eqp_type',
                'eqp_class',
                'lot_location',
                'lot_code'
            )
            ->orderBy('lot_code')
            ->orderBy('lot_id')
            ->get();
            
        return $availableWip;
    }
    
    /**
     * Assign a lot to a lot request
     */
    public function assignLot(Request $request, LotRequest $lotRequest)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return response()->json(['error' => 'Authentication required'], 401);
        }
        
        // Check permissions
        $user = $this->getAuthenticatedUser();
        if (($user->role !== 'admin' && $user->role !== 'manager') && $lotRequest->user_id !== Auth::user()->id) {
            return response()->json(['error' => 'Unauthorized access'], 403);
        }
        
        // Validate request
        $request->validate([
            'lot_id' => 'required|string',
            'lot_code' => 'required|string'
        ]);
        
        DB::beginTransaction();
        
        try {
            // Check if lot is already assigned
            $existingAssignment = \App\Models\LotAssignment::where('lot_id', $request->lot_id)->first();
            if ($existingAssignment) {
                return response()->json([
                    'success' => false,
                    'message' => 'This lot is already assigned to another request.'
                ], 400);
            }
            
            // Get the lot from WIP to verify it exists and get details
            $wipLot = \App\Models\UpdateWip::where('lot_id', $request->lot_id)
                ->where('lot_code', $request->lot_code)
                ->whereIn('wip_status', ['Newlot Standby', 'Rework Lot Standby'])
                ->first();
                
            if (!$wipLot) {
                return response()->json([
                    'success' => false,
                    'message' => 'Lot not found or not available for assignment.'
                ], 404);
            }
            
            // Get equipment from lot request that matches the lot code
            $equipmentItem = $lotRequest->lotRequestItems
                ->where('equipment_code', $request->lot_code)
                ->first();
                
            if (!$equipmentItem) {
                return response()->json([
                    'success' => false,
                    'message' => 'No matching equipment found for this lot code.'
                ], 400);
            }
            
            // Create lot assignment
            \App\Models\LotAssignment::create([
                'lot_request_id' => $lotRequest->id,
                'lot_id' => $request->lot_id,
                'lot_code' => $request->lot_code,
                'equipment_number' => $equipmentItem->equipment_number,
                'equipment_code' => $equipmentItem->equipment_code,
                'lot_quantity' => $wipLot->lot_qty,
                'assigned_date' => now(),
                'assigned_by' => Auth::user()->id
            ]);

            // Remove lot from availability cache so it cannot be assigned elsewhere
            try {
                \DB::table('available_lots_cache')->where('lot_code', $request->lot_id)->delete();
            } catch (\Exception $e) {
                // Fallback: mark as unavailable
                try {
                    \DB::table('available_lots_cache')->where('lot_code', $request->lot_id)
                        ->update(['is_available' => 0, 'available_quantity' => 0]);
                } catch (\Exception $inner) {}
            }
            
            // Automatically update lot request status to 'in_process' if it's pending
            if ($lotRequest->status === 'pending') {
                $lotRequest->update([
                    'status' => 'in_process',
                    'responded_at' => now()
                ]);
            }
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'Lot assigned successfully!',
                'lot_request_status' => $lotRequest->fresh()->status
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign lot. Please try again.'
            ], 500);
        }
    }
    
    /**
     * Accept a lot request for management processing
     */
    public function accept(LotRequest $lotRequest)
    {
        // Check permissions
        $user = $this->getAuthenticatedUser();
        if (!$user->isManager() && !$user->isAdmin()) {
            return redirect()->back()->with('error', 'Unauthorized: Only managers can accept requests.');
        }
        
        if (!$lotRequest->canBeAccepted()) {
            return redirect()->back()->with('error', 'This request cannot be accepted in its current state.');
        }
        
        try {
            $lotRequest->acceptRequest($user->emp_no);
            
            // Notify request owner (optional - won't break if notification class doesn't exist)
            try {
                $lotRequest->loadMissing('user');
                if ($lotRequest->user && class_exists('App\Notifications\LotRequestAccepted')) {
                    $lotRequest->user->notify(new \App\Notifications\LotRequestAccepted($lotRequest, $user));
                }
            } catch (\Exception $notificationError) {
                // Log but don't fail the operation
                \Log::warning('Could not send notification: ' . $notificationError->getMessage());
            }
            
            // Redirect to assign-lots page to proceed with lot assignment
            return redirect()->route('lot-requests.assign-lots', $lotRequest)
                ->with('success', 'Lot request accepted! Now you can assign lots to this request.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update cache: ' . $e->getMessage());
        }
    }
    
    /**
     * Check if the current request is likely a duplicate of a recent request
     */
    private function isDuplicateRequest(Request $currentRequest, LotRequest $recentRequest)
    {
        // Load the recent request items for comparison
        $recentRequest->load('lotRequestItems');
        
        // Compare equipment numbers
        $currentEquipment = [];
        foreach ($currentRequest->equipment_items as $item) {
            $equipmentNumbers = array_filter(array_map('trim', explode(',', $item['equipment_numbers'])));
            foreach ($equipmentNumbers as $equipmentNumber) {
                $currentEquipment[] = [
                    'equipment_number' => $equipmentNumber,
                    'quantity' => $item['quantity']
                ];
            }
        }
        
        $recentEquipment = $recentRequest->lotRequestItems->map(function($item) {
            return [
                'equipment_number' => $item->equipment_number,
                'quantity' => $item->quantity
            ];
        })->toArray();
        
        // Sort both arrays for comparison
        usort($currentEquipment, fn($a, $b) => $a['equipment_number'] <=> $b['equipment_number']);
        usort($recentEquipment, fn($a, $b) => $a['equipment_number'] <=> $b['equipment_number']);
        
        // Compare equipment arrays
        if (count($currentEquipment) !== count($recentEquipment)) {
            return false;
        }
        
        for ($i = 0; $i < count($currentEquipment); $i++) {
            if ($currentEquipment[$i]['equipment_number'] !== $recentEquipment[$i]['equipment_number'] ||
                $currentEquipment[$i]['quantity'] != $recentEquipment[$i]['quantity']) {
                return false;
            }
        }
        
        // Compare notes (optional - only if both have notes or both are empty)
        $currentNotes = trim($currentRequest->notes ?? '');
        $recentNotes = trim($recentRequest->notes ?? '');
        
        // If notes are significantly different, it's probably not a duplicate
        if (!empty($currentNotes) && !empty($recentNotes) && $currentNotes !== $recentNotes) {
            return false;
        }
        
        // Compare user_id
        $currentUserId = $currentRequest->filled('user_id') ? $currentRequest->user_id : Auth::user()->id;
        $recentUserId = $recentRequest->user_id;
        
        return $currentUserId == $recentUserId;
    }
}
     * Show lot assignment interface
     */
    public function assignLots(LotRequest $lotRequest)
    {
        // Check permissions
        $user = $this->getAuthenticatedUser();
        if (!$user->isManager() && !$user->isAdmin()) {
            return redirect()->back()->with('error', 'Unauthorized: Only managers can assign lots.');
        }
        
        if (!$lotRequest->canAssignLots()) {
            return redirect()->back()->with('error', 'Lots cannot be assigned to this request in its current state.');
        }
        
        // Load relationships needed for the view
        $lotRequest->load(['lotRequestItems.equipment', 'user']);
        
        // Get available lots matching equipment requirements
        $availableLots = $this->lotManagementService->findMatchingLots($lotRequest);
        
        return view('lot-requests.assign-lots', compact('lotRequest', 'availableLots'));
    }
    
    /**
     * Process lot assignment to request
     */
    public function processAssignment(Request $request, LotRequest $lotRequest)
    {
        // Check permissions
        $user = $this->getAuthenticatedUser();
        if (!$user->isManager() && !$user->isAdmin()) {
            return redirect()->back()->with('error', 'Unauthorized: Only managers can assign lots.');
        }
        
        $request->validate([
            'selected_lots' => 'required|array|min:1',
        ]);
        
        // Prepare lot selections (auto-determine equipment and use full lot quantity)
        $lotSelections = [];
        foreach ($request->selected_lots as $lotCode) {
            $lot = AvailableLotsCache::where('lot_code', $lotCode)->first();
            if (!$lot) { continue; }
            // Auto-pick best matching equipment for this lot
            $equipmentNumber = $this->lotManagementService->getBestMatchingEquipment($lotRequest, $lot);
            $lotSelections[$lotCode] = [
                'quantity' => (int) ($lot->available_quantity ?? 1),
                'equipment_number' => $equipmentNumber,
            ];
        }
        
        try {
            $result = $this->lotManagementService->assignLotsToRequest($lotRequest, $lotSelections);
            
            if ($result['success']) {
                // Mark request as completed immediately after assignment per requirement
                $lotRequest->update([
                    'status' => 'completed',
                    'completed_at' => now(),
                    'responded_at' => $lotRequest->responded_at ?: now(),
                ]);
                
                // Notify request owner (optional)
                try {
                    $lotRequest->loadMissing('user');
                    if ($lotRequest->user && class_exists('App\Notifications\LotsAssigned')) {
                        $lotRequest->user->notify(new \App\Notifications\LotsAssigned($lotRequest, $user, count($result['assignments'])));
                    }
                } catch (\Exception $e) {
                    \Log::warning('Could not send notification: ' . $e->getMessage());
                }
                
                return redirect()->route('lot-requests.index')
                    ->with('success', 'Lots assigned and request marked as Completed. ' . count($result['assignments']) . ' lots were assigned.');
            } else {
                return redirect()->back()
                    ->withErrors($result['errors'])
                    ->withInput();
            }
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to assign lots: ' . $e->getMessage())
                ->withInput();
        }
    }
    
    /**
     * Mark request as delivered
     */
    public function deliver(LotRequest $lotRequest)
    {
        // Check permissions
        $user = $this->getAuthenticatedUser();
        if (!$user->isManager() && !$user->isAdmin()) {
            return redirect()->back()->with('error', 'Unauthorized: Only managers can mark requests as delivered.');
        }
        
        if (!$lotRequest->canBeDelivered()) {
            return redirect()->back()->with('error', 'This request cannot be marked as delivered in its current state.');
        }
        
        try {
            $lotRequest->markDelivered($user->emp_no);
            
            // Notify request owner (optional)
            try {
                $lotRequest->loadMissing('user');
                if ($lotRequest->user && class_exists('App\Notifications\LotRequestDelivered')) {
                    $lotRequest->user->notify(new \App\Notifications\LotRequestDelivered($lotRequest, $user));
                }
            } catch (\Exception $e) {
                \Log::warning('Could not send notification: ' . $e->getMessage());
            }
            
            return redirect()->back()->with('success', 'Request marked as delivered successfully!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to mark as delivered: ' . $e->getMessage());
        }
    }
    
    /**
     * Set priority for a lot request
     */
    public function setPriority(Request $request, LotRequest $lotRequest)
    {
        // Check permissions
        $user = $this->getAuthenticatedUser();
        if (!$user->isManager() && !$user->isAdmin()) {
            return redirect()->back()->with('error', 'Unauthorized: Only managers can set priorities.');
        }
        
        $request->validate([
            'is_urgent' => 'boolean',
            'manual_priority' => 'nullable|integer|min:0|max:1000',
            'priority_notes' => 'nullable|string|max:500',
        ]);
        
        try {
            DB::beginTransaction();
            
            // Update request urgency
            $lotRequest->update([
                'is_urgent' => $request->is_urgent ?? false,
            ]);
            
            // Update or create priority record
            $lotRequest->priority()->updateOrCreate(
                ['lot_request_id' => $lotRequest->id],
                [
                    'manual_priority' => $request->manual_priority ?? 0,
                    'priority_notes' => $request->priority_notes,
                    'priority_set_by' => $user->emp_no,
                    'priority_updated_at' => now(),
                ]
            );
            
            // Recalculate priority score
            $calculatedScore = $this->lotManagementService->calculateRequestPriority($lotRequest);
            $lotRequest->update(['priority_score' => $calculatedScore]);
            
            DB::commit();
            
            // Notify request owner (optional)
            try {
                $lotRequest->loadMissing('user');
                if ($lotRequest->user && class_exists('App\Notifications\LotRequestPriorityUpdated')) {
                    $lotRequest->user->notify(new \App\Notifications\LotRequestPriorityUpdated(
                        $lotRequest,
                        $user,
                        (bool) ($request->is_urgent ?? false),
                        (int) ($request->manual_priority ?? 0)
                    ));
                }
            } catch (\Exception $e) {
                \Log::warning('Could not send notification: ' . $e->getMessage());
            }
            
            return redirect()->back()->with('success', 'Priority updated successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Failed to update priority: ' . $e->getMessage());
        }
    }
    
    /**
     * Refresh available lots cache from UpdateWip table
     */
    public function refreshAvailableLotsCache(Request $request)
    {
        // Check permissions
        $user = $this->getAuthenticatedUser();
        if (!$user->isManager() && !$user->isAdmin()) {
            return response()->json(['error' => 'Unauthorized: Only managers can refresh lots'], 403);
        }
        
        try {
            // Set longer execution time for this operation
            set_time_limit(120); // 2 minutes
            
            // Start transaction
            DB::beginTransaction();
            
            // First, clear the cache table (outside transaction to avoid lock issues)
            DB::table('available_lots_cache')->delete();
            
            // Get all lots from updatewip where lot_code = 'AVAILABLE'
            $availableLots = DB::table('updatewip')
                ->where('lot_code', 'AVAILABLE')
                ->where('lot_qty', '>', 0) // Only get lots with quantity
                ->select([
                    'lot_id',
                    'model_15',
                    'lot_size', 
                    'eqp_type',
                    'eqp_class',
                    'work_type',
                    'lipas_yn',
                    'stagnant_tat',
                    'lot_location',
                    'qty_class',
                    'lot_qty'
                ])
                ->get();
            
            $insertedCount = 0;
            $batchData = [];
            $now = now();
            
            // Prepare batch insert data
            foreach ($availableLots as $wipLot) {
                $batchData[] = [
                    'lot_code' => $wipLot->lot_id, // Using lot_id as the unique identifier
                    'model_15' => $wipLot->model_15,
                    'lot_size' => $wipLot->lot_size,
                    'eqp_type' => $wipLot->eqp_type,
                    'eqp_class' => $wipLot->eqp_class,
                    'work_type' => $wipLot->work_type,
                    'lipas_yn' => ($wipLot->lipas_yn === 'Y' || $wipLot->lipas_yn === '1' || $wipLot->lipas_yn === true) ? 1 : 0,
'stagnant_tat' => is_numeric($wipLot->stagnant_tat) ? round((float) $wipLot->stagnant_tat, 2) : null,
                    'lot_location' => $wipLot->lot_location,
                    'available_quantity' => is_numeric($wipLot->lot_qty) ? (int)$wipLot->lot_qty : 0,
                    'qty_class' => $wipLot->qty_class,
                    'is_available' => 1,
                    'last_updated' => $now,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
                
                // Insert in chunks of 500 for better performance
                if (count($batchData) >= 500) {
                    DB::table('available_lots_cache')->insert($batchData);
                    $insertedCount += count($batchData);
                    $batchData = [];
                }
            }
            
            // Insert remaining records
            if (!empty($batchData)) {
                DB::table('available_lots_cache')->insert($batchData);
                $insertedCount += count($batchData);
            }
            
            DB::commit();
            
            // If it's an AJAX request, return JSON
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => "Successfully refreshed available lots cache. {$insertedCount} lots imported from WIP.",
                    'count' => $insertedCount
                ]);
            }
            
            // Otherwise redirect back with success message
            return redirect()->back()
                ->with('success', "Available lots refreshed successfully! {$insertedCount} lots imported from WIP.");
                
        } catch (\Exception $e) {
            // Only rollback if transaction is active
            if (DB::transactionLevel() > 0) {
                DB::rollBack();
            }
            
            \Log::error('Failed to refresh available lots cache: ' . $e->getMessage());
            
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to refresh lots: ' . $e->getMessage()
                ], 500);
            }
            
            return redirect()->back()
                ->with('error', 'Failed to refresh available lots: ' . $e->getMessage());
        }
    }
    
    /**
     * Get refreshed available lots for a request (AJAX)
     */
    public function getAvailableLots(LotRequest $lotRequest)
    {
        // Check permissions
        $user = $this->getAuthenticatedUser();
        if (!$user->isManager() && !$user->isAdmin()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }
        
        // Load relationships
        $lotRequest->load(['lotRequestItems.equipment']);
        
        // Get available lots
        $availableLots = $this->lotManagementService->findMatchingLots($lotRequest);
        
        return response()->json([
            'success' => true,
            'lotsCount' => count($availableLots),
            'lots' => $availableLots,
            'message' => count($availableLots) > 0 ? 
                'Found ' . count($availableLots) . ' available lots' : 
                'No compatible lots available'
        ]);
    }
    
    /**
     * Auto-assign best matching lots to a request
     */
    public function autoAssign(LotRequest $lotRequest)
    {
        // Check permissions
        $user = $this->getAuthenticatedUser();
        if (!$user->isManager() && !$user->isAdmin()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }
        
        if (!$lotRequest->canAssignLots()) {
            return response()->json(['error' => 'Request cannot have lots assigned in current state'], 400);
        }
        
        try {
            $result = $this->lotManagementService->autoAssignBestLots($lotRequest);
            
            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Auto-assignment completed! ' . count($result['assignments']) . ' lots were assigned.',
                    'assignments' => $result['assignments']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Auto-assignment failed',
                    'errors' => $result['errors']
                ], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Auto-assignment error: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Update lots availability cache
     */
    public function updateLotsCache()
    {
        // Check permissions - only admins can update cache
        $user = $this->getAuthenticatedUser();
        if (!$user->isAdmin()) {
            return redirect()->back()->with('error', 'Unauthorized: Only administrators can update lots cache.');
        }
        
        try {
            $this->lotManagementService->updateLotsCache();
            
            return redirect()->back()->with('success', 'Lots availability cache updated successfully!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update cache: ' . $e->getMessage());
        }
    }
}
