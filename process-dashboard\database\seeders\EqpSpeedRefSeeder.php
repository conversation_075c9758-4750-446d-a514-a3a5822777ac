<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class EqpSpeedRefSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing data
        DB::table('eqp_speed_ref')->truncate();

        $csvFile = base_path('eqp_speed_ref.csv');
        
        if (File::exists($csvFile)) {
            $csv = array_map('str_getcsv', file($csvFile));
            
            // Remove the header row
            $header = array_shift($csv);
            
            foreach ($csv as $row) {
                if (count($row) >= 3 && !empty($row[0])) {
                    // Remove commas and quotes from speed value
                    $speed = trim($row[2]);
                    $speed = str_replace(['"', ','], '', $speed);
                    
                    DB::table('eqp_speed_ref')->insert([
                        'eqp_type' => trim($row[0]),
                        'size' => trim($row[1]),
                        'eqp_speed' => (int)$speed,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }
    }
}
