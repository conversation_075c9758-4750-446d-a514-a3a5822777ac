<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create lot_request_priorities table if it doesn't exist
        if (!Schema::hasTable('lot_request_priorities')) {
            Schema::create('lot_request_priorities', function (Blueprint $table) {
                $table->id();
                $table->foreignId('lot_request_id')->constrained()->onDelete('cascade');
                $table->integer('fifo_order')->default(0);
                $table->integer('manual_priority')->default(0);
                $table->integer('calculated_priority')->default(0);
                $table->text('priority_notes')->nullable();
                $table->string('priority_set_by')->nullable();
                $table->timestamp('priority_updated_at')->nullable();
                $table->timestamps();
                
                $table->index(['fifo_order', 'manual_priority', 'calculated_priority'], 'lot_priorities_idx');
            });
        }

        // Create available_lots_cache table if it doesn't exist
        if (!Schema::hasTable('available_lots_cache')) {
            Schema::create('available_lots_cache', function (Blueprint $table) {
                $table->id();
                $table->string('lot_code')->unique();
                $table->string('lot_size', 10)->nullable();
                $table->string('eqp_type', 20)->nullable();
                $table->string('eqp_class', 10)->nullable();
                $table->string('work_type', 30)->nullable();
                $table->boolean('lipas_yn')->default(false);
                $table->integer('stagnant_tat')->nullable();
                $table->string('lot_location')->nullable();
                $table->integer('available_quantity')->default(0);
                $table->boolean('is_available')->default(true);
                $table->timestamp('last_updated')->nullable();
                $table->timestamps();
                
                $table->index(['lot_size', 'eqp_type', 'eqp_class', 'work_type']);
                $table->index(['lipas_yn', 'stagnant_tat']);
                $table->index('is_available');
            });
        }

        // Add missing columns to lot_assignments table if it exists
        if (Schema::hasTable('lot_assignments')) {
            Schema::table('lot_assignments', function (Blueprint $table) {
                if (!Schema::hasColumn('lot_assignments', 'assigned_by_manager')) {
                    $table->string('assigned_by_manager')->nullable()->after('assigned_by');
                }
                if (!Schema::hasColumn('lot_assignments', 'assignment_status')) {
                    $table->enum('assignment_status', ['pending', 'picked', 'delivered'])->default('pending')->after('assigned_by_manager');
                }
                if (!Schema::hasColumn('lot_assignments', 'picked_at')) {
                    $table->timestamp('picked_at')->nullable()->after('assignment_status');
                }
                if (!Schema::hasColumn('lot_assignments', 'delivered_at')) {
                    $table->timestamp('delivered_at')->nullable()->after('picked_at');
                }
                if (!Schema::hasColumn('lot_assignments', 'delivered_by')) {
                    $table->string('delivered_by')->nullable()->after('delivered_at');
                }
                if (!Schema::hasColumn('lot_assignments', 'lot_size')) {
                    $table->string('lot_size', 10)->nullable()->after('lot_code');
                }
                if (!Schema::hasColumn('lot_assignments', 'eqp_type')) {
                    $table->string('eqp_type', 20)->nullable()->after('lot_size');
                }
                if (!Schema::hasColumn('lot_assignments', 'eqp_class')) {
                    $table->string('eqp_class', 10)->nullable()->after('eqp_type');
                }
                if (!Schema::hasColumn('lot_assignments', 'work_type')) {
                    $table->string('work_type', 30)->nullable()->after('eqp_class');
                }
                if (!Schema::hasColumn('lot_assignments', 'lipas_yn')) {
                    $table->boolean('lipas_yn')->default(false)->after('work_type');
                }
                if (!Schema::hasColumn('lot_assignments', 'stagnant_tat')) {
                    $table->integer('stagnant_tat')->nullable()->after('lipas_yn');
                }
                if (!Schema::hasColumn('lot_assignments', 'lot_location')) {
                    $table->string('lot_location')->nullable()->after('stagnant_tat');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('available_lots_cache');
        Schema::dropIfExists('lot_request_priorities');
        
        // Remove added columns from lot_assignments if they exist
        if (Schema::hasTable('lot_assignments')) {
            Schema::table('lot_assignments', function (Blueprint $table) {
                $columns = [
                    'assigned_by_manager', 'assignment_status', 'picked_at', 
                    'delivered_at', 'delivered_by', 'lot_size', 'eqp_type', 
                    'eqp_class', 'work_type', 'lipas_yn', 'stagnant_tat', 'lot_location'
                ];
                
                foreach ($columns as $column) {
                    if (Schema::hasColumn('lot_assignments', $column)) {
                        $table->dropColumn($column);
                    }
                }
            });
        }
    }
};