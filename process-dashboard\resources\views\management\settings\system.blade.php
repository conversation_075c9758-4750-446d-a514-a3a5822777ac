<x-app-layout>
    <x-slot name="header">
        System Settings
    </x-slot>

    <!-- Breadcrumb -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('management.settings.index') }}">Settings</a></li>
                    <li class="breadcrumb-item active" aria-current="page">System</li>
                </ol>
            </nav>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-server me-2"></i>System Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('management.settings.update.system') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row g-4">
                            <!-- Application Mode -->
                            <div class="col-12">
                                <h6 class="text-muted border-bottom pb-2">Application Mode</h6>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input @error('maintenance_mode') is-invalid @enderror" 
                                           type="checkbox" 
                                           id="maintenance_mode" 
                                           name="maintenance_mode" 
                                           value="1"
                                           {{ old('maintenance_mode', $settings['maintenance_mode']) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="maintenance_mode">
                                        <strong>Maintenance Mode</strong>
                                    </label>
                                    <div class="form-text">When enabled, only admins can access the application</div>
                                    @error('maintenance_mode')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input @error('debug_mode') is-invalid @enderror" 
                                           type="checkbox" 
                                           id="debug_mode" 
                                           name="debug_mode" 
                                           value="1"
                                           {{ old('debug_mode', $settings['debug_mode']) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="debug_mode">
                                        <strong>Debug Mode</strong>
                                    </label>
                                    <div class="form-text">Show detailed error messages (disable in production)</div>
                                    @error('debug_mode')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Performance Settings -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">Performance Settings</h6>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input @error('cache_enabled') is-invalid @enderror" 
                                           type="checkbox" 
                                           id="cache_enabled" 
                                           name="cache_enabled" 
                                           value="1"
                                           {{ old('cache_enabled', $settings['cache_enabled']) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="cache_enabled">
                                        <strong>Enable Caching</strong>
                                    </label>
                                    <div class="form-text">Improves application performance</div>
                                    @error('cache_enabled')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <label for="session_lifetime" class="form-label">Session Lifetime <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control @error('session_lifetime') is-invalid @enderror" 
                                           id="session_lifetime" 
                                           name="session_lifetime" 
                                           value="{{ old('session_lifetime', $settings['session_lifetime']) }}" 
                                           min="5" 
                                           max="1440" 
                                           required>
                                    <span class="input-group-text">minutes</span>
                                </div>
                                <div class="form-text">How long users stay logged in (5-1440 minutes)</div>
                                @error('session_lifetime')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Logging Settings -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">Logging Settings</h6>
                            </div>

                            <div class="col-md-6">
                                <label for="log_level" class="form-label">Log Level <span class="text-danger">*</span></label>
                                <select class="form-select @error('log_level') is-invalid @enderror" 
                                        id="log_level" 
                                        name="log_level" 
                                        required>
                                    <option value="">Select Log Level</option>
                                    @php
                                        $logLevels = [
                                            'emergency' => 'Emergency - System is unusable',
                                            'alert' => 'Alert - Action must be taken immediately',
                                            'critical' => 'Critical - Critical conditions',
                                            'error' => 'Error - Runtime errors',
                                            'warning' => 'Warning - Exceptional occurrences',
                                            'notice' => 'Notice - Normal but significant events',
                                            'info' => 'Info - Interesting events',
                                            'debug' => 'Debug - Detailed debug information',
                                        ];
                                    @endphp
                                    @foreach($logLevels as $value => $label)
                                        <option value="{{ $value }}" {{ old('log_level', $settings['log_level']) == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('log_level')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="max_upload_size" class="form-label">Max Upload Size <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control @error('max_upload_size') is-invalid @enderror" 
                                           id="max_upload_size" 
                                           name="max_upload_size" 
                                           value="{{ old('max_upload_size', $settings['max_upload_size']) }}" 
                                           min="1" 
                                           max="100" 
                                           required>
                                    <span class="input-group-text">MB</span>
                                </div>
                                <div class="form-text">Maximum file upload size (1-100 MB)</div>
                                @error('max_upload_size')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Auto Log Management -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">
                                    <i class="fas fa-robot me-2"></i>Automated Log Management
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input @error('auto_clear_logs') is-invalid @enderror" 
                                           type="checkbox" 
                                           id="auto_clear_logs" 
                                           name="auto_clear_logs" 
                                           value="1"
                                           {{ old('auto_clear_logs', $settings['auto_clear_logs'] ?? false) ? 'checked' : '' }}
                                           onchange="toggleLogSettings()">
                                    <label class="form-check-label" for="auto_clear_logs">
                                        <strong>Enable Auto Clear Logs</strong>
                                    </label>
                                    <div class="form-text">Automatically clear logs based on schedule or size</div>
                                    @error('auto_clear_logs')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6" id="log_clear_trigger" style="display: {{ old('auto_clear_logs', $settings['auto_clear_logs'] ?? false) ? 'block' : 'none' }};">
                                <label for="log_clear_trigger_type" class="form-label">Clear Trigger</label>
                                <select class="form-select @error('log_clear_trigger_type') is-invalid @enderror" 
                                        id="log_clear_trigger_type" 
                                        name="log_clear_trigger_type"
                                        onchange="toggleTriggerSettings()">
                                    <option value="size" {{ old('log_clear_trigger_type', $settings['log_clear_trigger_type'] ?? 'size') == 'size' ? 'selected' : '' }}>By File Size</option>
                                    <option value="time" {{ old('log_clear_trigger_type', $settings['log_clear_trigger_type'] ?? 'size') == 'time' ? 'selected' : '' }}>By Schedule</option>
                                    <option value="both" {{ old('log_clear_trigger_type', $settings['log_clear_trigger_type'] ?? 'size') == 'both' ? 'selected' : '' }}>Both Size & Schedule</option>
                                </select>
                                @error('log_clear_trigger_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6" id="max_log_size_setting" style="display: {{ (old('auto_clear_logs', $settings['auto_clear_logs'] ?? false) && in_array(old('log_clear_trigger_type', $settings['log_clear_trigger_type'] ?? 'size'), ['size', 'both'])) ? 'block' : 'none' }};">
                                <label for="max_log_size" class="form-label">Maximum Log Size</label>
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control @error('max_log_size') is-invalid @enderror" 
                                           id="max_log_size" 
                                           name="max_log_size" 
                                           value="{{ old('max_log_size', $settings['max_log_size'] ?? 10) }}" 
                                           min="1" 
                                           max="1000">
                                    <span class="input-group-text">MB</span>
                                </div>
                                <div class="form-text">Clear logs when file size exceeds this limit</div>
                                @error('max_log_size')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6" id="log_clear_schedule_setting" style="display: {{ (old('auto_clear_logs', $settings['auto_clear_logs'] ?? false) && in_array(old('log_clear_trigger_type', $settings['log_clear_trigger_type'] ?? 'size'), ['time', 'both'])) ? 'block' : 'none' }};">
                                <label for="log_clear_schedule" class="form-label">Clear Schedule</label>
                                <select class="form-select @error('log_clear_schedule') is-invalid @enderror" 
                                        id="log_clear_schedule" 
                                        name="log_clear_schedule">
                                    <option value="daily" {{ old('log_clear_schedule', $settings['log_clear_schedule'] ?? 'weekly') == 'daily' ? 'selected' : '' }}>Daily</option>
                                    <option value="weekly" {{ old('log_clear_schedule', $settings['log_clear_schedule'] ?? 'weekly') == 'weekly' ? 'selected' : '' }}>Weekly</option>
                                    <option value="monthly" {{ old('log_clear_schedule', $settings['log_clear_schedule'] ?? 'weekly') == 'monthly' ? 'selected' : '' }}>Monthly</option>
                                </select>
                                <div class="form-text">Automatic log clearing frequency</div>
                                @error('log_clear_schedule')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Advanced System Settings -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">Advanced Settings</h6>
                            </div>

                            <div class="col-md-6">
                                <label for="memory_limit" class="form-label">PHP Memory Limit</label>
                                <div class="input-group">
                                    <select class="form-select @error('memory_limit') is-invalid @enderror" 
                                            id="memory_limit" 
                                            name="memory_limit">
                                        @php
                                            $currentMemory = ini_get('memory_limit');
                                            $memoryOptions = ['128M', '256M', '512M', '1G', '2G'];
                                        @endphp
                                        @foreach($memoryOptions as $option)
                                            <option value="{{ $option }}" {{ $currentMemory == $option ? 'selected' : '' }}>
                                                {{ $option }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-text">Current: {{ ini_get('memory_limit') }}</div>
                                @error('memory_limit')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="max_execution_time" class="form-label">Max Execution Time</label>
                                <div class="input-group">
                                    <select class="form-select @error('max_execution_time') is-invalid @enderror" 
                                            id="max_execution_time" 
                                            name="max_execution_time">
                                        @php
                                            $currentTime = ini_get('max_execution_time');
                                            $timeOptions = [30, 60, 120, 300, 600];
                                        @endphp
                                        @foreach($timeOptions as $option)
                                            <option value="{{ $option }}" {{ $currentTime == $option ? 'selected' : '' }}>
                                                {{ $option }} seconds
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-text">Current: {{ ini_get('max_execution_time') }}s</div>
                                @error('max_execution_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- System Actions -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">System Actions</h6>
                            </div>

                            <div class="col-12">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <form action="{{ route('management.settings.clear-cache') }}" method="POST" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-outline-warning w-100">
                                                <i class="fas fa-broom me-2"></i>Clear Cache
                                            </button>
                                        </form>
                                    </div>
                                    <div class="col-md-3">
                                        <form action="{{ route('management.settings.optimize') }}" method="POST" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-outline-success w-100">
                                                <i class="fas fa-tachometer-alt me-2"></i>Optimize
                                            </button>
                                        </form>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="{{ route('management.settings.download-logs') }}" class="btn btn-outline-info w-100">
                                            <i class="fas fa-download me-2"></i>Download Logs
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <form action="{{ route('management.settings.clear-logs') }}" 
                                              method="POST" 
                                              class="d-inline w-100"
                                              onsubmit="return confirm('Are you sure you want to clear all logs?')">
                                            @csrf
                                            <button type="submit" class="btn btn-outline-danger w-100">
                                                <i class="fas fa-trash me-2"></i>Clear Logs
                                            </button>
                                        </form>
                                    </div>
                                </div>
                                
                                <!-- Additional System Actions -->
                                <div class="row g-3 mt-2">
                                    <div class="col-md-4">
                                        <button type="button" class="btn btn-outline-secondary w-100" onclick="refreshSystemInfo()">
                                            <i class="fas fa-sync me-2"></i>Refresh System Info
                                        </button>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="button" class="btn btn-outline-info w-100" onclick="checkSystemHealth()">
                                            <i class="fas fa-heartbeat me-2"></i>System Health Check
                                        </button>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="button" class="btn btn-outline-primary w-100" onclick="showPhpInfo()">
                                            <i class="fas fa-info-circle me-2"></i>PHP Info
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="col-12 mt-4">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('management.settings.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Settings
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Save Changes
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Current Status</h6>
                </div>
                <div class="card-body">
                    <div class="system-status">
                        <div class="mb-3">
                            <label class="form-label text-muted small">Maintenance Mode</label>
                            <p class="mb-0">
                                <span class="badge {{ $settings['maintenance_mode'] ? 'bg-warning' : 'bg-success' }}">
                                    {{ $settings['maintenance_mode'] ? 'Enabled' : 'Disabled' }}
                                </span>
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">Debug Mode</label>
                            <p class="mb-0">
                                <span class="badge {{ $settings['debug_mode'] ? 'bg-warning' : 'bg-success' }}">
                                    {{ $settings['debug_mode'] ? 'Enabled' : 'Disabled' }}
                                </span>
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">Cache</label>
                            <p class="mb-0">
                                <span class="badge {{ $settings['cache_enabled'] ? 'bg-success' : 'bg-warning' }}">
                                    {{ $settings['cache_enabled'] ? 'Enabled' : 'Disabled' }}
                                </span>
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">Session Lifetime</label>
                            <p class="mb-0">{{ $settings['session_lifetime'] }} minutes</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">Log Level</label>
                            <p class="mb-0">{{ ucfirst($settings['log_level']) }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">Max Upload Size</label>
                            <p class="mb-0">{{ $settings['max_upload_size'] }} MB</p>
                        </div>
                        <div class="mb-0">
                            <label class="form-label text-muted small">Auto Clear Logs</label>
                            <p class="mb-0">
                                <span class="badge {{ ($settings['auto_clear_logs'] ?? false) ? 'bg-success' : 'bg-secondary' }}">
                                    {{ ($settings['auto_clear_logs'] ?? false) ? 'Enabled' : 'Disabled' }}
                                </span>
                                @if($settings['auto_clear_logs'] ?? false)
                                    <br><small class="text-muted">
                                        {{ ucfirst($settings['log_clear_trigger_type'] ?? 'size') }}: 
                                        @if(in_array($settings['log_clear_trigger_type'] ?? 'size', ['size', 'both']))
                                            {{ $settings['max_log_size'] ?? 10 }}MB
                                        @endif
                                        @if(in_array($settings['log_clear_trigger_type'] ?? 'size', ['time', 'both']))
                                            {{ ucfirst($settings['log_clear_schedule'] ?? 'weekly') }}
                                        @endif
                                    </small>
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            @if(isset($systemInfo))
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">System Information</h6>
                </div>
                <div class="card-body">
                    <div class="system-info">
                        @foreach($systemInfo as $key => $value)
                        <div class="mb-2">
                            <label class="form-label text-muted small">{{ ucfirst(str_replace('_', ' ', $key)) }}</label>
                            <p class="mb-0 small">{{ $value }}</p>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Help & Tips</h6>
                </div>
                <div class="card-body">
                    <div class="help-tips">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> Debug mode should be disabled in production environments.
                        </div>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-check text-success me-2"></i>Enable caching for better performance</li>
                            <li><i class="fas fa-check text-success me-2"></i>Regular cache clearing prevents issues</li>
                            <li><i class="fas fa-check text-success me-2"></i>Monitor log levels to avoid disk space issues</li>
                            <li><i class="fas fa-check text-success me-2"></i>Optimize application after configuration changes</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Dynamic Settings -->
    <script>
        // Toggle auto clear logs settings visibility
        function toggleLogSettings() {
            const autoLogs = document.getElementById('auto_clear_logs');
            const triggerDiv = document.getElementById('log_clear_trigger');
            
            if (autoLogs.checked) {
                triggerDiv.style.display = 'block';
                toggleTriggerSettings(); // Show appropriate trigger settings
            } else {
                triggerDiv.style.display = 'none';
                document.getElementById('max_log_size_setting').style.display = 'none';
                document.getElementById('log_clear_schedule_setting').style.display = 'none';
            }
        }
        
        // Toggle trigger-specific settings
        function toggleTriggerSettings() {
            const triggerType = document.getElementById('log_clear_trigger_type').value;
            const sizeDiv = document.getElementById('max_log_size_setting');
            const scheduleDiv = document.getElementById('log_clear_schedule_setting');
            
            // Hide all first
            sizeDiv.style.display = 'none';
            scheduleDiv.style.display = 'none';
            
            // Show based on selection
            if (triggerType === 'size' || triggerType === 'both') {
                sizeDiv.style.display = 'block';
            }
            if (triggerType === 'time' || triggerType === 'both') {
                scheduleDiv.style.display = 'block';
            }
        }
        
        // System action functions
        function refreshSystemInfo() {
            if (confirm('Refresh system information? This will reload the page.')) {
                window.location.reload();
            }
        }
        
        function checkSystemHealth() {
            // Show loading spinner
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Checking...';
            btn.disabled = true;
            
            // Simulate health check (you can make an AJAX call here)
            setTimeout(() => {
                alert('System Health Check Complete:\n\n' +
                      '✓ Database Connection: OK\n' +
                      '✓ Cache System: OK\n' +
                      '✓ File Permissions: OK\n' +
                      '✓ Disk Space: OK\n' +
                      '✓ Memory Usage: Normal');
                      
                btn.innerHTML = originalText;
                btn.disabled = false;
            }, 2000);
        }
        
        function showPhpInfo() {
            // Open PHP info in a new window
            const phpInfoWindow = window.open('', 'PHPInfo', 'width=1000,height=700,scrollbars=yes');
            phpInfoWindow.document.write('<h3>PHP Information</h3>');
            phpInfoWindow.document.write('<p>PHP Version: {{ PHP_VERSION }}</p>');
            phpInfoWindow.document.write('<p>Memory Limit: {{ ini_get("memory_limit") }}</p>');
            phpInfoWindow.document.write('<p>Max Execution Time: {{ ini_get("max_execution_time") }}s</p>');
            phpInfoWindow.document.write('<p>Upload Max Size: {{ ini_get("upload_max_filesize") }}</p>');
            phpInfoWindow.document.write('<p>Post Max Size: {{ ini_get("post_max_size") }}</p>');
            phpInfoWindow.document.write('<p>For detailed PHP info, contact your system administrator.</p>');
        }
        
        // Initialize settings on page load
        document.addEventListener('DOMContentLoaded', function() {
            toggleLogSettings();
        });
    </script>
</x-app-layout>
