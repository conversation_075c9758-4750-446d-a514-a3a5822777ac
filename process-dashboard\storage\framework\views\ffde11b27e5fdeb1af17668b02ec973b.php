<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        Create Lot Request
     <?php $__env->endSlot(); ?>

    <div class="container-fluid px-3">
        <div class="mx-auto page-narrow lot-request-create">

    <!-- Main Form Card -->
    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <i class="fas fa-plus-circle me-2"></i>
                <span>New Lot Request</span>
            </div>
            <small class="opacity-75">Professional FIFO + Priority</small>
        </div>
        
        <div class="card-body p-4">
            <form action="<?php echo e(route('lot-requests.store')); ?>" method="POST" id="lotRequestForm">
                <?php echo csrf_field(); ?>
                
                <!-- Row 1: Requestor and Equipment Details -->
                <div class="row mb-4">
                    <!-- Left: Requestor -->
                    <div class="col-md-6">
                        <label for="requestor_search" class="form-label">Requestor <small class="text-muted">(Optional)</small></label>
                        <div class="position-relative">
                            <input type="text" 
                                   class="form-control cyan-border <?php $__errorArgs = ['user_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="requestor_search" 
                                   placeholder="Search employee... (Leave empty for self)"
                                   autocomplete="off">
                            <input type="hidden" name="user_id" id="selected_user_id" value="<?php echo e(old('user_id')); ?>">
                            <div id="requestor_dropdown" class="requestor-dropdown" style="display: none;"></div>
                        </div>
                        <?php $__errorArgs = ['user_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback d-block"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                        <!-- Compact: Bring Equipment Numbers + Quantity into the same column -->
                        <div class="mt-3">
                            <label class="form-label">Equipment Numbers <span class="text-danger">*</span></label>
                            <div class="equipment-input position-relative">
                                <div class="chips-container cyan-border" id="chipsContainer">
                                    <input class="chip-input" id="chipInput" placeholder="Input Equipment number.." autocomplete="off" autofocus>
                                </div>
                                <div id="equipment_dropdown" class="equipment-dropdown" style="display:none;"></div>
                                <input type="hidden" name="equipment_items[0][equipment_numbers]" id="equipment_numbers_hidden" value="<?php echo e(old('equipment_items.0.equipment_numbers')); ?>">
                            </div>
                            <?php $__errorArgs = ['equipment_items.0.equipment_numbers'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback d-block"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <div class="form-text">Supports wildcard-like search. Examples: VI3*, F1, MONO, 6S</div>
                        </div>

                        <div class="mt-3">
                            <label for="quantity" class="form-label">Quantity (lots) <span class="text-danger">*</span></label>
                            <input type="number" class="form-control cyan-border <?php $__errorArgs = ['equipment_items.0.quantity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="quantity" 
                                   name="equipment_items[0][quantity]" 
                                   min="1" 
                                   value="<?php echo e(old('equipment_items.0.quantity', 1)); ?>" 
                                   placeholder="Input Equipment" 
                                   required>
                            <?php $__errorArgs = ['equipment_items.0.quantity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    
                    <!-- Right: Equipment Details Panel -->
                    <div class="col-md-6">
                        <div class="equipment-details-card">
                            <div class="d-flex align-items-center justify-content-between mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-microchip me-2 text-info"></i>
                                    <strong>Equipment Details</strong>
                                </div>
                                <div id="equipment_badges">
                                    <span class="badge bg-primary me-1">Size: —</span>
                                    <span class="badge bg-primary me-1">Type: —</span>
                                    <span class="badge bg-primary me-1">Class: —</span>
                                    <span class="badge bg-primary me-1">Line: —</span>
                                    <span class="badge bg-primary">Area: —</span>
                                </div>
                            </div>
                            <div id="equipment_info" class="text-muted small mb-3">No equipment selected</div>
                            <div class="table-container" style="max-height: 140px; overflow-y: auto;">
                                <table class="table table-sm table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th style="width: 60px;">No</th>
                                            <th style="width: 140px;">Code</th>
                                            <th style="width: 40px;">Line</th>
                                            <th style="width: 40px;">Area</th>
                                            <th style="width: 40px;">Size</th>
                                        </tr>
                                    </thead>
                                    <tbody id="equipment_list"></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- Row 3: Notes -->
                <div class="row mb-4">
                    <div class="col-12">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control cyan-border <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                  id="notes" 
                                  name="notes" 
                                  rows="4" 
                                  placeholder="Input any notes..." 
                                  style="resize: vertical;"><?php echo e(old('notes')); ?></textarea>
                        <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-outline-secondary" onclick="window.location.href='<?php echo e(route('lot-requests.index')); ?>'">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="fas fa-paper-plane me-1"></i><span id="submitText">Submit Request</span>
                        <span id="submitSpinner" class="spinner-border spinner-border-sm ms-2" style="display: none;"></span>
                    </button>
                </div>
            </form>
        </div>
    </div>
        </div>
    </div>

    <style>
        /* Cyan/Teal border styling to match Image 2 */
        .cyan-border {
            border: 2px solid #17a2b8 !important;
            border-radius: 8px !important;
        }
        
        .cyan-border:focus {
            border-color: #138496 !important;
            box-shadow: 0 0 0 0.15rem rgba(23, 162, 184, 0.25) !important;
            outline: none !important;
        }
        
        /* Equipment Details Card */
        .equipment-details-card {
            background: linear-gradient(135deg, #e3f2fd 0%, #e1f5fe 100%);
            border: 1px solid #81d4fa;
            border-radius: 8px;
            padding: 1rem;
            min-height: 160px;
        }
        
        /* Chips Container */
        .chips-container {
            background: #fff;
            padding: 0.75rem;
            min-height: 80px;
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            align-items: flex-start;
            align-content: flex-start;
        }
        
        .chip-input {
            border: none;
            outline: none;
            flex: 1 1 200px;
            min-width: 200px;
            padding: 0.3rem;
            font-size: 0.95rem;
        }
        
        .chip {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            color: #1565c0;
            border: 1px solid #90caf9;
            border-radius: 16px;
            padding: 0.25rem 0.5rem;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
            font-size: 0.85rem;
            font-weight: 500;
            box-shadow: 0 1px 3px rgba(25,118,210,0.1);
        }
        
        .chip .remove {
            cursor: pointer;
            color: #d32f2f;
            font-weight: bold;
            margin-left: 0.25rem;
            transition: all 0.2s ease;
        }
        
        .chip .remove:hover {
            color: #b71c1c;
            transform: scale(1.1);
        }
        
        /* Dropdown */
        .equipment-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            max-height: 250px;
            overflow-y: auto;
            z-index: 1050;
            margin-top: 0.25rem;
        }
        
        .equipment-dropdown-item {
            padding: 0.75rem;
            cursor: pointer;
            border-bottom: 1px solid #f1f3f4;
            transition: all 0.2s ease;
        }
        
        .equipment-dropdown-item:hover {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        
        .equipment-dropdown-item:last-child {
            border-bottom: none;
        }
        
        .equipment-dropdown-item.text-primary {
            color: #17a2b8 !important;
            font-weight: 600;
        }
        
        .equipment-dropdown-item.text-primary:hover {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }
        
        /* Requestor dropdown */
        .requestor-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            max-height: 200px;
            overflow-y: auto;
            z-index: 1050;
            margin-top: 0.25rem;
        }
        
        .requestor-dropdown-item {
            padding: 0.75rem;
            cursor: pointer;
            border-bottom: 1px solid #f1f3f4;
            transition: background-color 0.2s ease;
        }
        
        .requestor-dropdown-item:hover {
            background: #f8f9fa;
        }
        
        /* Table styling */
        .table-container {
            border-radius: 6px;
            overflow: hidden;
        }
        
        .table th {
            background: rgba(23, 162, 184, 0.1);
            color: #495057;
            font-weight: 600;
            font-size: 0.8rem;
            padding: 0.5rem 0.4rem;
            border: none;
        }
        
        .table td {
            padding: 0.4rem;
            font-size: 0.85rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .table-hover tbody tr:hover {
            background: rgba(23, 162, 184, 0.05);
        }
    </style>

    <style>
        /* Layout narrowing and professional spacing */
        .page-narrow {
            max-width: 1080px;
        }
        .lot-request-create { padding-top: .5rem; padding-bottom: 1.5rem; }
        .lot-request-create .card { border-radius: 12px; border: 1px solid #e9ecef; }
        .lot-request-create .card-header.bg-primary { background: linear-gradient(135deg, #17a2b8, #138496) !important; }
        .lot-request-create .form-label { font-weight: 600; }
        .lot-request-create .form-text { color: #6c757d; }
    </style>

    <script>
        let debounceTimer = null;
        let currentEquipment = null;
        const selectedEquipments = [];
        let isSubmitting = false;
        
        const equipmentData = <?php echo json_encode($equipment, 15, 512) ?>;
        
        // Helper to build display code: TYPE-CLASS-WORK
        function deriveCode(obj) {
            const type = obj?.cam_class ?? obj?.eqp_type ?? obj?.type ?? '';
            const cls  = obj?.insp_type ?? obj?.eqp_class ?? obj?.class ?? '';
            const work = obj?.alloc_type ?? obj?.work_type ?? '';
            const parts = [type, cls, work].filter(Boolean);
            return parts.length ? parts.join('-') : 'N/A';
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            setupRequestorSearch();
            setupChipsInput();
            setupFormSubmission();
            
            // Ensure Equipment Numbers input has focus
            setTimeout(() => {
                const chipInput = document.getElementById('chipInput');
                if (chipInput) {
                    chipInput.focus();
                }
            }, 100);
        });
        
        function setupRequestorSearch() {
            const requestorInput = document.getElementById('requestor_search');
            const requestorDropdown = document.getElementById('requestor_dropdown');
            
            if (!requestorInput || !requestorDropdown) return;
            
            requestorInput.addEventListener('input', function() {
                const query = this.value.trim();
                clearTimeout(debounceTimer);
                
                if (query.length < 2) {
                    requestorDropdown.style.display = 'none';
                    return;
                }
                
                debounceTimer = setTimeout(() => {
                    searchEmployees(query);
                }, 300);
            });
            
            document.addEventListener('click', function(e) {
                if (!requestorInput.contains(e.target) && !requestorDropdown.contains(e.target)) {
                    requestorDropdown.style.display = 'none';
                }
            });
        }
        
        function searchEmployees(query) {
            const apiUrl = `<?php echo e(route('api.search.employees')); ?>?q=${encodeURIComponent(query)}`;
            const dropdown = document.getElementById('requestor_dropdown');
            
            fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                },
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(users => {
                if (users.length === 0) {
                    dropdown.innerHTML = '<div class="requestor-dropdown-item text-muted">No users found</div>';
                } else {
                    const dropdownHtml = users.map(user => {
                        return `<div class="requestor-dropdown-item" 
                                     data-user-id="${user.id}" 
                                     data-user-info="${user.emp_name} (${user.emp_no})">
                                    <strong>${user.emp_name}</strong>
                                    <br><small class="text-muted">ID: ${user.emp_no} | Role: ${user.role}</small>
                                </div>`;
                    }).join('');
                    
                    dropdown.innerHTML = dropdownHtml;
                    
                    dropdown.querySelectorAll('.requestor-dropdown-item[data-user-id]').forEach(item => {
                        item.addEventListener('click', function() {
                            const userId = this.dataset.userId;
                            const userInfo = this.dataset.userInfo;
                            
                            document.getElementById('selected_user_id').value = userId;
                            document.getElementById('requestor_search').value = userInfo;
                            dropdown.style.display = 'none';
                        });
                    });
                }
                
                dropdown.style.display = 'block';
            })
            .catch(error => {
                console.error('Error searching employees:', error);
                dropdown.innerHTML = '<div class="requestor-dropdown-item text-danger">Error loading users</div>';
                dropdown.style.display = 'block';
            });
        }
        
        function setupChipsInput() {
            const chips = document.getElementById('chipsContainer');
            const input = document.getElementById('chipInput');
            const dropdown = document.getElementById('equipment_dropdown');
            
            if (!chips || !input) return;
            
            const hidden = document.getElementById('equipment_numbers_hidden');
            if (hidden && hidden.value) {
                hidden.value.split(',').map(s=>s.trim()).filter(Boolean).forEach(addChip);
            }
            
            const delimiters = [',', ';', ' '];
            
            input.addEventListener('input', function(){
                const q = this.value.trim();
                if (q.length === 0) { 
                    dropdown.style.display = 'none'; 
                    return; 
                }
                suggestEquipment(q);
            });
            
            input.addEventListener('keydown', function(e){
                if (e.key === 'Enter' || delimiters.includes(e.key)) {
                    e.preventDefault();
                    const value = input.value.trim().replace(/[;,]+$/, '');
                    if (value) {
                        if (/[*?]/.test(value)) {
                            addWildcardMatches(value);
                        } else {
                            addChip(value);
                        }
                    }
                } else if (e.key === 'Backspace' && input.value === '') {
                    const last = chips.querySelector('.chip:last-of-type');
                    if (last) { 
                        removeChip(last.dataset.no); 
                    }
                } else if (e.key === 'Escape') {
                    if (dropdown) { dropdown.style.display = 'none'; dropdown.innerHTML = ''; }
                }
            });
            
            input.addEventListener('paste', function(e){
                const text = (e.clipboardData || window.clipboardData).getData('text');
                if (!text) return; 
                e.preventDefault();
                text.split(/[\s,;]+/).map(s=>s.trim()).filter(Boolean).forEach(addChip);
            });
            
            document.addEventListener('click', function(e){ 
                if (!chips.contains(e.target) && !dropdown.contains(e.target)) { 
                    dropdown.style.display = 'none'; 
                    dropdown.innerHTML = ''; 
                }
            });

            // Hide dropdown shortly after input loses focus (allows click to register)
            input.addEventListener('blur', function(){
                setTimeout(() => {
                    if (dropdown) { dropdown.style.display = 'none'; dropdown.innerHTML = ''; }
                }, 150);
            });
            
            function addChip(no) {
                if (selectedEquipments.find(x=>x.eqp_no.toLowerCase() === no.toLowerCase())) { 
                    input.value = ''; 
                    return; 
                }
                
                const chip = document.createElement('span'); 
                chip.className = 'chip'; 
                chip.dataset.no = no; 
                chip.innerHTML = `${no} <span class="remove">&times;</span>`; 
                chip.querySelector('.remove').addEventListener('click', () => removeChip(no));
                chips.insertBefore(chip, input); 
                input.value = '';
                
                const eq = (equipmentData || []).find(e => String(e.eqp_no).toLowerCase() === String(no).toLowerCase());
                if (eq) {
                    const details = {
                        eqp_no: eq.eqp_no,
                        eqp_code: deriveCode(eq),
                        line: eq.eqp_line,
                        area: eq.eqp_area,
                        size: eq.size,
                        cam_class: eq.cam_class || eq.eqp_type,
                        insp_type: eq.insp_type || eq.eqp_class,
                        alloc_type: eq.alloc_type || eq.work_type,
                    };
                    selectedEquipments.push(details);
                    updateHiddenAndDetails();
                } else {
                    checkEquipmentExists(no).finally(updateHiddenAndDetails);
                }
                
                // Hide dropdown after adding
                if (dropdown) { dropdown.style.display = 'none'; dropdown.innerHTML = ''; }
            }
            
            function removeChip(no) {
                const idx = selectedEquipments.findIndex(x => x.eqp_no.toLowerCase() === no.toLowerCase()); 
                if (idx >= 0) selectedEquipments.splice(idx, 1);
                const chipEl = chips.querySelector(`.chip[data-no="${CSS.escape(no)}"]`); 
                if (chipEl) chipEl.remove();
                updateHiddenAndDetails();
            }
            
            function updateHiddenAndDetails() {
                const hidden = document.getElementById('equipment_numbers_hidden'); 
                hidden.value = selectedEquipments.map(x => x.eqp_no).join(',');
                
                const tbody = document.getElementById('equipment_list'); 
                const info = document.getElementById('equipment_info'); 
                const badges = document.getElementById('equipment_badges');
                
                if (selectedEquipments.length === 0) { 
                    tbody.innerHTML = ''; 
                    badges.innerHTML = `<span class="badge bg-primary me-1">Size: —</span><span class="badge bg-primary me-1">Type: —</span><span class="badge bg-primary me-1">Class: —</span><span class="badge bg-primary me-1">Line: —</span><span class="badge bg-primary">Area: —</span>`; 
                    info.textContent = 'No equipment selected'; 
                    return; 
                }
                
                tbody.innerHTML = selectedEquipments.map(eq => `<tr><td>${eq.eqp_no}</td><td>${deriveCode(eq)}</td><td>${eq.line||''}</td><td>${eq.area||''}</td><td>${eq.size||''}</td></tr>`).join('');
                
                const uniq = arr => Array.from(new Set(arr.filter(Boolean))); 
                const sizes = uniq(selectedEquipments.map(x => x.size)); 
                const types = uniq(selectedEquipments.map(x => x.cam_class)); 
                const classes = uniq(selectedEquipments.map(x => x.insp_type)); 
                const lines = uniq(selectedEquipments.map(x => x.line)); 
                const areas = uniq(selectedEquipments.map(x => x.area));
                
                badges.innerHTML = `<span class="badge bg-primary me-1">Size: ${sizes.join('/')||'—'}</span><span class="badge bg-primary me-1">Type: ${types.join('/')||'—'}</span><span class="badge bg-primary me-1">Class: ${classes.join('/')||'—'}</span><span class="badge bg-primary me-1">Line: ${lines.join('/')||'—'}</span><span class="badge bg-primary">Area: ${areas.join('/')||'—'}</span>`;
                info.textContent = `${selectedEquipments.length} equipment selected`;
            }
            
            function addWildcardMatches(pattern) {
                const regex = new RegExp('^' + pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&').replace(/\\\*/g, '.*').replace(/\\\?/g, '.') + '$', 'i');
                const matches = (equipmentData || []).filter(eq => regex.test(String(eq.eqp_no)) || regex.test(String(eq.eqp_code || '')));
                
                matches.forEach(eq => {
                    if (!selectedEquipments.find(x => x.eqp_no.toLowerCase() === String(eq.eqp_no).toLowerCase())) {
                        selectedEquipments.push({
                            eqp_no: eq.eqp_no,
                            eqp_code: deriveCode(eq),
                            line: eq.eqp_line,
                            area: eq.eqp_area,
                            size: eq.size,
                            cam_class: eq.cam_class || eq.eqp_type,
                            insp_type: eq.insp_type || eq.eqp_class,
                            alloc_type: eq.alloc_type || eq.work_type,
                        });
                        
                        const chip = document.createElement('span'); 
                        chip.className = 'chip'; 
                        chip.dataset.no = eq.eqp_no; 
                        chip.innerHTML = `${eq.eqp_no} <span class="remove">&times;</span>`; 
                        chip.querySelector('.remove').addEventListener('click', () => removeChip(eq.eqp_no));
                        chips.insertBefore(chip, input);
                    }
                });
                
                input.value = '';
                if (dropdown) { dropdown.style.display = 'none'; dropdown.innerHTML = ''; }
                updateHiddenAndDetails();
            }
            
            window.__addChip = addChip;
            window.__addWildcardAll = addWildcardMatches;
        }
        
        function setupFormSubmission() {
            const form = document.getElementById('lotRequestForm');
            const submitBtn = document.getElementById('submitBtn');
            const submitText = document.getElementById('submitText');
            const submitSpinner = document.getElementById('submitSpinner');
            
            form.addEventListener('submit', function(e) {
                // Prevent double submission
                if (isSubmitting) {
                    e.preventDefault();
                    return false;
                }
                
                // Validate required fields before submission
                const equipmentNumbers = document.getElementById('equipment_numbers_hidden').value.trim();
                const quantity = document.getElementById('quantity').value;
                
                if (!equipmentNumbers) {
                    e.preventDefault();
                    alert('Please select at least one equipment number.');
                    return false;
                }
                
                if (!quantity || quantity < 1) {
                    e.preventDefault();
                    alert('Please enter a valid quantity.');
                    return false;
                }
                
                // Set submission state
                isSubmitting = true;
                
                // Update button appearance
                submitBtn.disabled = true;
                submitText.textContent = 'Submitting...';
                submitSpinner.style.display = 'inline-block';
                
                // Disable all form inputs to prevent changes
                const inputs = form.querySelectorAll('input, textarea, button, select');
                inputs.forEach(input => {
                    if (input.id !== 'submitBtn') {
                        input.disabled = true;
                    }
                });
                
                return true;
            });
            
            // Re-enable form if user navigates back
            window.addEventListener('pageshow', function(event) {
                if (event.persisted || window.performance?.getEntriesByType('navigation')[0]?.type === 'back_forward') {
                    resetFormSubmissionState();
                }
            });
        }
        
        function resetFormSubmissionState() {
            const form = document.getElementById('lotRequestForm');
            const submitBtn = document.getElementById('submitBtn');
            const submitText = document.getElementById('submitText');
            const submitSpinner = document.getElementById('submitSpinner');
            
            isSubmitting = false;
            
            if (submitBtn) {
                submitBtn.disabled = false;
                submitText.textContent = 'Submit Request';
                submitSpinner.style.display = 'none';
            }
            
            // Re-enable all form inputs
            if (form) {
                const inputs = form.querySelectorAll('input, textarea, button, select');
                inputs.forEach(input => {
                    input.disabled = false;
                });
            }
        }
        
        function checkEquipmentExists(equipmentNumber) {
            const apiUrl = `<?php echo e(route('api.equipment.code')); ?>?equipment_number=${encodeURIComponent(equipmentNumber)}`;
            
            return fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                },
                credentials: 'same-origin'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Equipment not found');
                }
                return response.json();
            })
            .then(data => {
                const details = {
                    eqp_no: equipmentNumber,
                    eqp_code: deriveCode({
                        cam_class: data.equipment_info?.cam_class || data.equipment_info?.type,
                        insp_type: data.equipment_info?.insp_type || data.equipment_info?.class,
                        alloc_type: data.equipment_info?.alloc_type,
                    }),
                    line: data.equipment_info?.line,
                    area: data.equipment_info?.area,
                    size: data.equipment_info?.size,
                    cam_class: data.equipment_info?.cam_class || data.equipment_info?.type,
                    insp_type: data.equipment_info?.insp_type || data.equipment_info?.class,
                    alloc_type: data.equipment_info?.alloc_type,
                };
                
                const idx = selectedEquipments.findIndex(x => x.eqp_no.toLowerCase() === details.eqp_no.toLowerCase());
                if (idx >= 0) {
                    selectedEquipments[idx] = { ...selectedEquipments[idx], ...details };
                } else {
                    selectedEquipments.push(details);
                }
                
                const dropdown = document.getElementById('equipment_dropdown');
                if (dropdown) { dropdown.style.display = 'none'; dropdown.innerHTML = ''; }
            })
            .catch(error => {
                console.log('Equipment not found in API:', equipmentNumber);
            });
        }
        
        function suggestEquipment(query) {
            const dropdown = document.getElementById('equipment_dropdown');
            if (!dropdown) return;
            
            const q = query.toLowerCase();
            if (!equipmentData || equipmentData.length === 0 || q.length === 0) {
                dropdown.style.display = 'none';
                return;
            }
            
            let matches = equipmentData.filter(eq => {
                const no = String(eq.eqp_no || '').toLowerCase();
                const code = String(eq.eqp_code || '').toLowerCase();
                return no.includes(q) || code.includes(q);
            }).slice(0, 12);
            
            // Wildcard handling
            if (/[*?]/.test(query)) {
                const regex = new RegExp('^' + q.replace(/[.*+?^${}()|[\]\\]/g, '\\$&').replace(/\\\*/g, '.*').replace(/\\\?/g, '.') + '$', 'i');
                matches = equipmentData.filter(eq => regex.test(String(eq.eqp_no).toLowerCase()) || regex.test(String(eq.eqp_code||'').toLowerCase())).slice(0, 50);
            }
            
            if (matches.length === 0) {
                dropdown.innerHTML = `<div class="equipment-dropdown-item text-muted">No suggestions for "${query}"</div>`;
                dropdown.style.display = 'block';
                return;
            }
            
            const addAll = /[*?]/.test(query) && matches.length > 0 ? `<div class="equipment-dropdown-item text-primary" onclick="__addWildcardAll('${query.replace(/'/g, "&#39;")}')">+ Add all ${matches.length} matches</div>` : '';
            
            dropdown.innerHTML = addAll + matches.map(eq => {
                const line = eq.eqp_line || 'N/A';
                const area = eq.eqp_area || 'N/A';
                const code = deriveCode(eq);
                return `<div class="equipment-dropdown-item" onclick="__addChip('${eq.eqp_no.replace(/'/g, "&#39;")}')">
                    <strong>${eq.eqp_no}</strong> ${code ? `<span class='badge bg-secondary ms-2'>${code}</span>` : ''}
                    <br><small class="text-muted">Line: ${line} | Area: ${area}</small>
                </div>`;
            }).join('');
            
            dropdown.style.display = 'block';
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?><?php /**PATH C:\inetpub\wwwroot\process-dashboard\resources\views/lot-requests/create.blade.php ENDPATH**/ ?>