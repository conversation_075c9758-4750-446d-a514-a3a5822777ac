# Multi-Area Lot Calculation Strategy

## 📊 **Scenario Analysis**

### **Given:**
- **1 Lot**: 10M PCS quantity
- **Line F**: Total assignment
- **4 Equipment**: Used for processing
  - **3 Equipment** from **Area 1**
  - **1 Equipment** from **Area 2**

### **Question:**
How should we attribute the 10M PCS performance to Area 1 vs Area 2?

---

## 🎯 **Calculation Strategy Options**

### **Option A: Proportional Distribution by Equipment Count**
```
Area 1 Attribution = (3 equipment / 4 total equipment) × 10M PCS = 7.5M PCS
Area 2 Attribution = (1 equipment / 4 total equipment) × 10M PCS = 2.5M PCS
```

### **Option B: Proportional Distribution by Equipment Capacity**
```php
// Calculate based on actual equipment capacity
$area1Equipment = ['EQP_F_A1_01', 'EQP_F_A1_02', 'EQP_F_A1_03'];
$area2Equipment = ['EQP_F_A2_01'];

$area1Capacity = Equipment::whereIn('eqp_no', $area1Equipment)
                         ->sum(DB::raw('eqp_oee * eqp_speed * operation_time'));
                         
$area2Capacity = Equipment::whereIn('eqp_no', $area2Equipment)
                         ->sum(DB::raw('eqp_oee * eqp_speed * operation_time'));

$totalCapacity = $area1Capacity + $area2Capacity;

$area1Attribution = ($area1Capacity / $totalCapacity) × 10M PCS
$area2Attribution = ($area2Capacity / $totalCapacity) × 10M PCS
```

### **Option C: Primary Area Attribution**
```
// Assign to the area with the most equipment
Area 1 = 10M PCS (because it has 3/4 equipment = majority)
Area 2 = 0 PCS
```

### **Option D: Weighted by Processing Time**
```php
// If we track actual processing time per equipment
$area1ProcessingTime = sum of (start_time to end_time) for 3 equipment
$area2ProcessingTime = sum of (start_time to end_time) for 1 equipment

$totalProcessingTime = $area1ProcessingTime + $area2ProcessingTime;

$area1Attribution = ($area1ProcessingTime / $totalProcessingTime) × 10M PCS
$area2Attribution = ($area2ProcessingTime / $totalProcessingTime) × 10M PCS
```

---

## 💻 **Recommended Implementation: Option B (Capacity-Based)**

### **Why Capacity-Based is Best:**
- ✅ **Most Accurate**: Reflects actual equipment contribution
- ✅ **Performance-Based**: Higher capacity equipment gets higher attribution
- ✅ **Fair Distribution**: Based on actual work capacity, not just count
- ✅ **Business Logic**: Aligns with production reality

### **Implementation Code:**

```php
private function calculateLotAreaAttribution($lotRecord, $filters)
{
    $attributions = [];
    $equipmentUsed = [];
    
    // Collect all equipment used in this lot
    for ($i = 1; $i <= 10; $i++) {
        $equipmentNo = $lotRecord->{"eqp_{$i}"};
        if (!empty($equipmentNo)) {
            $equipmentUsed[] = $equipmentNo;
        }
    }
    
    if (empty($equipmentUsed)) {
        return []; // No equipment used
    }
    
    // Get equipment details with area information
    $equipmentDetails = Equipment::whereIn('eqp_no', $equipmentUsed)
                                 ->select('eqp_no', 'eqp_line', 'eqp_area', 'eqp_oee', 'eqp_speed', 'operation_time')
                                 ->get()
                                 ->keyBy('eqp_no');
    
    // Calculate capacity per area
    $areaCapacities = [];
    $totalCapacity = 0;
    
    foreach ($equipmentUsed as $eqpNo) {
        $equipment = $equipmentDetails[$eqpNo] ?? null;
        if (!$equipment) continue;
        
        $area = $equipment->eqp_area;
        $oee = floatval($equipment->eqp_oee) ?: 0;
        $speed = floatval(str_replace(',', '', $equipment->eqp_speed)) ?: 0;
        $operationTime = floatval(str_replace(',', '', $equipment->operation_time)) ?: 0;
        
        $capacity = $oee * $speed * $operationTime;
        
        if (!isset($areaCapacities[$area])) {
            $areaCapacities[$area] = 0;
        }
        $areaCapacities[$area] += $capacity;
        $totalCapacity += $capacity;
    }
    
    // Calculate attribution per area
    $lotQuantity = $lotRecord->lot_qty;
    
    foreach ($areaCapacities as $area => $areaCapacity) {
        $attributionPercent = $totalCapacity > 0 ? ($areaCapacity / $totalCapacity) : 0;
        $attributions[$area] = [
            'quantity' => round($lotQuantity * $attributionPercent),
            'percentage' => round($attributionPercent * 100, 2),
            'capacity_contribution' => $areaCapacity
        ];
    }
    
    return $attributions;
}

private function calculateAreaPerformance($line, $area, $filters)
{
    // Get all lots that used equipment from this line
    $lotsQuery = Endtime::query();
    $this->applyDashboardTimeFilter($lotsQuery, $filters['dashboard_date'], 
                                   $filters['dashboard_shift'], $filters['dashboard_cutoff']);
    
    // Apply work type filter
    if ($filters['dashboard_work_type'] !== 'all') {
        $lotsQuery->where('work_type', $filters['dashboard_work_type']);
    }
    
    // Get lots that use any equipment from this line
    $lotsInLine = $lotsQuery->where('eqp_line', $line)->get();
    
    $areaTarget = $this->getAreaTargetCapacity($line, $area, $filters);
    $areaSubmittedQuantity = 0;
    
    foreach ($lotsInLine as $lot) {
        if ($lot->status !== 'Submitted') continue;
        
        // Calculate how much of this lot should be attributed to this area
        $lotAttributions = $this->calculateLotAreaAttribution($lot, $filters);
        
        if (isset($lotAttributions[$area])) {
            $areaSubmittedQuantity += $lotAttributions[$area]['quantity'];
        }
    }
    
    return $areaTarget > 0 ? round(($areaSubmittedQuantity / $areaTarget) * 100, 1) : 0;
}
```

---

## 📊 **Example Calculation**

### **Given Data:**
```
Lot: F001, Quantity: 10,000,000 PCS
Equipment Used:
- EQP_F_A1_01 (Area 1): OEE=0.85, Speed=1200, OpTime=8 → Capacity = 8,160
- EQP_F_A1_02 (Area 1): OEE=0.90, Speed=1100, OpTime=8 → Capacity = 7,920  
- EQP_F_A1_03 (Area 1): OEE=0.88, Speed=1000, OpTime=8 → Capacity = 7,040
- EQP_F_A2_01 (Area 2): OEE=0.95, Speed=1500, OpTime=8 → Capacity = 11,400

Total Capacity = 8,160 + 7,920 + 7,040 + 11,400 = 34,520
```

### **Attribution Calculation:**
```
Area 1 Capacity = 8,160 + 7,920 + 7,040 = 23,120
Area 2 Capacity = 11,400

Area 1 Attribution = (23,120 / 34,520) × 10M = 6,697,398 PCS (66.97%)
Area 2 Attribution = (11,400 / 34,520) × 10M = 3,302,602 PCS (33.03%)
```

### **Performance Impact:**
```
If this lot is submitted:
- Area 1 gets credit for 6.7M PCS toward its performance
- Area 2 gets credit for 3.3M PCS toward its performance
- Each area's performance % = (attributed quantity / area target capacity) × 100
```

---

## 🔄 **Alternative Simplified Approach**

If the capacity-based calculation is too complex initially, we can use:

```php
// Simplified: Equal distribution among equipment
private function calculateSimpleAreaAttribution($lotRecord)
{
    $equipmentByArea = [];
    $totalEquipment = 0;
    
    // Count equipment per area
    for ($i = 1; $i <= 10; $i++) {
        $equipmentNo = $lotRecord->{"eqp_{$i}"};
        if (!empty($equipmentNo)) {
            $equipment = Equipment::where('eqp_no', $equipmentNo)->first();
            if ($equipment) {
                $area = $equipment->eqp_area;
                $equipmentByArea[$area] = ($equipmentByArea[$area] ?? 0) + 1;
                $totalEquipment++;
            }
        }
    }
    
    // Calculate simple percentage attribution
    $attributions = [];
    foreach ($equipmentByArea as $area => $equipmentCount) {
        $attributions[$area] = [
            'quantity' => round(($equipmentCount / $totalEquipment) * $lotRecord->lot_qty),
            'percentage' => round(($equipmentCount / $totalEquipment) * 100, 2)
        ];
    }
    
    return $attributions;
}
```

---

## 🎯 **Recommendation**

For your scenario (1 lot, 10M PCS, 3 equipment Area 1, 1 equipment Area 2):

1. **Start with Capacity-Based Attribution (Option B)** - Most accurate
2. **Fall back to Count-Based (Simplified)** if capacity data is inconsistent
3. **Document the business rule** for consistency across the system
4. **Allow configuration** to switch between methods if needed

This approach ensures fair and accurate performance attribution while being implementable with your current database structure.

---

*Generated: {{ date('Y-m-d H:i:s') }}*
*Laravel Process Dashboard - Multi-Area Lot Calculation Strategy*
