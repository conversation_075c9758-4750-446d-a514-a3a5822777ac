<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

return new class extends Migration
{
    /**
     * Run the migration to recalculate all existing endtimes with corrected logic.
     */
    public function up()
    {
        // Get all ongoing lots that need recalculation
        $lots = DB::table('endtime')
            ->where('status', '!=', 'Submitted')
            ->get();

        $recalculatedCount = 0;
        $skippedCount = 0;

        foreach ($lots as $lot) {
            try {
                // Get equipment assignments for this lot
                $equipmentAssignments = [];
                
                for ($i = 1; $i <= 10; $i++) {
                    $eqpField = 'eqp_' . $i;
                    $startTimeField = 'start_time_' . $i;
                    $ngPercentField = 'ng_percent_' . $i;
                    
                    if (!empty($lot->$eqpField) && !empty($lot->$startTimeField)) {
                        $equipmentAssignments[] = [
                            'eqp_no' => $lot->$eqpField,
                            'start_time' => $lot->$startTimeField,
                            'ng_percent' => $lot->$ngPercentField ?: 0
                        ];
                    }
                }
                
                if (empty($equipmentAssignments)) {
                    $skippedCount++;
                    continue;
                }
                
                // Get equipment data for calculations
                $profiles = [];
                foreach ($equipmentAssignments as $assignment) {
                    $equipment = DB::table('equipment')
                        ->where('eqp_no', $assignment['eqp_no'])
                        ->first();
                    
                    if (!$equipment || !$equipment->oee_capa) {
                        continue;
                    }
                    
                    $dailyCapacity = floatval($equipment->oee_capa);
                    if ($dailyCapacity <= 0) continue;
                    
                    $ngPercent = max(0, min(100, floatval($assignment['ng_percent'])));
                    $grossRatePerMinute = $dailyCapacity / 1440;
                    $netGoodRatePerMinute = $grossRatePerMinute * (1 - $ngPercent / 100);
                    
                    if ($netGoodRatePerMinute <= 0) continue;
                    
                    $startTime = Carbon::parse($assignment['start_time']);
                    
                    $profiles[] = [
                        'eqp_no' => $assignment['eqp_no'],
                        'start_time' => $startTime,
                        'rate_per_minute' => $netGoodRatePerMinute,
                        'oee_capacity' => $dailyCapacity,
                        'ng_percent' => $ngPercent,
                    ];
                }
                
                if (empty($profiles)) {
                    $skippedCount++;
                    continue;
                }
                
                // Sort profiles by start time
                usort($profiles, function($a, $b) {
                    return $a['start_time']->timestamp <=> $b['start_time']->timestamp;
                });
                
                $earliestStart = $profiles[0]['start_time'];
                $totalRate = array_sum(array_column($profiles, 'rate_per_minute'));
                
                if ($totalRate <= 0) {
                    $skippedCount++;
                    continue;
                }
                
                // CORRECTED LOGIC: Calculate completion time based on earliest start and total rate
                $baselineMinutes = $lot->lot_qty / $totalRate;
                $estimatedEndtime = $earliestStart->copy()->addMinutes($baselineMinutes);
                
                // Update the lot with the new estimated endtime
                DB::table('endtime')
                    ->where('id', $lot->id)
                    ->update([
                        'est_endtime' => $estimatedEndtime,
                        'updated_at' => now()
                    ]);
                
                $recalculatedCount++;
                
            } catch (\Exception $e) {
                // Log error but continue with other lots
                \Log::error('Migration: Failed to recalculate endtime for lot', [
                    'lot_id' => $lot->lot_id ?? $lot->id,
                    'error' => $e->getMessage()
                ]);
                $skippedCount++;
            }
        }
        
        // Log the migration results
        \Log::info('Endtime recalculation migration completed', [
            'total_lots' => count($lots),
            'recalculated' => $recalculatedCount,
            'skipped' => $skippedCount
        ]);
        
        echo "Endtime recalculation completed:\n";
        echo "- Total lots: " . count($lots) . "\n";
        echo "- Recalculated: $recalculatedCount\n";
        echo "- Skipped: $skippedCount\n";
    }

    /**
     * Reverse the migration.
     */
    public function down()
    {
        // This migration cannot be easily reversed as it overwrites calculated data
        // Manual restoration would be required from a backup if needed
        echo "This migration cannot be automatically reversed.\n";
        echo "If you need to restore original endtimes, please restore from a database backup.\n";
    }
};