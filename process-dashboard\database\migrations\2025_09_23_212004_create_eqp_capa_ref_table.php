<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('eqp_capa_ref', function (Blueprint $table) {
            $table->id();
            $table->string('work_type');
            $table->string('size');
            $table->decimal('oee', 8, 2);
            $table->decimal('passing', 8, 2);
            $table->decimal('yield', 8, 2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('eqp_capa_ref');
    }
};
