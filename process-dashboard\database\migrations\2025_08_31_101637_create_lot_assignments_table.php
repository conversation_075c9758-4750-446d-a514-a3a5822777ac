<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lot_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lot_request_id')->constrained()->onDelete('cascade');
            $table->string('lot_id'); // The lot ID from UpdateWip table
            $table->string('lot_code'); // The lot code for reference
            $table->string('equipment_number'); // Which equipment this lot is assigned to
            $table->string('equipment_code'); // Equipment code for reference
            $table->integer('lot_quantity'); // Quantity of this lot
            $table->datetime('assigned_date');
            $table->foreignId('assigned_by')->constrained('users')->onDelete('cascade'); // Who made the assignment
            $table->timestamps();
            
            // Ensure a lot can only be assigned to one request
            $table->unique('lot_id');
            
            // Index for performance
            $table->index(['lot_request_id', 'lot_code']);
            $table->index(['equipment_number', 'equipment_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lot_assignments');
    }
};
