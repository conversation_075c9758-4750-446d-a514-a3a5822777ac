# Process Dashboard - Production Management System

<p align="center">
    <img src="https://img.shields.io/badge/Laravel-12.x-red.svg" alt="Laravel Version">
    <img src="https://img.shields.io/badge/PHP-8.2+-blue.svg" alt="PHP Version">
    <img src="https://img.shields.io/badge/Bootstrap-5.3.7-purple.svg" alt="Bootstrap Version">
    <img src="https://img.shields.io/badge/Tailwind-3.1+-teal.svg" alt="Tailwind CSS">
    <img src="https://img.shields.io/badge/Status-Production-brightgreen.svg" alt="Project Status">
    <img src="https://img.shields.io/badge/Security-Enhanced-blue.svg" alt="Security">
</p>

## 📋 About Process Dashboard

A comprehensive Laravel-based production management system designed for manufacturing environments. This application provides real-time monitoring, analytics, and management tools for Work-in-Progress (WIP), lot requests, equipment, end-time processes, and advanced system administration with enhanced security features.

## 🚀 Core Features

### 📊 **Dashboards & Analytics**
- **WIP Analytics Dashboard** - Inventory levels, process flow, bottleneck identification, and throughput metrics
- **Lot Request Analytics** - Request volume trends, processing time analytics, and equipment utilization
- **Machine Allocation Dashboard** - Equipment assignment and utilization tracking
- **Endline Reporting** - Production completion tracking and analysis
- **Financial Analytics** - Revenue tracking and cost analysis
- **Customer Analytics** - Customer performance metrics and insights
- **Product Analytics** - Product performance and lifecycle analytics

### 🏭 **Production Management**
- **Equipment Management** - Track, manage, and maintain production equipment
- **Lot Request System** - Create, edit, and manage production lot requests
- **Order Processing** - Complete order lifecycle management
- **Work-in-Progress (WIP)** - Real-time WIP tracking and updates
- **End-time Management** - Production completion time tracking with advanced forecasting

### 📈 **Advanced Endtime Forecasting System**
- **Advanced NG% Calculation**: Equipment-specific NG (No Good) percentage handling with 3-step calculation method
- **Parallel Processing Support**: Multiple equipment with different start times and capacities
- **Precise Time Calculation**: Decimal hour handling for accurate endtime predictions down to the minute
- **Real-time Recalculation**: Dynamic endtime updates as equipment assignments and NG% values change
- **Interactive Equipment Assignment**: Up to 10 equipment units with drag-and-drop interface
- **Visual Calculation Breakdown**: Step-by-step display of calculation logic and results
- **Equipment Capacity Display**: Real-time capacity calculations based on OEE, speed, and operation time

### 📊 **WIP Management**
- **Real-time WIP Summary**: Grouped analysis by lot size, code, equipment type, and work type
- **Advanced Filtering**: Multi-dimensional filtering (Hold status, Lot size, Quantity class, Work type, WIP status, Lipas, Auto)
- **Interactive Sorting**: Client-side table sorting for both summary and detail views
- **Detailed Lot View**: Modal with individual lot details including stagnation analysis
- **Print Functionality**: Individual lot report generation with professional formatting
- **Average TAT Tracking**: Turnaround time analysis and monitoring

### 🔧 **Equipment Management**
- **Equipment Master Data**: Comprehensive equipment specifications and configurations
- **Ongoing Lot Tracking**: Real-time tracking of which lot is currently on each equipment
- **CSV Import**: Bulk equipment data import from CSV files
- **Equipment Classification**: By type, class, maker, feeder type, and operational parameters

### 📈 **Reporting & Analytics**
- **Inventory Reports** - Stock levels and inventory analytics
- **Sales Reports** - Revenue and sales performance tracking  
- **User Reports** - System usage and user activity analytics
- **Custom Analytics** - Customer, financial, and product analytics

### 👥 **User Management**
- **Role-based Access**: Admin and User roles with appropriate permissions
- **Excel Import**: Bulk user import from Excel files
- **Authentication**: Secure login system with employee number integration
- **Admin Controls**: Equipment and WIP data management restricted to admins
- **User Profile Management**: Individual user settings and preferences

### 💾 **Data Import & Export**
- **CSV Support**: Equipment and WIP data import from CSV files
- **Excel Integration**: User data import from Excel files
- **Data Validation**: Robust validation and error handling for imports
- **BOM Handling**: Proper handling of CSV files with BOM encoding

## 🛡️ Advanced Security Features

### 🔐 **Enhanced Security Settings**
- **Password Policy Management**
  - Configurable minimum length (6-32 characters)
  - Uppercase, lowercase, numbers, and symbols requirements
  - Password strength validation

- **Login Security**
  - Configurable login attempt limits (3-10 attempts)
  - Account lockout duration (1-60 minutes)
  - Failed login attempt tracking

- **Two-Factor Authentication (2FA)**
  - Support for App, SMS, and Email-based 2FA
  - Configurable 2FA methods per user preference
  - Enhanced account security

### 🔒 **Session & Access Control**
- **Advanced Session Management**
  - Configurable session timeout (15 minutes to 24 hours)
  - Maximum concurrent sessions per user
  - Force logout on password change option
  - Remember me functionality toggle

- **IP Security**
  - IP whitelisting for enhanced access control
  - Configurable IP access restrictions
  - Geographic access controls

### 📋 **Security Monitoring**
- **Active Session Management**
  - Real-time active session monitoring
  - Session termination capabilities
  - Device and location tracking

- **Security Audit Logs**
  - Comprehensive security event logging
  - Login/logout activity tracking
  - Failed authentication attempts
  - Configuration change logs

## ⚙️ System Settings & Configuration

### 🎛️ **General Settings**
- **Application Configuration**
  - App name and description management
  - Contact email configuration
  - Timezone settings (global and per-user)
  - Unit system configuration (PCS, K PCS, M PCS)

### 📧 **Email Configuration**
- **SMTP Settings**
  - Mail server configuration
  - SMTP authentication setup
  - Email encryption settings
  - Email notification management

### 🖥️ **System Settings**
- **Performance Configuration**
  - Maintenance mode toggle
  - Debug mode management
  - Cache system controls
  - Session lifetime configuration

- **PHP Runtime Settings**
  - Memory limit configuration (128M - 2G)
  - Max execution time (30 - 600 seconds)
  - Upload size limits (1-100 MB)
  - Log level management

### 🗂️ **Automated Log Management**
- **Auto Clear Logs Feature**
  - Automatic log clearing based on file size or schedule
  - Configurable size triggers (1-1000 MB)
  - Schedule-based clearing (daily, weekly, monthly)
  - Automatic backup creation before clearing
  - Smart backup retention (keeps 5 newest)
  - Comprehensive logging of all auto-clear activities

## 🗃️ Data Management

### 💾 **Backup System**
- **Automated Data Backups**
  - Scheduled database backups
  - File system backup management
  - Backup restoration capabilities
  - Backup verification and integrity checks

### 📁 **File Management**
- **Log Management**
  - Download system logs
  - Clear log files with confirmation
  - Automated log rotation
  - Log size monitoring

- **Cache Management**
  - Application cache clearing
  - Configuration cache management
  - View cache optimization
  - Route cache management

## 🛠️ Technology Stack

- **Backend**: Laravel 12.x (PHP 8.2+)
- **Frontend**: Bootstrap 5.3.7 with FontAwesome icons
- **CSS Framework**: Tailwind CSS 3.1+ with Alpine.js 3.4+
- **Database**: MySQL/MariaDB
- **Authentication**: Laravel Breeze with custom employee-based authentication
- **Build Tool**: Vite 7.0+ for asset compilation
- **Charts**: ApexCharts 5.3+ and Chart.js 4.5+
- **UI Libraries**: SweetAlert2 11.22+ for notifications
- **JavaScript**: Vanilla JS with Alpine.js for reactive components

## 🏗️ Technical Architecture

### 🔧 **Laravel Framework**
- **Version**: Laravel 12.x
- **PHP Version**: 8.2+
- **Authentication**: Laravel Breeze
- **Frontend**: Blade Templates with Bootstrap 5

### 🗄️ **Database**
- **Primary Database**: Configured via Laravel's database system
- **Migrations**: Complete database schema management
- **Seeders**: Initial data population
- **Factories**: Test data generation

### 📂 **Project Structure**
```
process-dashboard/
├── app/
│   ├── Console/Commands/
│   │   ├── AutoClearLogs.php
│   │   ├── RecalculateEndtimes.php
│   │   └── TestRolePermissions.php
│   ├── Http/Controllers/
│   │   ├── Auth/ (Authentication Controllers)
│   │   ├── AnalyticsController.php
│   │   ├── DashboardController.php
│   │   ├── DataManagementController.php
│   │   ├── EndtimeController.php
│   │   ├── EquipmentController.php
│   │   ├── LotRequestController.php
│   │   ├── OrderController.php
│   │   ├── ReportController.php
│   │   ├── SettingsController.php
│   │   ├── SubmittedController.php
│   │   ├── UpdateWipController.php
│   │   └── UserManagementController.php
│   ├── Helpers/
│   │   └── SettingsHelper.php
│   └── Auth/
│       └── EmpNoUserProvider.php
├── resources/views/
│   ├── auth/ (Authentication Views)
│   ├── components/ (Reusable UI Components)
│   ├── dashboards/ (Dashboard Views)
│   ├── equipment/ (Equipment Management Views)
│   ├── endtime/ (End-time Management Views)
│   ├── layouts/ (Layout Templates)
│   ├── lot-requests/ (Lot Request Views)
│   ├── management/ (Admin Management Views)
│   │   ├── analytics/ (Analytics Views)
│   │   ├── data/ (Data Management Views)
│   │   ├── settings/ (Settings Views)
│   │   │   └── security/ (Security Settings)
│   │   └── users/ (User Management Views)
│   ├── orders/ (Order Management Views)
│   ├── profile/ (User Profile Views)
│   ├── reports/ (Report Views)
│   └── submitted/ (Submitted Forms Views)
```

### 📋 **Database Schema**

#### Core Tables
- **users**: User management with role-based access
- **equipment**: Equipment master data with OEE, speed, and operation time for capacity calculations
- **endtime**: Lot endtime forecasting with equipment assignments and NG% tracking
- **updatewip**: Work-in-process records with stagnation analysis
- **lot_requests**: Lot request management system
- **orders**: Order management system

#### Key Endtime Table Schema
```sql
-- Equipment assignments (up to 10 units)
eqp_1 to eqp_10 VARCHAR(255)           -- Equipment numbers
start_time_1 to start_time_10 DATETIME  -- Start times for each equipment
ng_percent_1 to ng_percent_10 DECIMAL   -- NG percentages for rework calculation

-- Calculation results
est_endtime DATETIME                    -- Calculated end time
lot_qty INTEGER                         -- Lot quantity
status VARCHAR(50)                      -- Processing status
```

## 🎨 UI/UX Improvements

### Endtime Forecasting Interface
- **Advanced Modal Design**: 
  - Equipment assignment interface with real-time capacity display
  - NG% input fields with instant calculation updates
  - Step-by-step calculation breakdown with visual indicators
  - Equipment search and selection with autocomplete

- **Calculation Display**:
  - 3-step calculation breakdown (Initial → NG → Final)
  - Equipment-specific NG piece contributions
  - Real-time endtime updates with relative time display
  - Visual progress indicators and capacity utilization charts

### WIP Management Interface
- **Enhanced Modal Design**: 
  - Fixed width (1200px, 90% max-width) and height (80vh)
  - Scrollable content area with proper overflow handling
  - Professional header and footer sections

- **Table Enhancements**:
  - Row numbering in modal details
  - Individual lot print functionality
  - Interactive column sorting (both summary and details)
  - Improved Code column styling with bold text and background

- **Filter Integration**:
  - Fixed data mismatch between summary and modal details
  - Consistent filter application across all views
  - Always-visible filter section for quick access

- **Visual Indicators**:
  - Color-coded TAT badges (green < 15 days, yellow 15-30 days, red > 30 days)
  - Sorting arrows with different colors (green for summary, yellow for modal)
  - Professional gradients and modern card layouts

## 🚀 Installation & Setup

### 📋 **Prerequisites**
- PHP 8.2 or higher
- Composer
- Node.js and NPM
- Web server (Apache/Nginx)
- Database server (MySQL/PostgreSQL)
- Windows Server/IIS or Apache/Nginx

### ⚡ **Quick Start**
1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd process-dashboard
   ```

2. **Install Dependencies**
   ```bash
   composer install
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Database Setup**
   ```bash
   php artisan migrate
   php artisan db:seed
   ```

5. **Build Assets**
   ```bash
   npm run build
   ```

6. **Start Development Server**
   ```bash
   php artisan serve
   # or use the custom composer script
   composer run serve
   ```

### 🔧 **Production Deployment**
1. **Optimize Application**
   ```bash
   composer install --optimize-autoloader --no-dev
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

2. **Set Up Task Scheduler**
   Add to crontab:
   ```bash
   * * * * * php /path/to/artisan schedule:run >> /dev/null 2>&1
   ```

3. **Configure Auto Clear Logs**
   In `app/Console/Kernel.php`:
   ```php
   protected function schedule(Schedule $schedule)
   {
       $schedule->command('logs:auto-clear')->hourly();
   }
   ```

**Note for Windows/IIS**: Ensure proper permissions are set for the storage and bootstrap/cache directories.

## 🎯 Configuration Guide

### ⚙️ **System Settings Configuration**
1. Navigate to `/management/settings`
2. Configure each section according to your environment:
   - **General Settings**: App name, timezone, contact info
   - **Email Settings**: SMTP configuration for notifications  
   - **System Settings**: Performance, logging, and automation
   - **Security Settings**: Authentication, 2FA, and access controls

### 🔒 **Security Configuration**
1. **Enable Two-Factor Authentication**
   - Go to Security Settings
   - Enable 2FA and select preferred method
   - Configure backup codes

2. **Set Up IP Whitelisting**
   - Configure allowed IP addresses
   - Set geographic restrictions if needed
   - Test access from allowed locations

3. **Configure Session Security**
   - Set appropriate session timeout
   - Limit concurrent sessions
   - Enable logout on password change

### 🗂️ **Auto Clear Logs Setup**
1. **Enable Auto Clear**
   - Navigate to System Settings
   - Toggle "Enable Auto Clear Logs"
   - Choose trigger type (Size/Schedule/Both)

2. **Configure Triggers**
   - Set maximum log size (recommended: 10-50 MB)
   - Choose clearing schedule (daily/weekly/monthly)
   - Save configuration

3. **Verify Operation**
   - Run manual check: `php artisan logs:auto-clear`
   - Monitor backup creation
   - Check audit logs for activities

## 🔍 Monitoring & Maintenance

### 📊 **System Health**
- **Performance Monitoring**: Built-in system info display
- **Resource Usage**: Memory and execution time tracking
- **Log Analysis**: Automated log management and monitoring
- **Cache Management**: Automatic cache optimization

### 🛠️ **Maintenance Tasks**
- **Regular Backups**: Automated database and file backups
- **Log Rotation**: Automated log clearing and archival
- **Cache Clearing**: Performance optimization routines
- **Security Audits**: Regular security log reviews

## 📊 Data Management

### CSV File Formats

#### equipment.csv
Required columns: `eqp_no`, `eqp_line`, `eqp_area`, `eqp_type`, `eqp_class`, `eqp_maker`, `feeder_type`, `lot_size`, `work_type`, `lot_type`, `lotqty_alloc`, `eqp_oee`, `eqp_speed`, `operation_time`, `eqp_code`, `modified_by`, `ongoing_lot`

#### updatewip.csv
WIP data with lot tracking, quantities, TAT, and location information

### Default Admin User
- **Employee No**: 21278703 (Gilbert Hapita)
- **Employee Name**: HAPITA, GILBERT HIBE
- **Password**: password (default - should be changed after first login)
- **Role**: ADMIN
- **Position**: Specialist - Production Management

## 🆘 Troubleshooting

### 🔧 **Common Issues**
1. **Performance Issues**
   - Check system settings for memory limits
   - Clear application cache
   - Run Laravel optimization

2. **Log Management**
   - Verify auto-clear configuration
   - Check disk space availability
   - Review backup retention settings

3. **Authentication Problems**
   - Check security settings
   - Verify 2FA configuration
   - Review session settings

### 📞 **Support**
- Check system logs in `/storage/logs/`
- Review configuration in Settings dashboard
- Use built-in system health check tools
- Consult Laravel documentation for framework issues

## 🎯 Usage

### WIP Management Workflow
1. **Apply Filters**: Use the filter section to narrow down WIP data
2. **Sort Data**: Click column headers to sort by different criteria
3. **View Details**: Click the eye icon to see individual lot details
4. **Print Reports**: Use the print button for individual lot reports
5. **Analyze TAT**: Monitor average turnaround times and identify bottlenecks

### Equipment Management
- Equipment data automatically imported from CSV
- Track ongoing lots on each piece of equipment
- Monitor equipment utilization and allocation

## 🔒 Security

- Role-based access control (Admin/User)
- Data modification restricted to Admin users
- Secure authentication with Laravel Breeze
- Input validation and sanitization

## 🧪 Testing

Run the test suite:
```bash
php artisan test
```

## 📈 Performance

- Optimized database queries with proper indexing
- Client-side sorting for improved responsiveness
- Efficient AJAX loading for modal details
- Responsive design for various screen sizes

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 Changelog

### 2025-09-09 - System Update & Maintenance
- 📊 **Dashboard Enhancement**: Real-time auto-refresh functionality with configurable intervals
- 🎯 **Production Overview**: Advanced capacity tracking with equipment count monitoring
- 🔧 **Chart Integration**: ApexCharts and Chart.js integration for manufacturing analytics
- 🎨 **UI Improvements**: Enhanced header controls with time, shift, and cutoff filters
- 🔐 **Permission System**: Comprehensive role-based access control implementation
- 📱 **Responsive Design**: Improved mobile and tablet compatibility
- 🚀 **Performance**: Optimized AJAX data loading and real-time updates
- 🗃️ **Database**: Consolidated endtime table with enhanced performance indexes

### 2025-09-04 - Endtime Forecasting System v2.1
- ✨ **Critical Bug Fix**: Fixed JavaScript decimal hour truncation in time calculations
- ✨ **Enhanced NG% Logic**: Implemented equipment-based NG piece calculation method
- ✨ **Real-time Recalculation**: View details now shows live recalculated values
- ✨ **Precision Improvements**: Endtime accuracy down to the minute
- 🐛 **Time Sync Fix**: Resolved 41-minute discrepancy in modal vs table display
- 📊 **Calculation Breakdown**: Added step-by-step NG% calculation display
- 🔍 **Equipment Search**: Enhanced equipment selection with autocomplete

### 2025-09-02 - Endtime Forecasting System v2.0
- ✨ **New Feature**: Complete endtime forecasting system with NG% support
- ✨ **Parallel Processing**: Multiple equipment with different start times
- ✨ **Equipment Assignment**: Up to 10 equipment units per lot
- ✨ **NG% Calculation**: Quality rework impact on processing time
- 🗺️ **Database Schema**: Added endtime table with equipment assignments
- 🎨 **Modern UI**: Professional modal design with real-time updates

### 2025-08-29 - Major UI/UX Enhancements
- ✨ Enhanced modal design with fixed sizing and scrolling
- ✨ Added row numbering and individual lot print functionality
- ✨ Implemented interactive table sorting for all views
- ✨ Fixed filter consistency between summary and detail views
- ✨ Improved Code column styling with better visibility
- 🗺️ Added `ongoing_lot` column to equipment table
- 🎨 Enhanced visual indicators and professional styling
- 🐛 Fixed data mismatch issues between views

### Previous Updates
- 🏗️ Initial WIP management system implementation
- 🔐 User authentication and role management
- 📊 Equipment and WIP data import capabilities
- 🎨 Bootstrap-based responsive UI

## 🐛 Known Issues

None at this time. Please report any issues through the issue tracker.

## 📞 Support

For technical support or questions about the Process Dashboard System, please contact the development team.

---

**Built with Laravel 11** | **Powered by Bootstrap 5** | **Enhanced with FontAwesome**
