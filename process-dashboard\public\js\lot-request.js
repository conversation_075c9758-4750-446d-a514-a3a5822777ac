/**
 * Lot Request Analytics Dashboard JavaScript
 * Handles charts, real-time updates, and interactive features
 */

class LotRequestAnalytics {
    constructor() {
        this.charts = {};
        this.autoRefreshInterval = null;
        this.websocket = null;
        this.isInitialized = false;
        this.currentFilters = {
            period: 'week',
            status: 'all',
            equipment: 'all'
        };
        
        // Chart color scheme
        this.colors = {
            primary: '#667eea',
            secondary: '#764ba2',
            success: '#32d484',
            info: '#17a2b8',
            warning: '#f6c343',
            danger: '#e74c3c',
            light: '#f8f9ff',
            dark: '#2c3e50'
        };

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    /**
     * Initialize the dashboard
     */
    async init() {
        try {
            console.log('Initializing Lot Request Analytics Dashboard...');
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Initialize charts
            await this.initializeCharts();
            
            // Load initial data
            await this.loadDashboardData();
            
            // Start real-time updates
            this.startRealtimeUpdates();
            
            // Setup auto-refresh
            this.setupAutoRefresh();
            
            this.isInitialized = true;
            console.log('Dashboard initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize dashboard:', error);
            this.showError('Failed to initialize dashboard. Please refresh the page.');
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Period filter change
        const periodFilter = document.getElementById('periodFilter');
        if (periodFilter) {
            periodFilter.addEventListener('change', (e) => {
                this.currentFilters.period = e.target.value;
                this.updateDashboardData();
            });
        }

        // Status filter change
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.currentFilters.status = e.target.value;
                this.updateDashboardData();
            });
        }

        // Auto refresh toggle
        const autoRefreshToggle = document.getElementById('autoRefreshToggle');
        if (autoRefreshToggle) {
            autoRefreshToggle.addEventListener('change', () => {
                this.toggleAutoRefresh();
            });
        }

        // Volume chart period buttons
        document.querySelectorAll('input[name="volumeChart"]').forEach(input => {
            input.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.updateVolumeChart(e.target.id.replace('volume', '').toLowerCase());
                }
            });
        });

        // Equipment period selector
        const equipmentPeriod = document.getElementById('equipmentPeriod');
        if (equipmentPeriod) {
            equipmentPeriod.addEventListener('change', (e) => {
                this.updateEquipmentChart(e.target.value);
            });
        }

        // Window resize handler
        window.addEventListener('resize', this.debounce(() => {
            this.resizeCharts();
        }, 250));
    }

    /**
     * Initialize all charts
     */
    async initializeCharts() {
        console.log('Initializing charts...');

        // Initialize Volume Trend Chart
        await this.initVolumeChart();
        
        // Initialize Status Distribution Chart
        await this.initStatusChart();
        
        // Initialize Equipment Utilization Chart
        await this.initEquipmentChart();
        
        // Initialize Processing Time Chart
        await this.initProcessingTimeChart();
        
        // Initialize Area Station Chart
        await this.initAreaStationChart();

        console.log('All charts initialized');
    }

    /**
     * Initialize Volume Trend Chart (Line Chart)
     */
    async initVolumeChart() {
        const canvas = document.getElementById('volumeTrendChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        this.charts.volumeTrend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Total Requests',
                    data: [],
                    borderColor: this.colors.primary,
                    backgroundColor: this.colors.primary + '20',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    pointBackgroundColor: this.colors.primary,
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                }, {
                    label: 'Completed',
                    data: [],
                    borderColor: this.colors.success,
                    backgroundColor: this.colors.success + '20',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    pointBackgroundColor: this.colors.success
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                aspectRatio: 2.5,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: this.colors.primary,
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: true,
                            color: 'rgba(0,0,0,0.05)'
                        },
                        ticks: {
                            color: this.colors.dark,
                            font: {
                                size: 12
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0,0,0,0.05)'
                        },
                        ticks: {
                            color: this.colors.dark,
                            font: {
                                size: 12
                            }
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                },
                elements: {
                    point: {
                        hoverRadius: 8
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }

    /**
     * Initialize Status Distribution Chart (Doughnut Chart)
     */
    async initStatusChart() {
        const canvas = document.getElementById('statusDistributionChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        this.charts.statusDistribution = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Pending', 'In Process', 'Completed', 'Cancelled'],
                datasets: [{
                    data: [0, 0, 0, 0],
                    backgroundColor: [
                        this.colors.warning,
                        this.colors.info,
                        this.colors.success,
                        this.colors.danger
                    ],
                    borderColor: '#ffffff',
                    borderWidth: 3,
                    hoverBorderWidth: 4,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                aspectRatio: 1,
                cutout: '60%',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: this.colors.primary,
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    animateScale: true,
                    duration: 1200,
                    easing: 'easeOutBounce'
                },
                onHover: (event, elements) => {
                    event.native.target.style.cursor = elements.length ? 'pointer' : 'default';
                }
            }
        });
    }

    /**
     * Initialize Equipment Utilization Chart (Horizontal Bar Chart)
     */
    async initEquipmentChart() {
        const canvas = document.getElementById('equipmentUtilizationChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        this.charts.equipmentUtilization = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [{
                    label: 'Request Count',
                    data: [],
                    backgroundColor: [
                        this.colors.primary + 'CC',
                        this.colors.success + 'CC',
                        this.colors.info + 'CC',
                        this.colors.warning + 'CC',
                        this.colors.danger + 'CC',
                        this.colors.secondary + 'CC'
                    ],
                    borderColor: [
                        this.colors.primary,
                        this.colors.success,
                        this.colors.info,
                        this.colors.warning,
                        this.colors.danger,
                        this.colors.secondary
                    ],
                    borderWidth: 2,
                    borderRadius: 6,
                    borderSkipped: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                aspectRatio: 1.5,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: this.colors.primary,
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            title: function(tooltipItems) {
                                return tooltipItems[0].label;
                            },
                            label: function(context) {
                                return `Requests: ${context.parsed.x}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0,0,0,0.05)'
                        },
                        ticks: {
                            color: this.colors.dark,
                            font: {
                                size: 12
                            }
                        }
                    },
                    y: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: this.colors.dark,
                            font: {
                                size: 12
                            }
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }

    /**
     * Initialize Processing Time Chart (Line Chart)
     */
    async initProcessingTimeChart() {
        const canvas = document.getElementById('processingTimeChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        this.charts.processingTime = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Average Processing Time (hours)',
                    data: [],
                    borderColor: this.colors.info,
                    backgroundColor: this.colors.info + '20',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    pointBackgroundColor: this.colors.info,
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                aspectRatio: 2,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: this.colors.info,
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const hours = context.parsed.y;
                                const days = Math.floor(hours / 24);
                                const remainingHours = hours % 24;
                                
                                if (days > 0) {
                                    return `${days}d ${remainingHours.toFixed(1)}h`;
                                } else {
                                    return `${remainingHours.toFixed(1)} hours`;
                                }
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: true,
                            color: 'rgba(0,0,0,0.05)'
                        },
                        ticks: {
                            color: this.colors.dark,
                            font: {
                                size: 12
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0,0,0,0.05)'
                        },
                        ticks: {
                            color: this.colors.dark,
                            font: {
                                size: 12
                            },
                            callback: function(value) {
                                return value + 'h';
                            }
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }

    /**
     * Initialize Area Station Chart (Radar Chart)
     */
    async initAreaStationChart() {
        const canvas = document.getElementById('areaStationChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        this.charts.areaStation = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: [],
                datasets: [{
                    label: 'Request Count',
                    data: [],
                    borderColor: this.colors.secondary,
                    backgroundColor: this.colors.secondary + '20',
                    borderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    pointBackgroundColor: this.colors.secondary,
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                aspectRatio: 1,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: this.colors.secondary,
                        borderWidth: 1,
                        cornerRadius: 8
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        },
                        angleLines: {
                            color: 'rgba(0,0,0,0.1)'
                        },
                        pointLabels: {
                            color: this.colors.dark,
                            font: {
                                size: 12
                            }
                        },
                        ticks: {
                            color: this.colors.dark,
                            backdropColor: 'rgba(255,255,255,0.8)',
                            font: {
                                size: 10
                            }
                        }
                    }
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }

    /**
     * Load dashboard data
     */
    async loadDashboardData() {
        try {
            this.showLoading(true);
            
            // Simulate API call - replace with actual endpoint
            const response = await this.fetchDashboardData();
            
            // Update KPI cards
            this.updateKPICards(response.kpis);
            
            // Update charts with new data
            this.updateVolumeChartData(response.volumeTrend);
            this.updateStatusChartData(response.statusDistribution);
            this.updateEquipmentChartData(response.equipmentUtilization);
            this.updateProcessingTimeChartData(response.processingTime);
            this.updateAreaStationChartData(response.areaStations);
            
            // Update activity feed
            this.updateActivityFeed(response.activities);
            
            // Update recent requests table
            this.updateRecentRequestsTable(response.recentRequests);
            
            this.showLoading(false);
            
        } catch (error) {
            console.error('Failed to load dashboard data:', error);
            this.showError('Failed to load dashboard data.');
            this.showLoading(false);
        }
    }

    /**
     * Fetch dashboard data from API
     */
    async fetchDashboardData() {
        // Mock data for demonstration
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    kpis: {
                        totalRequests: Math.floor(Math.random() * 1000) + 500,
                        totalEquipment: Math.floor(Math.random() * 100) + 50,
                        totalLots: Math.floor(Math.random() * 10000) + 5000,
                        avgProcessingTime: (Math.random() * 24 + 1).toFixed(1) + 'h'
                    },
                    volumeTrend: {
                        labels: this.generateDateLabels(7),
                        datasets: [
                            this.generateRandomData(7, 20, 80),
                            this.generateRandomData(7, 10, 60)
                        ]
                    },
                    statusDistribution: [
                        Math.floor(Math.random() * 50) + 10,
                        Math.floor(Math.random() * 30) + 5,
                        Math.floor(Math.random() * 100) + 50,
                        Math.floor(Math.random() * 20) + 2
                    ],
                    equipmentUtilization: {
                        labels: ['EQP-001', 'EQP-002', 'EQP-003', 'EQP-004', 'EQP-005'],
                        data: this.generateRandomData(5, 10, 50)
                    },
                    processingTime: {
                        labels: this.generateDateLabels(14),
                        data: this.generateRandomData(14, 2, 48, 1)
                    },
                    areaStations: {
                        labels: ['Area A', 'Area B', 'Area C', 'Area D', 'Area E'],
                        data: this.generateRandomData(5, 5, 25)
                    },
                    activities: this.generateMockActivities(5),
                    recentRequests: this.generateMockRequests(8)
                });
            }, 500);
        });
    }

    /**
     * Update KPI cards
     */
    updateKPICards(kpis) {
        const elements = {
            totalRequests: document.getElementById('totalRequests'),
            totalEquipment: document.getElementById('totalEquipment'),
            totalLots: document.getElementById('totalLots'),
            avgProcessingTime: document.getElementById('avgProcessingTime')
        };

        Object.keys(elements).forEach(key => {
            if (elements[key] && kpis[key] !== undefined) {
                this.animateNumber(elements[key], kpis[key]);
            }
        });

        // Update navigation counts
        const navElements = {
            totalRequestsCount: document.getElementById('totalRequestsCount'),
            pendingRequestsCount: document.getElementById('pendingRequestsCount'),
            completedRequestsCount: document.getElementById('completedRequestsCount')
        };

        if (navElements.totalRequestsCount) {
            this.animateNumber(navElements.totalRequestsCount, kpis.totalRequests);
        }
    }

    /**
     * Update volume chart data
     */
    updateVolumeChartData(data) {
        if (!this.charts.volumeTrend || !data) return;
        
        this.charts.volumeTrend.data.labels = data.labels;
        this.charts.volumeTrend.data.datasets[0].data = data.datasets[0];
        this.charts.volumeTrend.data.datasets[1].data = data.datasets[1];
        this.charts.volumeTrend.update('active');
    }

    /**
     * Update status chart data
     */
    updateStatusChartData(data) {
        if (!this.charts.statusDistribution || !data) return;
        
        this.charts.statusDistribution.data.datasets[0].data = data;
        this.charts.statusDistribution.update('active');

        // Update legend counts
        const legendElements = {
            pendingCount: document.getElementById('pendingCount'),
            inProcessCount: document.getElementById('inProcessCount'),
            completedCount: document.getElementById('completedCount'),
            cancelledCount: document.getElementById('cancelledCount')
        };

        data.forEach((count, index) => {
            const elementIds = ['pendingCount', 'inProcessCount', 'completedCount', 'cancelledCount'];
            const element = document.getElementById(elementIds[index]);
            if (element) {
                this.animateNumber(element, count);
            }
        });
    }

    /**
     * Update equipment chart data
     */
    updateEquipmentChartData(data) {
        if (!this.charts.equipmentUtilization || !data) return;
        
        this.charts.equipmentUtilization.data.labels = data.labels;
        this.charts.equipmentUtilization.data.datasets[0].data = data.data;
        this.charts.equipmentUtilization.update('active');
    }

    /**
     * Update processing time chart data
     */
    updateProcessingTimeChartData(data) {
        if (!this.charts.processingTime || !data) return;
        
        this.charts.processingTime.data.labels = data.labels;
        this.charts.processingTime.data.datasets[0].data = data.data;
        this.charts.processingTime.update('active');

        // Update processing stats
        const stats = {
            fastestTime: Math.min(...data.data).toFixed(1) + 'h',
            averageTime: (data.data.reduce((a, b) => a + b, 0) / data.data.length).toFixed(1) + 'h',
            slowestTime: Math.max(...data.data).toFixed(1) + 'h'
        };

        ['fastestTime', 'averageTime', 'slowestTime'].forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                element.textContent = stats[key];
            }
        });
    }

    /**
     * Update area station chart data
     */
    updateAreaStationChartData(data) {
        if (!this.charts.areaStation || !data) return;
        
        this.charts.areaStation.data.labels = data.labels;
        this.charts.areaStation.data.datasets[0].data = data.data;
        this.charts.areaStation.update('active');
    }

    /**
     * Update activity feed
     */
    updateActivityFeed(activities) {
        const activityFeed = document.getElementById('activityFeed');
        if (!activityFeed || !activities) return;

        const activityHTML = activities.map(activity => `
            <div class="activity-item" style="animation: fadeInUp 0.4s ease-out;">
                <div class="activity-icon ${activity.type}">
                    <i class="fas fa-${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <p><strong>${activity.title}</strong> ${activity.description}</p>
                    <small>${activity.time}</small>
                </div>
            </div>
        `).join('');

        activityFeed.innerHTML = activityHTML;
    }

    /**
     * Update recent requests table
     */
    updateRecentRequestsTable(requests) {
        const tableBody = document.getElementById('recentRequestsTable');
        if (!tableBody || !requests) return;

        const tableHTML = requests.map(request => `
            <tr style="animation: fadeInUp 0.4s ease-out;">
                <td>
                    <a href="/lot-requests/${request.id}" class="request-link">
                        ${request.number}
                    </a>
                </td>
                <td>
                    <div class="user-info">
                        <strong>${request.requestor.name}</strong>
                        <small>${request.requestor.empNo}</small>
                    </div>
                </td>
                <td>
                    <span class="equipment-count">${request.equipmentCount} items</span>
                </td>
                <td>
                    <span class="lot-count">${request.lotCount}</span>
                </td>
                <td>
                    <span class="status-badge ${request.status.toLowerCase().replace(' ', '-')}">
                        ${request.status}
                    </span>
                </td>
                <td>
                    <div class="time-info">
                        <small>${request.timeAgo}</small>
                    </div>
                </td>
                <td>
                    <div class="action-buttons">
                        <a href="/lot-requests/${request.id}" class="btn btn-sm btn-outline-info" title="View">
                            <i class="fas fa-eye"></i>
                        </a>
                    </div>
                </td>
            </tr>
        `).join('');

        tableBody.innerHTML = tableHTML;
    }

    /**
     * Start real-time updates
     */
    startRealtimeUpdates() {
        // Set up periodic updates every 30 seconds
        setInterval(() => {
            this.updateDashboardData(true);
        }, 30000);
    }

    /**
     * Setup auto-refresh functionality
     */
    setupAutoRefresh() {
        const toggle = document.getElementById('autoRefreshToggle');
        if (toggle) {
            toggle.addEventListener('change', () => {
                this.toggleAutoRefresh();
            });
        }
    }

    /**
     * Toggle auto-refresh
     */
    toggleAutoRefresh() {
        const toggle = document.getElementById('autoRefreshToggle');
        if (!toggle) return;

        if (toggle.checked) {
            this.autoRefreshInterval = setInterval(() => {
                this.updateDashboardData(true);
            }, 30000);
            console.log('Auto-refresh enabled');
        } else {
            if (this.autoRefreshInterval) {
                clearInterval(this.autoRefreshInterval);
                this.autoRefreshInterval = null;
            }
            console.log('Auto-refresh disabled');
        }
    }

    /**
     * Update dashboard data (refresh)
     */
    async updateDashboardData(silent = false) {
        if (!silent) {
            this.showLoading(true);
        }

        try {
            await this.loadDashboardData();
            
            if (!silent) {
                this.showSuccess('Dashboard updated successfully');
            }
        } catch (error) {
            console.error('Failed to update dashboard:', error);
            if (!silent) {
                this.showError('Failed to update dashboard data.');
            }
        } finally {
            if (!silent) {
                this.showLoading(false);
            }
        }
    }

    /**
     * Update volume chart period
     */
    updateVolumeChart(period) {
        console.log('Updating volume chart for period:', period);
        // Implement period-specific data loading
        this.loadDashboardData();
    }

    /**
     * Update equipment chart
     */
    updateEquipmentChart(period) {
        console.log('Updating equipment chart for period:', period);
        // Implement period-specific data loading
        this.loadDashboardData();
    }

    /**
     * Export dashboard data
     */
    exportDashboardData() {
        try {
            // Collect current dashboard data
            const data = {
                timestamp: new Date().toISOString(),
                filters: this.currentFilters,
                charts: Object.keys(this.charts).map(key => ({
                    name: key,
                    data: this.charts[key].data
                }))
            };

            // Create and download file
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `lot-request-analytics-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            this.showSuccess('Dashboard data exported successfully');
        } catch (error) {
            console.error('Failed to export data:', error);
            this.showError('Failed to export dashboard data.');
        }
    }

    /**
     * Resize charts on window resize
     */
    resizeCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.resize === 'function') {
                chart.resize();
            }
        });
    }

    /**
     * Show loading state
     */
    showLoading(show) {
        const dashboard = document.querySelector('.lot-analytics-dashboard');
        if (dashboard) {
            if (show) {
                dashboard.classList.add('loading');
            } else {
                dashboard.classList.remove('loading');
            }
        }
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    /**
     * Show error message
     */
    showError(message) {
        this.showNotification(message, 'error');
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 150);
            }
        }, 5000);
    }

    /**
     * Animate number changes
     */
    animateNumber(element, targetValue, duration = 1000) {
        const startValue = parseInt(element.textContent) || 0;
        const difference = targetValue - startValue;
        const increment = difference / (duration / 16);
        let currentValue = startValue;

        const timer = setInterval(() => {
            currentValue += increment;
            
            if ((increment > 0 && currentValue >= targetValue) || 
                (increment < 0 && currentValue <= targetValue)) {
                currentValue = targetValue;
                clearInterval(timer);
            }
            
            element.textContent = Math.floor(currentValue);
        }, 16);
    }

    /**
     * Generate date labels
     */
    generateDateLabels(days) {
        const labels = [];
        for (let i = days - 1; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
        }
        return labels;
    }

    /**
     * Generate random data for charts
     */
    generateRandomData(count, min = 0, max = 100, decimals = 0) {
        const data = [];
        for (let i = 0; i < count; i++) {
            const value = Math.random() * (max - min) + min;
            data.push(decimals > 0 ? parseFloat(value.toFixed(decimals)) : Math.floor(value));
        }
        return data;
    }

    /**
     * Generate mock activities
     */
    generateMockActivities(count) {
        const activities = [
            { type: 'new-request', icon: 'plus', title: 'New Request', description: '#LR-2024-0156', time: '2 minutes ago' },
            { type: 'completed', icon: 'check', title: 'Completed', description: '#LR-2024-0155', time: '5 minutes ago' },
            { type: 'in-process', icon: 'cog', title: 'Processing', description: '#LR-2024-0154', time: '8 minutes ago' },
            { type: 'new-request', icon: 'plus', title: 'New Request', description: '#LR-2024-0153', time: '12 minutes ago' },
            { type: 'completed', icon: 'check', title: 'Completed', description: '#LR-2024-0152', time: '15 minutes ago' }
        ];
        return activities.slice(0, count);
    }

    /**
     * Generate mock requests
     */
    generateMockRequests(count) {
        const requests = [];
        const statuses = ['Pending', 'In Process', 'Completed', 'Cancelled'];
        const requestors = [
            { name: 'John Smith', empNo: 'EMP001' },
            { name: 'Jane Doe', empNo: 'EMP002' },
            { name: 'Mike Johnson', empNo: 'EMP003' },
            { name: 'Sarah Wilson', empNo: 'EMP004' }
        ];

        for (let i = 0; i < count; i++) {
            requests.push({
                id: 1000 + i,
                number: `LR-2024-${(1000 + i).toString().padStart(4, '0')}`,
                requestor: requestors[Math.floor(Math.random() * requestors.length)],
                equipmentCount: Math.floor(Math.random() * 5) + 1,
                lotCount: Math.floor(Math.random() * 100) + 10,
                status: statuses[Math.floor(Math.random() * statuses.length)],
                timeAgo: `${Math.floor(Math.random() * 60) + 1} minutes ago`
            });
        }
        return requests;
    }

    /**
     * Debounce function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Global functions for HTML event handlers
window.updateDashboardData = function() {
    if (window.lotRequestDashboard) {
        window.lotRequestDashboard.updateDashboardData();
    }
};

window.toggleAutoRefresh = function() {
    if (window.lotRequestDashboard) {
        window.lotRequestDashboard.toggleAutoRefresh();
    }
};

window.exportDashboardData = function() {
    if (window.lotRequestDashboard) {
        window.lotRequestDashboard.exportDashboardData();
    }
};

window.updateEquipmentChart = function() {
    if (window.lotRequestDashboard) {
        const period = document.getElementById('equipmentPeriod').value;
        window.lotRequestDashboard.updateEquipmentChart(period);
    }
};

// Initialize dashboard when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.lotRequestDashboard = new LotRequestAnalytics();
    });
} else {
    window.lotRequestDashboard = new LotRequestAnalytics();
}