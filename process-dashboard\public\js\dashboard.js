// ==================== DASHBOARD SPECIFIC FUNCTIONS ====================

// Make Chart.js and ApexCharts globally available (loaded externally)
if (typeof Chart !== "undefined") {
    window.Chart = Chart;
}
if (typeof ApexCharts !== "undefined") {
    window.ApexCharts = ApexCharts;
}

/**
 * Production Dashboard Functions
 * Handles auto refresh, shift/cutoff detection, and data updates
 */
class ProductionDashboard {
    constructor() {
        this.autoRefreshInterval = null;
        this.chartUpdateThrottle = null;
        this.lastChartUpdate = 0;
        this.chartUpdateDelay = 1000; // 1 second throttle
        this.init();
    }

    init() {
        // Only initialize if we're on the dashboard page
        if (document.getElementById("autoRefreshToggle")) {
            this.initializeDashboardFilters();
            this.handleShiftChange();
            this.initializeEquipmentStatusChart();
            this.initCharts();
            this.bindEvents();
            this.initializeEquipmentCarouselControls();
        }
    }

    bindEvents() {
        // Make functions globally available for onclick handlers
        window.toggleAutoRefresh = this.toggleAutoRefresh.bind(this);
        window.handleIntervalChange = this.handleIntervalChange.bind(this);
        window.handleShiftChange = this.handleShiftChange.bind(this);
        window.updateDashboardData = this.updateDashboardData.bind(this);
        window.resetDashboardFilters = this.resetDashboardFilters.bind(this);

        // Make equipment carousel functions globally available
        window.toggleEquipmentCarousel =
            this.toggleEquipmentCarousel.bind(this);
        window.nextEquipmentLine = this.nextEquipmentLine.bind(this);
        window.prevEquipmentLine = this.prevEquipmentLine.bind(this);

        // Make test function globally available for debugging
        window.testAreaPerformanceUpdate =
            this.testAreaPerformanceUpdate.bind(this);
    }

    /**
     * Get current shift and cutoff based on Asia/Manila timezone
     * Shift Schedule:
     * - Day Shift: 07:00 - 18:59 (12 hours)
     * - Night Shift: 19:00 - 06:59 (12 hours, spans 2 days)
     * Cutoff Schedule:
     * Day Shift cutoffs:
     *   - 1st cutoff: 07:00~11:59
     *   - 2nd cutoff: 12:00~15:59
     *   - 3rd cutoff: 16:00~18:59
     * Night Shift cutoffs:
     *   - 1st cutoff: 19:00~23:59
     *   - 2nd cutoff: 00:00~03:59
     *   - 3rd cutoff: 04:00~06:59
     */
    getCurrentShiftAndCutoff() {
        // Get current time in Asia/Manila timezone
        const now = new Date();
        const manilaTime = new Date(
            now.toLocaleString("en-US", { timeZone: "Asia/Manila" })
        );
        const hours = manilaTime.getHours();
        const minutes = manilaTime.getMinutes();

        let shift, cutoff, shiftDate;

        if (hours >= 7 && hours <= 18) {
            // Day Shift: 07:00 - 18:59
            shift = "day";
            shiftDate = manilaTime; // Same day

            if (hours >= 7 && hours <= 11) {
                cutoff = "1"; // 07:00 - 11:59
            } else if (hours >= 12 && hours <= 15) {
                cutoff = "2"; // 12:00 - 15:59
            } else {
                cutoff = "3"; // 16:00 - 18:59
            }
        } else {
            // Night Shift: 19:00 - 06:59 (spans 2 days)
            shift = "night";

            if (hours >= 19 && hours <= 23) {
                // First part of night shift (19:00 - 23:59) - same day
                shiftDate = manilaTime;
                cutoff = "1"; // 19:00 - 23:59
            } else if (hours >= 0 && hours <= 6) {
                // Second part of night shift (00:00 - 06:59) - next day, but shift started previous day
                shiftDate = new Date(manilaTime);
                shiftDate.setDate(shiftDate.getDate() - 1); // Use previous day for shift date

                if (hours >= 0 && hours <= 3) {
                    cutoff = "2"; // 00:00 - 03:59 (crosses midnight)
                } else {
                    cutoff = "3"; // 04:00 - 06:59
                }
            }
        }

        // Format date as YYYY-MM-DD for the date input
        const shiftDateString =
            shiftDate.getFullYear() +
            "-" +
            String(shiftDate.getMonth() + 1).padStart(2, "0") +
            "-" +
            String(shiftDate.getDate()).padStart(2, "0");

        return {
            shift,
            cutoff,
            date: shiftDateString,
            currentTime: `${hours.toString().padStart(2, "0")}:${minutes
                .toString()
                .padStart(2, "0")}`,
            shiftDisplay: shift === "day" ? "Day" : "Night",
        };
    }

    toggleAutoRefresh() {
        const toggle = document.getElementById("autoRefreshToggle");
        const status = document.getElementById("refreshStatus");
        const intervalInput = document.getElementById("refreshInterval");

        if (toggle.checked) {
            // Auto-set to current shift and cutoff
            const currentShiftInfo = this.getCurrentShiftAndCutoff();

            // Update UI selectors
            document.getElementById("dashboard_date").value =
                currentShiftInfo.date;
            document.getElementById("dashboard_shift").value =
                currentShiftInfo.shift;
            document.getElementById("dashboard_cutoff").value =
                currentShiftInfo.cutoff;

            // Enable cutoff selector since we're setting a specific shift
            document.getElementById("dashboard_cutoff").disabled = false;

            // Start auto refresh with time updates
            const intervalSeconds = parseInt(intervalInput.value) || 30;
            const intervalMs = intervalSeconds * 1000;

            this.autoRefreshInterval = setInterval(() => {
                // Update current shift, date, and time automatically
                this.updateCurrentShiftAndTime();
                // Then update dashboard data
                this.updateDashboardData(true);
            }, intervalMs);
            status.style.display = "inline-block";

            // Update dashboard with new settings
            this.updateDashboardData();
        } else {
            if (this.autoRefreshInterval) {
                clearInterval(this.autoRefreshInterval);
                this.autoRefreshInterval = null;
            }
            status.style.display = "none";
            this.showToast("Auto refresh disabled", "info");
        }
    }

    // Helper function to get ordinal suffix (1st, 2nd, 3rd)
    getSuffix(cutoff) {
        if (cutoff === "1") return "st";
        if (cutoff === "2") return "nd";
        if (cutoff === "3") return "rd";
        return "th";
    }

    /**
     * Update current shift and time automatically during auto-refresh
     */
    updateCurrentShiftAndTime() {
        const toggle = document.getElementById("autoRefreshToggle");
        if (!toggle || !toggle.checked) return; // Only update when auto-refresh is on

        try {
            // Get current shift and cutoff
            const currentShiftInfo = this.getCurrentShiftAndCutoff();

            // Update UI selectors if they have changed
            const dateElement = document.getElementById("dashboard_date");
            const shiftElement = document.getElementById("dashboard_shift");
            const cutoffElement = document.getElementById("dashboard_cutoff");

            let hasChanged = false;

            if (dateElement && dateElement.value !== currentShiftInfo.date) {
                dateElement.value = currentShiftInfo.date;
                hasChanged = true;
            }

            if (shiftElement && shiftElement.value !== currentShiftInfo.shift) {
                shiftElement.value = currentShiftInfo.shift;
                hasChanged = true;
            }

            if (
                cutoffElement &&
                cutoffElement.value !== currentShiftInfo.cutoff
            ) {
                cutoffElement.value = currentShiftInfo.cutoff;
                hasChanged = true;
            }

            // Update URL with current filter states if anything changed
            if (hasChanged) {
                const workType =
                    document.getElementById("dashboard_work_type")?.value ||
                    "all";
                const allocType =
                    document.getElementById("dashboard_alloc_type")?.value ||
                    "all";
                this.updateURLWithFilters(
                    currentShiftInfo.date,
                    currentShiftInfo.shift,
                    currentShiftInfo.cutoff,
                    workType,
                    allocType
                );
                console.log("Auto-updated to:", currentShiftInfo);
            }
        } catch (error) {
            console.error("Error updating current shift and time:", error);
        }
    }

    handleIntervalChange() {
        const toggle = document.getElementById("autoRefreshToggle");
        const intervalInput = document.getElementById("refreshInterval");

        let intervalValue = parseInt(intervalInput.value);
        if (intervalValue < 5) {
            intervalValue = 5;
            intervalInput.value = 5;
        } else if (intervalValue > 300) {
            intervalValue = 300;
            intervalInput.value = 300;
        }

        if (toggle.checked && this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            const intervalMs = intervalValue * 1000;
            this.autoRefreshInterval = setInterval(() => {
                // Update current shift, date, and time automatically
                this.updateCurrentShiftAndTime();
                // Then update dashboard data
                this.updateDashboardData(true);
            }, intervalMs);
        }
    }

    handleShiftChange() {
        const shiftSelector = document.getElementById("dashboard_shift");
        const cutoffSelector = document.getElementById("dashboard_cutoff");

        if (shiftSelector.value === "all") {
            cutoffSelector.disabled = true;
            cutoffSelector.value = "all";
        } else {
            cutoffSelector.disabled = false;
            if (cutoffSelector.value === "all") {
                cutoffSelector.value = "1";
            }
        }
        // Update both dashboard cards and chart
        this.updateDashboardData();
    }

    updateDashboardData(isAutoRefresh = false) {
        const date = document.getElementById("dashboard_date").value;
        const shift = document.getElementById("dashboard_shift").value;
        const cutoff = document.getElementById("dashboard_cutoff").value;
        const workType =
            document.getElementById("dashboard_work_type")?.value || "all";
        const allocType =
            document.getElementById("dashboard_alloc_type")?.value || "all";

        // Update URL with current filter states for persistence
        if (!isAutoRefresh) {
            this.updateURLWithFilters(date, shift, cutoff, workType, allocType);
        }

        if (!isAutoRefresh) {
            this.showLoadingState();
        }

        const url = new URL("/api/dashboard-stats", window.location.origin);
        url.searchParams.set("dashboard_date", date);
        url.searchParams.set("dashboard_shift", shift);
        url.searchParams.set("dashboard_cutoff", cutoff);
        url.searchParams.set("dashboard_work_type", workType);
        url.searchParams.set("dashboard_alloc_type", allocType);

        fetch(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
        })
            .then((response) => response.json())
            .then((data) => {
                if (data.success) {
                    this.updateDashboardCards(data.stats);
                    // Update Per Line and Per Size Summary tables
                    this.updateSummaryTables(
                        data.perLineSummary,
                        data.perSizeSummary
                    );
                    // Update Line Performance Analysis
                    if (data.linePerformanceAnalysis) {
                        this.updateLinePerformanceAnalysis(
                            data.linePerformanceAnalysis
                        );
                    }
                    // Update dynamic panels
                    if (data.previousShiftAchievement) {
                        this.updatePreviousShiftAchievement(
                            data.previousShiftAchievement
                        );
                    }
                    if (data.currentPerformanceMonitor) {
                        this.updateCurrentPerformanceMonitor(
                            data.currentPerformanceMonitor
                        );
                    }
                    // Also update the chart with new filter settings
                    this.updateChart();
                    if (!isAutoRefresh) {
                        this.showToast(
                            "Dashboard updated successfully",
                            "success"
                        );
                    }
                } else {
                    this.showToast("Failed to update dashboard", "error");
                }
            })
            .catch((error) => {
                this.showToast("Error updating dashboard", "error");
            })
            .finally(() => {
                this.hideLoadingState();
            });
    }

    updateDashboardCards(stats) {
        function formatNumber(num) {
            if (num === null || num === undefined) return 0;
            return parseInt(num).toLocaleString();
        }

        function formatPCS(num) {
            if (num === null || num === undefined) return "0.0";
            // If already formatted (string), extract numeric part and reformat consistently
            if (typeof num === 'string' && num.includes('M PCS')) {
                const numericPart = parseFloat(num.replace(/[^\d.-]/g, ''));
                return numericPart.toFixed(1).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            }
            // Format raw number to millions
            const millions = parseFloat(num) / 1000000;
            return millions.toFixed(1).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }

        // Update main cards
        document.getElementById("targetCapacity").textContent = `${formatPCS(
            stats.target_capacity
        )} M PCS`;
        document.getElementById("targetBadge").textContent = `${formatNumber(
            stats.equipment_count
        )} EQP Count`;

        document.getElementById("totalEndtime").textContent = `${formatPCS(
            stats.total_quantity
        )} M PCS`;
        document.getElementById("totalPcs").textContent = `${formatNumber(
            stats.total_lots
        )} LOTS`;

        document.getElementById("submittedLots").textContent = `${formatPCS(
            stats.submitted_quantity
        )} M PCS`;
        document.getElementById("submittedPcs").textContent = `${formatNumber(
            stats.submitted_lots
        )} LOTS`;

        document.getElementById("ongoingLots").textContent = `${formatPCS(
            stats.ongoing_quantity
        )} M PCS`;
        document.getElementById("ongoingPcs").textContent = `${formatNumber(
            stats.ongoing_lots
        )} LOTS`;

        // Update percentages
        const submittedPercentageElement = document
            .querySelector("#submittedLots")
            .parentElement.querySelector("small");
        if (submittedPercentageElement) {
            submittedPercentageElement.textContent = `${(
                stats.submitted_percentage || 0
            ).toFixed(1)}%`;
        }

        const ongoingPercentageElement = document
            .querySelector("#ongoingLots")
            .parentElement.querySelector("small");
        if (ongoingPercentageElement) {
            ongoingPercentageElement.textContent = `${(
                stats.ongoing_percentage || 0
            ).toFixed(1)}%`;
        }

        // Update Equipment Status data
        this.updateEquipmentStatus(stats);

        // Update progress bar values (for production progress bars)
        this.updateProgressBars(stats);
    }

    updateProgressBars(stats) {
        const targetCapacity = parseFloat(stats.target_capacity || 0);
        const totalQuantity = parseFloat(stats.total_quantity || 0);
        const submittedQuantity = parseFloat(stats.submitted_quantity || 0);
        const ongoingQuantity = parseFloat(stats.ongoing_quantity || 0);

        // Calculate percentages for progress bar widths
        const endtimePercent =
            targetCapacity > 0
                ? Math.min((totalQuantity / targetCapacity) * 100, 100)
                : 0;
        const submittedPercent =
            totalQuantity > 0
                ? Math.min((submittedQuantity / totalQuantity) * 100, 100)
                : 0;
        const remainingPercent =
            totalQuantity > 0
                ? Math.min((ongoingQuantity / totalQuantity) * 100, 100)
                : 0;

        // Update only the progress bar widths
        const progressBars = document.querySelectorAll(
            ".enhanced-progress-fill"
        );
        if (progressBars[0]) progressBars[0].style.width = "100%"; // Target always 100%
        if (progressBars[1]) progressBars[1].style.width = endtimePercent + "%";
        if (progressBars[2])
            progressBars[2].style.width = submittedPercent + "%";
        if (progressBars[3])
            progressBars[3].style.width = remainingPercent + "%";
    }

    resetDashboardFilters() {
        // Get current date in Asia/Manila timezone
        const now = new Date();
        const manilaTime = new Date(
            now.toLocaleString("en-US", { timeZone: "Asia/Manila" })
        );
        const currentDateString =
            manilaTime.getFullYear() +
            "-" +
            String(manilaTime.getMonth() + 1).padStart(2, "0") +
            "-" +
            String(manilaTime.getDate()).padStart(2, "0");

        const cutoffSelector = document.getElementById("dashboard_cutoff");

        document.getElementById("dashboard_date").value = currentDateString;
        document.getElementById("dashboard_shift").value = "all";
        if (document.getElementById("dashboard_work_type")) {
            document.getElementById("dashboard_work_type").value = "all";
        }
        if (document.getElementById("dashboard_alloc_type")) {
            document.getElementById("dashboard_alloc_type").value = "all";
        }

        cutoffSelector.value = "all";
        cutoffSelector.disabled = true;

        // Turn off auto refresh if it was on
        const toggle = document.getElementById("autoRefreshToggle");
        const status = document.getElementById("refreshStatus");
        if (toggle.checked) {
            toggle.checked = false;
            if (this.autoRefreshInterval) {
                clearInterval(this.autoRefreshInterval);
                this.autoRefreshInterval = null;
            }
            status.style.display = "none";
        }

        this.updateDashboardData();
        this.showToast("Filters reset to current date", "info");
    }

    /**
     * Initialize dashboard filters based on auto refresh state
     * If auto refresh is OFF, set to current date with default filters
     * If auto refresh is ON, maintain current state (shouldn't happen on page load)
     */
    initializeDashboardFilters() {
        const toggle = document.getElementById("autoRefreshToggle");
        const dateInput = document.getElementById("dashboard_date");
        const shiftSelector = document.getElementById("dashboard_shift");
        const cutoffSelector = document.getElementById("dashboard_cutoff");
        const workTypeSelector = document.getElementById("dashboard_work_type");
        const allocTypeSelector = document.getElementById(
            "dashboard_alloc_type"
        );

        // Check if auto refresh toggle is OFF (default state after page load)
        if (!toggle.checked) {
            // Set to current date (Asia/Manila timezone)
            const now = new Date();
            const manilaTime = new Date(
                now.toLocaleString("en-US", { timeZone: "Asia/Manila" })
            );
            const currentDateString =
                manilaTime.getFullYear() +
                "-" +
                String(manilaTime.getMonth() + 1).padStart(2, "0") +
                "-" +
                String(manilaTime.getDate()).padStart(2, "0");

            // Set default values for manual mode
            dateInput.value = currentDateString;
            shiftSelector.value = "all";
            if (workTypeSelector) {
                workTypeSelector.value = "all";
            }
            if (allocTypeSelector) {
                allocTypeSelector.value = "all";
            }
            cutoffSelector.value = "all";
            cutoffSelector.disabled = true;
        } else {
            // This case shouldn't normally happen on page load, but handle it gracefully
        }

        // Update dashboard with initial settings
        this.updateDashboardData();
    }

    showLoadingState() {
        const cards = document.querySelectorAll(".dashboard-main-card h5");
        cards.forEach((card) => {
            card.style.opacity = "0.5";
        });
    }

    hideLoadingState() {
        const cards = document.querySelectorAll(".dashboard-main-card h5");
        cards.forEach((card) => {
            card.style.opacity = "1";
        });
    }

    showToast(message, type) {
        const toast = document.createElement("div");
        toast.className = `alert alert-${
            type === "success"
                ? "success"
                : type === "error"
                ? "danger"
                : "info"
        } alert-dismissible fade show position-fixed`;

        toast.style.cssText = `
            top: 70px; 
            right: 20px; 
            z-index: 1060; 
            min-width: 300px;
            max-width: 400px;
            font-size: 0.875rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        `;

        toast.innerHTML = `
            <i class="fas fa-${
                type === "success"
                    ? "check-circle"
                    : type === "error"
                    ? "exclamation-circle"
                    : "info-circle"
            } me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }

    // Equipment Status gradient radial chart initialization
    initializeEquipmentStatusChart() {
        const chartContainer = document.getElementById(
            "equipment-status-chart"
        );
        if (!chartContainer || typeof ApexCharts === "undefined") {
            return;
        }

        try {
            // Get initial data from global variable set by PHP
            const dashboardStats = window.dashboardStats || {};
            const totalEquipment = dashboardStats.total_equipment || 0;
            const equipmentWithOngoing =
                dashboardStats.equipment_with_ongoing || 0;
            const percentage = dashboardStats.equipment_status_percentage || 0;

            const gradientOptions = {
                series: [percentage],
                chart: {
                    height: 300,
                    type: "radialBar",
                    toolbar: {
                        show: false,
                    },
                },
                plotOptions: {
                    radialBar: {
                        startAngle: -135,
                        endAngle: 225,
                        hollow: {
                            margin: 0,
                            size: "50%",
                            background: "transparent",
                            image: undefined,
                            imageOffsetX: 0,
                            imageOffsetY: 0,
                            position: "front",
                            dropShadow: {
                                enabled: true,
                                top: 3,
                                left: 0,
                                blur: 4,
                                opacity: 0.24,
                            },
                        },
                        track: {
                            background: "rgba(255, 255, 255, 0.95)",
                            strokeWidth: "67%",
                            margin: 0,
                            dropShadow: {
                                enabled: true,
                                top: -3,
                                left: 0,
                                blur: 4,
                                opacity: 0.35,
                            },
                        },
                        dataLabels: {
                            show: true,
                            name: {
                                offsetY: -10,
                                show: true,
                                color: "#888",
                                fontSize: "17px",
                                fontWeight: "normal",
                            },
                            value: {
                                formatter: function (val) {
                                    return parseInt(val) + "%";
                                },
                                color: "#111",
                                fontSize: "30px",
                                show: true,
                                fontWeight: "bold",
                            },
                        },
                    },
                },
                fill: {
                    type: "gradient",
                    gradient: {
                        shade: "light",
                        type: "horizontal",
                        shadeIntensity: 0.4,
                        gradientToColors: ["#00E396"],
                        inverseColors: false,
                        opacityFrom: 0.9,
                        opacityTo: 0.7,
                        stops: [0, 100],
                    },
                },
                stroke: {
                    lineCap: "round",
                },
                labels: ["UTILIZED EQP"],
                colors: ["#00D4FF"],
            };

            window.equipmentStatusChart = new ApexCharts(
                chartContainer,
                gradientOptions
            );
            window.equipmentStatusChart.render();
        } catch (error) {
            // Error initializing equipment status chart
        }
    }

    // Initialize Equipment Status Chart in ProductionDashboard class
    initializeEquipmentStatusChart() {
        const chartContainer = document.getElementById(
            "equipment-status-chart"
        );
        if (!chartContainer || typeof ApexCharts === "undefined") {
            return;
        }

        try {
            // Get initial data from global variable set by PHP
            const dashboardStats = window.dashboardStats || {};
            const percentage = dashboardStats.equipment_status_percentage || 0;

            const gradientOptions = {
                series: [percentage],
                chart: {
                    height: 300,
                    type: "radialBar",
                    toolbar: {
                        show: false,
                    },
                },
                plotOptions: {
                    radialBar: {
                        startAngle: -135,
                        endAngle: 225,
                        hollow: {
                            margin: 0,
                            size: "70%",
                            background: "transparent",
                            dropShadow: {
                                enabled: true,
                                top: 3,
                                left: 0,
                                blur: 4,
                                opacity: 0.24,
                            },
                        },
                        track: {
                            background: "rgba(255, 255, 255, 0.95)",
                            strokeWidth: "70%",
                            margin: 0,
                            dropShadow: {
                                enabled: true,
                                top: -3,
                                left: 0,
                                blur: 4,
                                opacity: 0.35,
                            },
                        },
                        dataLabels: {
                            show: true,
                            name: {
                                offsetY: -10,
                                show: true,
                                color: "#888",
                                fontSize: "17px",
                                fontWeight: "normal",
                            },
                            value: {
                                formatter: function (val) {
                                    return parseInt(val) + "%";
                                },
                                color: "#111",
                                fontSize: "36px",
                                show: true,
                                fontWeight: "bold",
                            },
                        },
                    },
                },
                fill: {
                    type: "gradient",
                    gradient: {
                        shade: "light",
                        type: "horizontal",
                        shadeIntensity: 0.4,
                        gradientToColors: ["#00E396"],
                        inverseColors: false,
                        opacityFrom: 0.9,
                        opacityTo: 0.7,
                        stops: [0, 100],
                    },
                },
                stroke: {
                    lineCap: "round",
                },
                labels: ["UTILIZED EQP"],
                colors: ["#00D4FF"],
            };

            window.equipmentStatusChart = new ApexCharts(
                chartContainer,
                gradientOptions
            );
            window.equipmentStatusChart.render();
        } catch (error) {
            // Error initializing equipment status chart
        }
    }

    // Update Per Line and Per Size Summary tables
    updateSummaryTables(perLineSummary, perSizeSummary) {
        // Update Per Line Summary table
        if (perLineSummary) {
            this.updatePerLineTable(perLineSummary);
        }

        // Update Per Size Summary table
        if (perSizeSummary) {
            this.updatePerSizeTable(perSizeSummary);
        }
    }

    // Update Line Performance Analysis section
    updateLinePerformanceAnalysis(data) {
        // Store all lines data for click handlers
        window.allLinesData = data.all_lines || {};

        // Update Top Performers section
        this.updatePerformanceSection(
            "top_performers",
            data.top_performers || [],
            "bg-success-transparent",
            "bg-success",
            "text-success"
        );

        // Update Average Performance section
        this.updatePerformanceSection(
            "average_performance",
            data.average_performance || [],
            "bg-light",
            "bg-info",
            "text-info"
        );

        // Update Needs Attention section
        this.updatePerformanceSection(
            "needs_attention",
            data.needs_attention || [],
            "bg-warning-transparent",
            "bg-warning",
            "text-warning"
        );

        // Update Area Performance
        this.updateAreaPerformance(data.area_performance || {});

        // Update selected line display
        const selectedLineDisplay = document.getElementById(
            "selected-line-display"
        );
        if (selectedLineDisplay) {
            selectedLineDisplay.textContent = data.selected_line || "A";
        }

        // Bind click handlers to line items
        this.bindLineClickHandlers();
    }

    // Bind click handlers to line performance items
    bindLineClickHandlers() {
        const lineItems = document.querySelectorAll(".line-clickable");
        lineItems.forEach((item) => {
            item.addEventListener("click", (e) => {
                const line = e.currentTarget.dataset.line;
                if (line) {
                    this.selectLine(line);
                }
            });
        });
    }

    // Handle line selection and update area performance
    async selectLine(selectedLine) {
        // Update selected line display
        const selectedLineDisplay = document.getElementById(
            "selected-line-display"
        );
        if (selectedLineDisplay) {
            selectedLineDisplay.textContent = selectedLine;
        }

        // Visual feedback - highlight selected line
        document.querySelectorAll(".line-clickable").forEach((item) => {
            item.classList.remove("line-selected");
        });
        document
            .querySelector(`[data-line="${selectedLine}"]`)
            ?.classList.add("line-selected");

        // Fetch area performance for selected line
        try {
            const params = new URLSearchParams({
                line: selectedLine,
                dashboard_date:
                    document.getElementById("dashboard_date")?.value ||
                    new Date().toISOString().split("T")[0],
                dashboard_shift:
                    document.getElementById("dashboard_shift")?.value || "all",
                dashboard_cutoff:
                    document.getElementById("dashboard_cutoff")?.value || "all",
                dashboard_work_type:
                    document.getElementById("dashboard_work_type")?.value ||
                    "all",
                dashboard_alloc_type:
                    document.getElementById("dashboard_alloc_type")?.value ||
                    "all",
            });

            const response = await fetch(
                `/api/line-area-performance?${params.toString()}`
            );
            const result = await response.json();

            if (result.success) {
                this.updateAreaPerformance(result.area_performance);
            } else {
                console.error("API call failed:", result.message);
            }
        } catch (error) {
            console.error("Error fetching line area performance:", error);
        }
    }

    // Test function to debug area performance updates
    testAreaPerformanceUpdate(testLine = "F") {
        // Test with mock data
        const mockAreaData = {
            1: 95.2,
            2: 87.5,
            3: 92.1,
            4: 78.9,
        };

        this.updateAreaPerformance(mockAreaData);

        // Also test the actual API call
        setTimeout(() => {
            this.selectLine(testLine);
        }, 2000);
    }

    // Initialize charts
    initCharts() {
        this.initProductionOverviewChart();
    }

    // Production Overview Chart (ApexCharts) - OPTIMIZED with immediate container visibility
    async initProductionOverviewChart() {
        const chartContainer = document.querySelector("#projects-overview");
        if (!chartContainer || typeof ApexCharts === "undefined") {
            console.error("Chart container or ApexCharts not available");
            return;
        }

        try {
            // Show loading state immediately
            this.showChartLoadingState();
            this.hideChartErrorState();

            // Get chart data from database (optimized)
            const chartData = await this.fetchChartData();

            const options = {
                series: chartData.series,
                chart: {
                    type: "area",
                    height: 395,
                    animations: {
                        speed: 100,
                    },
                    toolbar: {
                        show: false,
                    },
                    zoom: {
                        enabled: false,
                    },
                    dropShadow: {
                        enabled: true,
                        enabledOnSeries: undefined,
                        top: 6,
                        left: 1,
                        blur: 4,
                        color: ["transparent", "#000", "transparent"],
                        opacity: 0.12,
                    },
                },
                colors: [
                    "rgba(253, 175, 34, 1)",
                    "var(--primary-color)",
                    "rgba(50, 212, 132, 1)",
                    "rgba(255, 73, 205, 1)",
                ],
                dataLabels: {
                    enabled: false,
                },
                markers: {
                    size: [6, 0, 0, 0],
                    colors: [
                        "rgba(253, 175, 34, 1)",
                        "var(--primary-color)",
                        "rgba(50, 212, 132, 1)",
                        "rgba(255, 73, 205, 1)",
                    ],
                    strokeColors: ["#fff", "#fff", "#fff", "#fff"],
                    strokeWidth: 2,
                    hover: {
                        size: 10,
                        sizeOffset: 2,
                    },
                },
                grid: {
                    borderColor: "#f1f1f1",
                    strokeDashArray: 2,
                    xaxis: {
                        lines: {
                            show: true,
                        },
                    },
                    yaxis: {
                        lines: {
                            show: false,
                        },
                    },
                },
                fill: {
                    type: "gradient",
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.4,
                        opacityTo: 0.1,
                        stops: [0, 90, 100],
                        colorStops: [
                            [
                                {
                                    offset: 0,
                                    color: "rgba(253, 175, 34, 0.1)",
                                    opacity: 1,
                                },
                                {
                                    offset: 75,
                                    color: "rgba(253, 175, 34, 0.1)",
                                    opacity: 1,
                                },
                                {
                                    offset: 100,
                                    color: "rgba(253, 175, 34, 0.1)",
                                    opacity: 1,
                                },
                            ],
                            [
                                {
                                    offset: 0,
                                    color: "var(--primary-color)",
                                    opacity: 1,
                                },
                                {
                                    offset: 75,
                                    color: "var(--primary-color)",
                                    opacity: 1,
                                },
                                {
                                    offset: 100,
                                    color: "var(--primary-color)",
                                    opacity: 1,
                                },
                            ],
                            [
                                {
                                    offset: 0,
                                    color: "rgba(50, 212, 132, 1)",
                                    opacity: 1,
                                },
                                {
                                    offset: 75,
                                    color: "rgba(50, 212, 132, 1)",
                                    opacity: 1,
                                },
                                {
                                    offset: 100,
                                    color: "rgba(50, 212, 132, 1)",
                                    opacity: 1,
                                },
                            ],
                            [
                                {
                                    offset: 0,
                                    color: "rgba(255, 73, 205, 1)",
                                    opacity: 1,
                                },
                                {
                                    offset: 75,
                                    color: "rgba(255, 73, 205, 1)",
                                    opacity: 1,
                                },
                                {
                                    offset: 100,
                                    color: "rgba(255, 73, 205, 1)",
                                    opacity: 1,
                                },
                            ],
                        ],
                    },
                },
                stroke: {
                    curve: ["smooth", "smooth", "smooth", "smooth"],
                    width: [4, 0, 0, 0],
                    dashArray: [4, 0, 0, 0],
                },
                xaxis: {
                    axisTicks: {
                        show: false,
                    },
                },
                yaxis: {
                    labels: {
                        formatter: function (value) {
                            return value + "M PCS";
                        },
                    },
                },
                plotOptions: {
                    bar: {
                        columnWidth: "40%",
                        borderRadius: "3",
                    },
                },
                tooltip: {
                    y: [
                        {
                            formatter: function (e) {
                                return void 0 !== e
                                    ? e.toFixed(0) + "M PCS"
                                    : e;
                            },
                        },
                        {
                            formatter: function (e) {
                                return void 0 !== e
                                    ? e.toFixed(0) + "M PCS"
                                    : e;
                            },
                        },
                        {
                            formatter: function (e) {
                                return void 0 !== e
                                    ? e.toFixed(0) + "M PCS"
                                    : e;
                            },
                        },
                        {
                            formatter: function (e) {
                                return void 0 !== e
                                    ? e.toFixed(0) + "M PCS"
                                    : e;
                            },
                        },
                    ],
                },
                legend: {
                    show: true,
                    position: "top",
                    markers: {
                        size: 5,
                        strokeWidth: 0,
                    },
                },
            };

            const manufacturingChart = new ApexCharts(chartContainer, options);

            // Render the chart and handle completion
            await manufacturingChart.render();

            // Store reference globally for potential updates
            window.manufacturingChart = manufacturingChart;

            // Hide loading state and show success
            this.hideChartLoadingState();
            this.setChartStatus("");
        } catch (error) {
            console.error("Error initializing manufacturing chart:", error);
            this.showChartErrorState();
        }
    }

    // Fetch chart data from API using current dashboard filters
    async fetchChartData() {
        try {
            const params = new URLSearchParams({
                dashboard_date:
                    document.getElementById("dashboard_date")?.value ||
                    new Date().toISOString().split("T")[0],
                dashboard_shift:
                    document.getElementById("dashboard_shift")?.value || "all",
                dashboard_cutoff:
                    document.getElementById("dashboard_cutoff")?.value || "all",
                dashboard_work_type:
                    document.getElementById("dashboard_work_type")?.value ||
                    "all",
                dashboard_alloc_type:
                    document.getElementById("dashboard_alloc_type")?.value ||
                    "all",
            });

            const response = await fetch(
                `/api/manufacturing-overview?${params.toString()}`
            );
            const result = await response.json();

            if (result.success) {
                return result.data;
            } else {
                return this.getFallbackChartData();
            }
        } catch (error) {
            return this.getFallbackChartData();
        }
    }

    // Fallback chart data in case API fails
    getFallbackChartData() {
        return {
            labels: [
                "Line A",
                "Line B",
                "Line C",
                "Line D",
                "Line E",
                "Line F",
                "Line G",
                "Line H",
                "Line I",
                "Line J",
                "Line K",
            ],
            series: [
                {
                    type: "area",
                    name: "Target",
                    data: [
                        { x: "Line A", y: 0.68 },
                        { x: "Line B", y: 0.8 },
                        { x: "Line C", y: 0.68 },
                        { x: "Line D", y: 0.84 },
                        { x: "Line E", y: 0.98 },
                        { x: "Line F", y: 0.72 },
                        { x: "Line G", y: 0.9 },
                        { x: "Line H", y: 1.0 },
                        { x: "Line I", y: 0.85 },
                        { x: "Line J", y: 0.95 },
                        { x: "Line K", y: 0.75 },
                    ],
                },
                {
                    type: "bar",
                    name: "Endtime",
                    data: [
                        { x: "Line A", y: 0.32 },
                        { x: "Line B", y: 0.56 },
                        { x: "Line C", y: 0.25 },
                        { x: "Line D", y: 0.49 },
                        { x: "Line E", y: 0.31 },
                        { x: "Line F", y: 0.56 },
                        { x: "Line G", y: 0.56 },
                        { x: "Line H", y: 0.86 },
                        { x: "Line I", y: 0.4 },
                        { x: "Line J", y: 0.5 },
                        { x: "Line K", y: 0.35 },
                    ],
                },
                {
                    type: "bar",
                    name: "Submitted",
                    chart: {
                        dropShadow: {
                            enabled: true,
                            enabledOnSeries: undefined,
                            top: 5,
                            left: 0,
                            blur: 3,
                            color: "#000",
                            opacity: 0.1,
                        },
                    },
                    data: [
                        { x: "Line A", y: 0.18 },
                        { x: "Line B", y: 0.25 },
                        { x: "Line C", y: 0.3 },
                        { x: "Line D", y: 0.35 },
                        { x: "Line E", y: 0.35 },
                        { x: "Line F", y: 0.25 },
                        { x: "Line G", y: 0.15 },
                        { x: "Line H", y: 0.25 },
                        { x: "Line I", y: 0.35 },
                        { x: "Line J", y: 0.35 },
                        { x: "Line K", y: 0.2 },
                    ],
                },
                {
                    type: "bar",
                    name: "Remaining",
                    data: [
                        { x: "Line A", y: 0.28 },
                        { x: "Line B", y: 0.32 },
                        { x: "Line C", y: 0.23 },
                        { x: "Line D", y: 0.19 },
                        { x: "Line E", y: 0.32 },
                        { x: "Line F", y: 0.21 },
                        { x: "Line G", y: 0.35 },
                        { x: "Line H", y: 0.49 },
                        { x: "Line I", y: 0.3 },
                        { x: "Line J", y: 0.3 },
                        { x: "Line K", y: 0.25 },
                    ],
                },
            ],
        };
    }

    // Update a specific performance section (Top Performers, Average, Needs Attention)
    updatePerformanceSection(
        sectionKey,
        performersData,
        bgClass,
        badgeClass,
        textClass
    ) {
        // Find the section by looking for the specific heading
        const sectionHeadings = {
            top_performers: "Top Performers",
            average_performance: "Average Performance",
            needs_attention: "Needs Attention",
        };

        const sectionTitle = sectionHeadings[sectionKey];
        const headingElement = Array.from(document.querySelectorAll("h6")).find(
            (h) => h.textContent.includes(sectionTitle)
        );

        if (!headingElement) {
            return;
        }

        // Update the count badge
        const countBadge = headingElement.querySelector(".badge");
        if (countBadge) {
            countBadge.textContent = performersData.length;
        }

        // Find the container (should be the next sibling or in the parent's next sibling)
        let container = headingElement.parentElement;
        let performanceItems = container.querySelectorAll(".performance-item");

        // Clear existing items
        performanceItems.forEach((item) => item.remove());

        // Add new items
        performersData.forEach((performer, index) => {
            const itemElement = this.createPerformanceItem(
                performer,
                sectionKey === "needs_attention" && index === 0
                    ? "bg-danger-transparent"
                    : bgClass,
                sectionKey === "needs_attention" && index === 0
                    ? "bg-danger"
                    : badgeClass,
                sectionKey === "needs_attention" && index === 0
                    ? "text-danger"
                    : textClass
            );
            container.appendChild(itemElement);
        });
    }

    // Create a performance item element
    createPerformanceItem(performer, bgClass, badgeClass, textClass) {
        const itemDiv = document.createElement("div");
        itemDiv.className = `performance-item line-clickable d-flex justify-content-between align-items-center mb-2 p-2 ${bgClass} rounded`;
        itemDiv.style.cursor = "pointer";
        itemDiv.dataset.line = performer.line;

        itemDiv.innerHTML = `
            <div class="d-flex align-items-center">
                <span class="badge ${badgeClass} me-2">${performer.line}</span>
                <div class="flex-fill">
                    <div class="fw-medium small">Line ${performer.line}</div>
                    <div class="row g-0 text-muted small">
                        <div class="col">Target: ${
                            performer.target_formatted || "0 PCS"
                        }</div>
                    </div>
                    <div class="row g-0 text-muted small">
                        <div class="col">Result: ${
                            performer.result_formatted || "0 PCS"
                        }</div>
                    </div>
                </div>
            </div>
            <div class="text-end">
                <div class="fw-medium ${textClass}">${
            performer.performance_percent
        }%</div>
                <small class="text-muted">Achievement</small>
            </div>
        `;

        return itemDiv;
    }

    // Update Area Performance section
    updateAreaPerformance(areaData) {
        for (let area = 1; area <= 4; area++) {
            const areaPerf = areaData[area] || 0;
            const colorClass =
                areaPerf >= 100
                    ? "success"
                    : areaPerf >= 80
                    ? "warning"
                    : "danger";

            // Find the area performance element by data attribute
            const areaElement = document.querySelector(`[data-area="${area}"]`);

            if (areaElement) {
                const perfDiv = areaElement.querySelector(".fw-medium");

                if (perfDiv) {
                    // Force immediate DOM update
                    perfDiv.textContent = `${areaPerf}%`;
                    perfDiv.className = `fw-medium text-${colorClass}`;
                    areaElement.className = `p-2 bg-${colorClass}-transparent rounded area-performance-item`;
                    areaElement.setAttribute("data-area", area);

                    // Force browser repaint
                    areaElement.style.display = "none";
                    areaElement.offsetHeight; // Trigger reflow
                    areaElement.style.display = "";
                }
            }
        }
    }

    // Update Current Period Achievement panel (Left Panel)
    updatePreviousShiftAchievement(data) {
        // Update title and subtitle
        const titleElement = document.getElementById(
            "previous-achievement-title"
        );
        const subtitleElement = document.getElementById(
            "previous-achievement-subtitle"
        );
        const badgeElement = document.getElementById(
            "previous-achievement-badge"
        );

        if (titleElement)
            titleElement.textContent =
                data.title || "Current Period Achievement";
        if (subtitleElement)
            subtitleElement.textContent =
                data.subtitle || "Current period results";
        if (badgeElement) badgeElement.textContent = "Current Period";

        // Update circular progress
        const progressElement = document.getElementById(
            "previous-achievement-circle"
        );
        const percentElement = document.getElementById(
            "previous-achievement-percent"
        );
        const achievementCircle = document.getElementById("achievement-circle");

        if (progressElement) {
            progressElement.setAttribute(
                "data-percentage",
                data.achievement_percent || 0
            );
        }
        if (percentElement) {
            percentElement.textContent = `${parseFloat(
                data.achievement_percent || 0
            ).toFixed(1)}%`;
            percentElement.className = `mb-0 text-${
                data.status_class || "warning"
            }`;
        }
        if (achievementCircle) {
            const percent = parseFloat(data.achievement_percent || 0);
            const dashOffset = Math.round(314 - (percent / 100) * 314);
            achievementCircle.style.strokeDashoffset = dashOffset;
            achievementCircle.style.stroke = `var(--${
                data.status_class || "warning"
            }-color)`;
        }

        // Update status text and gap text
        const statusElement = document.getElementById("previous-status-text");
        const gapElement = document.getElementById("previous-gap-text");

        if (statusElement) {
            statusElement.textContent = data.status || "Below Target";
            statusElement.className = `fw-medium text-${
                data.status_class || "warning"
            }`;
        }
        if (gapElement)
            gapElement.textContent = data.gap_text || "No data available";

        // Update metrics - using correct element IDs from the HTML
        const targetElement = document.getElementById("previous-target-pcs");
        const submittedElement = document.getElementById(
            "previous-submitted-lots"
        );
        const endtimeElement = document.getElementById(
            "previous-total-endtime"
        );
        const remainingElement = document.getElementById(
            "previous-remaining-lots"
        );

        if (targetElement) targetElement.textContent = data.target_pcs || "0M";
        if (submittedElement)
            submittedElement.textContent = data.actual_pcs || "0M";
        if (endtimeElement)
            endtimeElement.textContent = data.total_endtime || "0M";
        if (remainingElement)
            remainingElement.textContent = data.remaining_lots || "0";

        // Update status summary
        const resultBadge = document.getElementById("previous-result-badge");
        const impactElement = document.getElementById("previous-impact-text");

        if (resultBadge) {
            resultBadge.textContent = data.status || "Below Target";
            resultBadge.className = `badge bg-${
                data.status_class || "warning"
            }`;
        }
        if (impactElement)
            impactElement.textContent =
                data.impact || "Need recovery in next period";
    }

    // Update Full Day Performance Monitor panel (Right Panel)
    updateCurrentPerformanceMonitor(data) {
        // Update title and subtitle
        const titleElement = document.getElementById("current-monitor-title");
        const subtitleElement = document.getElementById("current-time-display");
        const badgeElement = document.getElementById("current-monitor-badge");

        if (titleElement)
            titleElement.textContent =
                data.title || "Full Day Progress Monitor";
        if (subtitleElement)
            subtitleElement.textContent =
                data.subtitle || "Full day analysis (00:00 ~ 23:59)";
        if (badgeElement) badgeElement.textContent = "Full Day";

        // Update progress section
        const progressTitleElement = document.getElementById(
            "current-progress-title"
        );
        const progressTextElement = document.getElementById(
            "current-progress-text"
        );
        const progressBarElement = document.getElementById(
            "current-progress-bar"
        );

        if (progressTitleElement)
            progressTitleElement.textContent =
                data.period_desc || "Period Progress";
        if (progressTextElement)
            progressTextElement.textContent = `${parseFloat(
                data.progress_percent || 0
            ).toFixed(1)}%`;
        if (progressBarElement)
            progressBarElement.style.width = `${data.progress_percent || 0}%`;

        // Update metrics grid
        const equipmentRunningElement = document.getElementById(
            "current-equipment-running"
        );
        const idealProgressElement = document.getElementById(
            "current-ideal-progress"
        );
        const actualProgressElement = document.getElementById("current-actual-progress");
        const recoveryQtyElement = document.getElementById(
            "current-recovery-qty"
        );

        if (equipmentRunningElement)
            equipmentRunningElement.textContent = data.equipment_running || "0/0";
        if (idealProgressElement)
            idealProgressElement.textContent = data.ideal_progress_qty || "0.0M";
        if (actualProgressElement)
            actualProgressElement.textContent = data.actual_progress_qty || "0.0M";
        if (recoveryQtyElement) {
            recoveryQtyElement.textContent = data.recovery_qty || "0.0M";
        }

        // Update production summary section
        const targetTotalElement = document.getElementById("current-target-total");
        const completionRateElement = document.getElementById("current-completion-rate");
        const timeMarkerElement = document.getElementById("current-time-marker");

        if (targetTotalElement && data.target_capacity_raw) {
            targetTotalElement.textContent = `${(data.target_capacity_raw / 1000000).toFixed(1)}M`;
        }
        if (completionRateElement && data.target_capacity_raw) {
            const completionRate = data.target_capacity_raw > 0 
                ? ((data.actual_progress_qty_raw || 0) / data.target_capacity_raw * 100).toFixed(1)
                : 0;
            completionRateElement.textContent = `${completionRate}%`;
        }
        if (timeMarkerElement && data.current_time) {
            timeMarkerElement.textContent = data.current_time;
        }

        // Update best and worst performance
        const bestLineElement = document.getElementById("current-best-line");
        const bestEfficiencyElement = document.getElementById(
            "current-best-efficiency"
        );
        const worstLineElement = document.getElementById("current-worst-line");
        const worstEfficiencyElement = document.getElementById(
            "current-worst-efficiency"
        );

        if (bestLineElement && data.best_line) {
            bestLineElement.textContent = `Best: Line ${
                data.best_line.line || "A"
            }`;
        }
        if (bestEfficiencyElement && data.best_line) {
            bestEfficiencyElement.textContent =
                data.best_line.efficiency || "0% efficiency";
        }
        if (worstLineElement && data.worst_line) {
            worstLineElement.textContent = `Alert: Line ${
                data.worst_line.line || "K"
            }`;
        }
        if (worstEfficiencyElement && data.worst_line) {
            worstEfficiencyElement.textContent =
                data.worst_line.efficiency || "0% efficiency";
        }

        // Update AI Analysis sections
        if (data.ai_recommendation) {
            this.updateAIRecommendation(data.ai_recommendation);
        }
        if (data.ai_alerts && data.ai_alerts.length > 0) {
            this.updateAIAlerts(data.ai_alerts);
        }
    }

    // Update AI Recommendation display
    updateAIRecommendation(recommendation) {
        const titleElement = document.getElementById("ai-recommendation-title");
        const messageElement = document.getElementById(
            "ai-recommendation-message"
        );
        const containerElement = document.getElementById(
            "ai-recommendation-container"
        );

        if (titleElement) {
            titleElement.textContent =
                recommendation.title || "AI Recommendation";
        }
        if (messageElement) {
            messageElement.textContent =
                recommendation.message || "No recommendation available";
        }

        // Update container styling based on recommendation type
        if (containerElement) {
            // Remove existing alert classes
            containerElement.className = containerElement.className.replace(
                /alert-\w+/g,
                ""
            );

            // Add appropriate alert class based on type
            const alertClass = this.getAlertClass(
                recommendation.type || "info"
            );
            containerElement.classList.add(alertClass);
        }
    }

    // Update AI Alerts display
    updateAIAlerts(alerts) {
        const alertsContainer = document.getElementById("ai-alerts-container");
        if (!alertsContainer) {
            return;
        }

        // Clear existing alerts
        alertsContainer.innerHTML = "";

        // Add new alerts
        alerts.forEach((alert) => {
            const alertElement = this.createAlertElement(alert);
            alertsContainer.appendChild(alertElement);
        });

        // Show the alerts section if there are alerts
        const alertsSection = document.getElementById("ai-alerts-section");
        if (alertsSection) {
            alertsSection.style.display = alerts.length > 0 ? "block" : "none";
        }
    }

    // Create an individual alert element
    createAlertElement(alert) {
        const alertDiv = document.createElement("div");
        const alertClass = this.getAlertClass(alert.type || "info");
        alertDiv.className = `alert ${alertClass} alert-dismissible d-flex align-items-start mb-2 py-2`;

        // Get appropriate icon based on alert type
        const icon = this.getAlertIcon(alert.type || "info");

        alertDiv.innerHTML = `
            <i class="${icon} me-2 mt-1 flex-shrink-0"></i>
            <div class="flex-grow-1">
                <div class="fw-medium small mb-1">${
                    alert.title || "Alert"
                }</div>
                <div class="small text-muted">${
                    alert.message || "No details available"
                }</div>
            </div>
            <button type="button" class="btn-close btn-close-sm" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        return alertDiv;
    }

    // Get Bootstrap alert class based on type
    getAlertClass(type) {
        const alertClasses = {
            success: "alert-success",
            warning: "alert-warning",
            danger: "alert-danger",
            info: "alert-info",
        };
        return alertClasses[type] || "alert-info";
    }

    // Get FontAwesome icon based on alert type
    getAlertIcon(type) {
        const iconClasses = {
            success: "fas fa-check-circle text-success",
            warning: "fas fa-exclamation-triangle text-warning",
            danger: "fas fa-exclamation-circle text-danger",
            info: "fas fa-info-circle text-info",
        };
        return iconClasses[type] || "fas fa-info-circle text-info";
    }

    // Update Per Line Summary table data
    updatePerLineTable(data) {
        const colors = [
            "primary",
            "success",
            "info",
            "warning",
            "secondary",
            "danger",
        ];

        // Find the Per Line Summary table
        const tableRows = document.querySelectorAll(
            ".col-xl-8 .card-body table tbody tr"
        );
        if (tableRows.length >= 5) {
            // Update Target row
            this.updateTableRow(tableRows[0], data.lines, data.target);

            // Update ENDTIME row
            this.updateTableRow(tableRows[1], data.lines, data.endtime);

            // Update SUBMTD row
            this.updateTableRow(tableRows[2], data.lines, data.submitted);

            // Update SUBMTD % row with badges
            this.updatePercentageRow(
                tableRows[3],
                data.lines,
                data.submitted_percent,
                colors,
                true
            );

            // Update ENDTIME % row with badges
            this.updatePercentageRow(
                tableRows[4],
                data.lines,
                data.endtime_percent,
                colors,
                false
            );
        }
    }

    // Update Per Size Summary table data
    updatePerSizeTable(data) {
        const colors = [
            "primary",
            "success",
            "info",
            "warning",
            "secondary",
            "danger",
        ];

        // Find the Per Size Summary table
        const tableRows = document.querySelectorAll(
            ".col-xl-4 .card-body table tbody tr"
        );

        if (tableRows.length >= 5) {
            // Update Target row (row 0) - Per Size table has NO label column, just data cells
            this.updatePerSizeTableRow(tableRows[0], data.sizes, data.target);

            // Update ENDTIME row (row 1)
            this.updatePerSizeTableRow(tableRows[1], data.sizes, data.endtime);

            // Update SUBMTD row (row 2)
            this.updatePerSizeTableRow(
                tableRows[2],
                data.sizes,
                data.submitted
            );

            // Update SUBMTD % row with badges (row 3)
            this.updatePerSizePercentageRow(
                tableRows[3],
                data.sizes,
                data.submitted_percent,
                colors,
                true
            );

            // Update ENDTIME % row with badges (row 4)
            this.updatePerSizePercentageRow(
                tableRows[4],
                data.sizes,
                data.endtime_percent,
                colors,
                false
            );
        }
    }

    // Helper method to update a simple table row (Target, ENDTIME, SUBMTD)
    updateTableRow(row, keys, values) {
        const cells = row.querySelectorAll("td:not(:first-child)");

        keys.forEach((key, index) => {
            if (cells[index]) {
                const newValue = values[key] || "0 M";
                cells[index].textContent = newValue;
            }
        });
    }

    // Helper method to update percentage rows with badges
    updatePercentageRow(row, keys, percentages, colors, isTransparent) {
        const cells = row.querySelectorAll("td:not(:first-child)");
        keys.forEach((key, index) => {
            if (cells[index]) {
                const percent = percentages[key] || 0;
                const color = colors[index % colors.length];
                const transparency = isTransparent ? "-transparent" : "";
                cells[
                    index
                ].innerHTML = `<span class="badge bg-${color}${transparency}">${parseFloat(
                    percent
                ).toFixed(1)}%</span>`;
            }
        });
    }

    // Helper method for Per Size table (no label column - all cells are data)
    updatePerSizeTableRow(row, keys, values) {
        const cells = row.querySelectorAll("td"); // Get ALL cells, no :not(:first-child)

        keys.forEach((key, index) => {
            if (cells[index]) {
                const newValue = values[key] || "0 M";
                cells[index].textContent = newValue;
            }
        });
    }

    // Helper method for Per Size percentage rows (no label column)
    updatePerSizePercentageRow(row, keys, percentages, colors, isTransparent) {
        const cells = row.querySelectorAll("td"); // Get ALL cells, no :not(:first-child)
        keys.forEach((key, index) => {
            if (cells[index]) {
                const percent = percentages[key] || 0;
                const color = colors[index % colors.length];
                const transparency = isTransparent ? "-transparent" : "";
                cells[
                    index
                ].innerHTML = `<span class="badge bg-${color}${transparency}">${parseFloat(
                    percent
                ).toFixed(1)}%</span>`;
            }
        });
    }

    // Update equipment status display
    updateEquipmentStatus(stats) {
        // Update equipment counts
        const totalEquipmentEl = document.getElementById("totalEquipment");
        const equipmentWithOngoingEl = document.getElementById(
            "equipmentWithOngoing"
        );
        const idleEquipmentEl = document.getElementById("idleEquipment");

        if (totalEquipmentEl) {
            totalEquipmentEl.textContent = stats.total_equipment || 0;
        }
        if (equipmentWithOngoingEl) {
            equipmentWithOngoingEl.textContent =
                stats.equipment_with_ongoing || 0;
        }
        if (idleEquipmentEl) {
            const idleCount =
                (stats.total_equipment || 0) -
                (stats.equipment_with_ongoing || 0);
            idleEquipmentEl.textContent = idleCount;
        }

        // Update progress bars
        const ongoingProgressBar = document.querySelector(
            ".equipment-ongoing-progress"
        );
        const idleProgressBar = document.querySelector(
            ".equipment-idle-progress"
        );
        const totalEquipment = stats.total_equipment || 1;

        if (ongoingProgressBar) {
            const ongoingPercentage =
                ((stats.equipment_with_ongoing || 0) / totalEquipment) * 100;
            ongoingProgressBar.style.width = ongoingPercentage + "%";
            // Ensure the green color is applied
            ongoingProgressBar.style.backgroundColor = "rgba(50, 212, 132, 1)";
        }

        if (idleProgressBar) {
            const idleCount =
                (stats.total_equipment || 0) -
                (stats.equipment_with_ongoing || 0);
            const idlePercentage = (idleCount / totalEquipment) * 100;
            idleProgressBar.style.width = idlePercentage + "%";
        }

        // Update the radial chart
        if (
            window.equipmentStatusChart &&
            stats.equipment_status_percentage !== undefined
        ) {
            window.equipmentStatusChart.updateSeries([
                stats.equipment_status_percentage,
            ]);
        }
    }

    // Update the ApexCharts Production Overview chart with new data - OPTIMIZED with throttling
    async updateChart() {
        if (!window.manufacturingChart) return;

        // OPTIMIZATION: Throttle chart updates to prevent multiple rapid API calls
        const now = Date.now();
        if (now - this.lastChartUpdate < this.chartUpdateDelay) {
            // Clear any pending throttled update
            if (this.chartUpdateThrottle) {
                clearTimeout(this.chartUpdateThrottle);
            }

            // Schedule a throttled update
            this.chartUpdateThrottle = setTimeout(() => {
                this.performChartUpdate();
            }, this.chartUpdateDelay);
            return;
        }

        // Execute immediately if enough time has passed
        this.performChartUpdate();
    }

    // Perform the actual chart update with proper loading states
    async performChartUpdate() {
        if (!window.manufacturingChart) {
            await this.initProductionOverviewChart();
            return;
        }

        try {
            this.lastChartUpdate = Date.now();

            // Show loading indicators
            this.showChartLoadingState();

            // Fetch new chart data
            const params = new URLSearchParams({
                dashboard_date:
                    document.getElementById("dashboard_date")?.value ||
                    new Date().toISOString().split("T")[0],
                dashboard_shift:
                    document.getElementById("dashboard_shift")?.value || "all",
                dashboard_cutoff:
                    document.getElementById("dashboard_cutoff")?.value || "all",
                dashboard_work_type:
                    document.getElementById("dashboard_work_type")?.value ||
                    "all",
                dashboard_alloc_type:
                    document.getElementById("dashboard_alloc_type")?.value ||
                    "all",
            });

            const response = await fetch(
                `/api/manufacturing-overview?${params.toString()}`
            );
            const result = await response.json();

            if (result.success) {
                // Update the chart with new series data
                window.manufacturingChart.updateSeries(result.data.series);
                this.hideChartLoadingState();
            } else {
                this.showChartErrorState();
            }
        } catch (error) {
            console.error("Error updating manufacturing chart:", error);
            this.showChartErrorState();
        }
    }

    // Chart loading state management
    showChartLoadingState() {
        const spinner = document.getElementById("chart-loading-spinner");
        const status = document.getElementById("chart-status");
        if (spinner) spinner.style.display = "inline-block";
        if (status) status.textContent = "Loading...";
    }

    hideChartLoadingState() {
        const spinner = document.getElementById("chart-loading-spinner");
        if (spinner) spinner.style.display = "none";
    }

    showChartErrorState() {
        this.hideChartLoadingState();
        const errorOverlay = document.getElementById("chart-error-overlay");
        const status = document.getElementById("chart-status");
        if (errorOverlay) errorOverlay.style.display = "block";
        if (status) status.textContent = "Error";
    }

    hideChartErrorState() {
        const errorOverlay = document.getElementById("chart-error-overlay");
        if (errorOverlay) errorOverlay.style.display = "none";
    }

    setChartStatus(message, extra = "") {
        const status = document.getElementById("chart-status");
        if (status) status.textContent = message + (extra ? " " + extra : "");
    }

    // ==================== EQUIPMENT CAROUSEL FUNCTIONALITY ====================

    /**
     * Toggle equipment carousel between overall view and per-line view
     */
    toggleEquipmentCarousel() {
        try {
            const toggle = document.getElementById("equipmentCarouselToggle");
            const overallView = document.getElementById(
                "equipment-overall-view"
            );
            const lineView = document.getElementById("equipment-line-view");
            const currentLineIndicator = document.getElementById(
                "equipmentCurrentLine"
            );

            if (!toggle || !overallView || !lineView || !currentLineIndicator) {
                console.error("Equipment carousel elements not found");
                return;
            }

            const prevBtn = document.getElementById("prevLineBtn");
            const nextBtn = document.getElementById("nextLineBtn");
            const currentLineDisplay =
                document.getElementById("currentLineDisplay");

            if (toggle.checked) {
                // Switch to line view
                overallView.classList.remove("active");
                lineView.classList.add("active");
                if (currentLineIndicator)
                    currentLineIndicator.textContent = "Line A";

                // Enable navigation buttons
                if (prevBtn) prevBtn.disabled = false;
                if (nextBtn) nextBtn.disabled = false;

                // Initialize carousel state
                this.currentEquipmentLine = "A";
                this.equipmentLines = [
                    "A",
                    "B",
                    "C",
                    "D",
                    "E",
                    "F",
                    "G",
                    "H",
                    "I",
                    "J",
                    "K",
                ];
                this.equipmentCarouselInterval = null;

                // Update display to show current line
                if (currentLineDisplay) currentLineDisplay.textContent = "A";

                // Wait for the view to be visible before loading data
                setTimeout(() => {
                    this.loadLineEquipmentData("A");
                    // Start auto-carousel after initial load
                    this.startEquipmentCarousel();
                }, 100);
            } else {
                // Switch back to overall view
                lineView.classList.remove("active");
                overallView.classList.add("active");
                if (currentLineIndicator)
                    currentLineIndicator.textContent = "Overall";

                // Reset to "All" lines view for manual navigation
                this.currentEquipmentLine = "all";
                this.equipmentLines = [
                    "all",
                    "A",
                    "B",
                    "C",
                    "D",
                    "E",
                    "F",
                    "G",
                    "H",
                    "I",
                    "J",
                    "K",
                ];

                // Enable navigation buttons for manual navigation
                if (prevBtn) prevBtn.disabled = false;
                if (nextBtn) nextBtn.disabled = false;

                // Show 'All' in display
                if (currentLineDisplay) currentLineDisplay.textContent = "All";

                // Stop auto-carousel
                this.stopEquipmentCarousel();

                // Destroy line chart if it exists
                if (window.lineEquipmentStatusChart) {
                    window.lineEquipmentStatusChart.destroy();
                    window.lineEquipmentStatusChart = null;
                }
            }
        } catch (error) {
            console.error("Error toggling equipment carousel:", error);
            this.showToast("Error switching equipment view", "error");
        }
    }

    /**
     * Navigate to next equipment line
     */
    nextEquipmentLine() {
        const toggle = document.getElementById("equipmentCarouselToggle");

        // If toggle is OFF, enable manual scrolling through lines
        if (!toggle || !toggle.checked) {
            this.enableManualLineNavigation();
            // Initialize line navigation if not already done
            if (!this.equipmentLines || !this.currentEquipmentLine) {
                this.currentEquipmentLine = "all";
                this.equipmentLines = [
                    "all",
                    "A",
                    "B",
                    "C",
                    "D",
                    "E",
                    "F",
                    "G",
                    "H",
                    "I",
                    "J",
                    "K",
                ];
            }
        }

        if (!this.equipmentLines || !this.currentEquipmentLine) return;

        const currentIndex = this.equipmentLines.indexOf(
            this.currentEquipmentLine
        );
        const nextIndex = (currentIndex + 1) % this.equipmentLines.length;
        const nextLine = this.equipmentLines[nextIndex];

        if (toggle && toggle.checked) {
            // Full carousel mode - switch to line view
            this.switchToEquipmentLine(nextLine);
        } else {
            // Manual navigation mode - just update display and indicators
            this.updateLineIndicators(nextLine);
        }
    }

    /**
     * Navigate to previous equipment line
     */
    prevEquipmentLine() {
        const toggle = document.getElementById("equipmentCarouselToggle");

        // If toggle is OFF, enable manual scrolling through lines
        if (!toggle || !toggle.checked) {
            this.enableManualLineNavigation();
            // Initialize line navigation if not already done
            if (!this.equipmentLines || !this.currentEquipmentLine) {
                this.currentEquipmentLine = "all";
                this.equipmentLines = [
                    "all",
                    "A",
                    "B",
                    "C",
                    "D",
                    "E",
                    "F",
                    "G",
                    "H",
                    "I",
                    "J",
                    "K",
                ];
            }
        }

        if (!this.equipmentLines || !this.currentEquipmentLine) return;

        const currentIndex = this.equipmentLines.indexOf(
            this.currentEquipmentLine
        );
        const prevIndex =
            currentIndex === 0
                ? this.equipmentLines.length - 1
                : currentIndex - 1;
        const prevLine = this.equipmentLines[prevIndex];

        if (toggle && toggle.checked) {
            // Full carousel mode - switch to line view
            this.switchToEquipmentLine(prevLine);
        } else {
            // Manual navigation mode - just update display and indicators
            this.updateLineIndicators(prevLine);
        }
    }

    /**
     * Switch to specific equipment line
     */
    switchToEquipmentLine(line) {
        this.currentEquipmentLine = line;

        // Update display
        const currentLineDisplay =
            document.getElementById("currentLineDisplay");
        const currentLineIndicator = document.getElementById(
            "equipmentCurrentLine"
        );

        if (currentLineDisplay) currentLineDisplay.textContent = line;
        if (currentLineIndicator)
            currentLineIndicator.textContent = `Line ${line}`;

        // Load line data
        this.loadLineEquipmentData(line);
    }

    /**
     * Enable manual line navigation when toggle is OFF
     */
    enableManualLineNavigation() {
        const prevBtn = document.getElementById("prevLineBtn");
        const nextBtn = document.getElementById("nextLineBtn");

        // Enable navigation buttons
        if (prevBtn) prevBtn.disabled = false;
        if (nextBtn) nextBtn.disabled = false;
    }

    /**
     * Update line indicators for manual navigation mode
     */
    updateLineIndicators(line) {
        this.currentEquipmentLine = line;

        // Handle "all" lines vs specific line
        const overallView = document.getElementById("equipment-overall-view");
        const lineView = document.getElementById("equipment-line-view");
        const currentLineDisplay =
            document.getElementById("currentLineDisplay");
        const currentLineIndicator = document.getElementById(
            "equipmentCurrentLine"
        );

        if (line === "all") {
            // Switch to overall view for "All" lines
            if (overallView && lineView) {
                lineView.classList.remove("active");
                overallView.classList.add("active");
            }

            // Update display indicators
            if (currentLineDisplay) currentLineDisplay.textContent = "All";
            if (currentLineIndicator)
                currentLineIndicator.textContent = "Overall";

            // No need to load specific line stats - overall view shows global stats
        } else {
            // Switch to line-specific view when manually navigating to a specific line
            if (overallView && lineView) {
                overallView.classList.remove("active");
                lineView.classList.add("active");
            }

            // Update display indicators
            if (currentLineDisplay) currentLineDisplay.textContent = line;
            if (currentLineIndicator)
                currentLineIndicator.textContent = `Line ${line}`;

            // Load equipment statistics for the selected line in manual mode
            this.loadLineEquipmentStats(line);
        }
    }

    /**
     * Load equipment statistics for a specific line
     */
    async loadLineEquipmentStats(line) {
        try {
            // Build request parameters
            const params = new URLSearchParams({
                line: line,
                dashboard_date:
                    document.getElementById("dashboard_date")?.value ||
                    new Date().toISOString().split("T")[0],
                dashboard_shift:
                    document.getElementById("dashboard_shift")?.value || "all",
                dashboard_cutoff:
                    document.getElementById("dashboard_cutoff")?.value || "all",
                dashboard_work_type:
                    document.getElementById("dashboard_work_type")?.value ||
                    "all",
                dashboard_alloc_type:
                    document.getElementById("dashboard_alloc_type")?.value ||
                    "all",
            });

            // Fetch line equipment statistics
            const response = await fetch(
                `/api/line-equipment-stats?${params.toString()}`
            );
            const result = await response.json();

            if (result.success) {
                // Update the equipment statistics display
                this.updateEquipmentStatsDisplay(result.stats);
            } else {
                console.warn(
                    "Failed to load line equipment stats:",
                    result.message
                );
                // Fallback to overall stats if line-specific stats fail
                this.resetToOverallStats();
            }
        } catch (error) {
            console.error("Error loading line equipment stats:", error);
            // Fallback to overall stats on error
            this.resetToOverallStats();
        }
    }

    /**
     * Update equipment statistics display with line-specific data
     */
    updateEquipmentStatsDisplay(stats) {
        // Check if we're in line-specific view or overall view
        // Check if the line view is currently active
        const lineView = document.getElementById("equipment-line-view");
        const isLineView = lineView && lineView.classList.contains("active");

        if (isLineView) {
            // Update line-specific elements
            const lineTotalEquipmentEl =
                document.getElementById("lineTotalEquipment");
            if (lineTotalEquipmentEl) {
                lineTotalEquipmentEl.textContent = stats.total_equipment || 0;
            }

            const lineEquipmentWithOngoingEl = document.getElementById(
                "lineEquipmentWithOngoing"
            );
            if (lineEquipmentWithOngoingEl) {
                lineEquipmentWithOngoingEl.textContent =
                    stats.equipment_with_ongoing || 0;
            }

            const lineIdleEquipmentEl =
                document.getElementById("lineIdleEquipment");
            if (lineIdleEquipmentEl) {
                const idleCount =
                    (stats.total_equipment || 0) -
                    (stats.equipment_with_ongoing || 0);
                lineIdleEquipmentEl.textContent = idleCount;
            }

            // Update line-specific progress bars
            this.updateLineEquipmentProgressBars(stats);

            // Update or create the line equipment chart with the new percentage
            const equipmentPercentage =
                stats.equipment_percentage ||
                ((stats.equipment_with_ongoing || 0) /
                    (stats.total_equipment || 1)) *
                    100;
            this.updateLineEquipmentChart(equipmentPercentage);
        } else {
            // Update overall view elements
            const totalEquipmentEl = document.getElementById("totalEquipment");
            if (totalEquipmentEl) {
                totalEquipmentEl.textContent = stats.total_equipment || 0;
            }

            const equipmentWithOngoingEl = document.getElementById(
                "equipmentWithOngoing"
            );
            if (equipmentWithOngoingEl) {
                equipmentWithOngoingEl.textContent =
                    stats.equipment_with_ongoing || 0;
            }

            const idleEquipmentEl = document.getElementById("idleEquipment");
            if (idleEquipmentEl) {
                const idleCount =
                    (stats.total_equipment || 0) -
                    (stats.equipment_with_ongoing || 0);
                idleEquipmentEl.textContent = idleCount;
            }

            // Update overall progress bars
            this.updateEquipmentProgressBars(stats);
        }
    }

    /**
     * Update equipment progress bars
     */
    updateEquipmentProgressBars(stats) {
        const totalEquipment = stats.total_equipment || 1;

        // Update ongoing progress bar
        const ongoingProgressBar = document.querySelector(
            ".equipment-ongoing-progress"
        );
        if (ongoingProgressBar) {
            const ongoingPercentage =
                ((stats.equipment_with_ongoing || 0) / totalEquipment) * 100;
            ongoingProgressBar.style.width = ongoingPercentage + "%";
        }

        // Update idle progress bar
        const idleProgressBar = document.querySelector(
            ".equipment-idle-progress"
        );
        if (idleProgressBar) {
            const idleCount =
                totalEquipment - (stats.equipment_with_ongoing || 0);
            const idlePercentage = (idleCount / totalEquipment) * 100;
            idleProgressBar.style.width = idlePercentage + "%";
        }
    }

    /**
     * Update line-specific equipment progress bars
     */
    updateLineEquipmentProgressBars(stats) {
        const totalEquipment = stats.total_equipment || 1;

        // Update line ongoing progress bar
        const lineOngoingProgressBar = document.querySelector(
            ".line-equipment-ongoing-progress"
        );
        if (lineOngoingProgressBar) {
            const ongoingPercentage =
                ((stats.equipment_with_ongoing || 0) / totalEquipment) * 100;
            lineOngoingProgressBar.style.width = ongoingPercentage + "%";
        }

        // Update line idle progress bar
        const lineIdleProgressBar = document.querySelector(
            ".line-equipment-idle-progress"
        );
        if (lineIdleProgressBar) {
            const idleCount =
                totalEquipment - (stats.equipment_with_ongoing || 0);
            const idlePercentage = (idleCount / totalEquipment) * 100;
            lineIdleProgressBar.style.width = idlePercentage + "%";
        }
    }

    /**
     * Reset to overall statistics when line-specific data is unavailable
     */
    resetToOverallStats() {
        const dashboardStats = window.dashboardStats || {};

        const stats = {
            total_equipment: dashboardStats.total_equipment || 0,
            equipment_with_ongoing: dashboardStats.equipment_with_ongoing || 0,
        };

        this.updateEquipmentStatsDisplay(stats);
    }

    /**
     * Load equipment data for specific line
     */
    async loadLineEquipmentData(line) {
        if (!line) {
            console.error("No line specified for equipment data");
            return;
        }

        try {
            // Show loading state
            const container = document.getElementById("equipment-line-view");
            if (container) container.classList.add("equipment-loading");

            // Build request parameters using current dashboard filters
            const params = new URLSearchParams({
                line: line,
                dashboard_date:
                    document.getElementById("dashboard_date")?.value ||
                    new Date().toISOString().split("T")[0],
                dashboard_shift:
                    document.getElementById("dashboard_shift")?.value || "all",
                dashboard_cutoff:
                    document.getElementById("dashboard_cutoff")?.value || "all",
                dashboard_work_type:
                    document.getElementById("dashboard_work_type")?.value ||
                    "all",
                dashboard_alloc_type:
                    document.getElementById("dashboard_alloc_type")?.value ||
                    "all",
            });

            const response = await fetch(
                `/api/line-equipment-stats?${params.toString()}`,
                {
                    method: "GET",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN":
                            document
                                .querySelector('meta[name="csrf-token"]')
                                ?.getAttribute("content") || "",
                    },
                }
            );

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                this.updateLineEquipmentDisplay(data.stats);
            } else {
                console.error("API returned error:", data.message);
                this.showToast(
                    `Failed to load line ${line} equipment data: ${data.message}`,
                    "error"
                );
            }
        } catch (error) {
            console.error("Error loading line equipment data:", error);
            this.showToast(
                `Error loading line ${line} equipment data`,
                "error"
            );
        } finally {
            // Hide loading state
            const container = document.getElementById("equipment-line-view");
            if (container) container.classList.remove("equipment-loading");
        }
    }

    /**
     * Update line equipment display with new data
     */
    updateLineEquipmentDisplay(stats) {
        // Update line equipment counts
        const lineTotalEquipmentEl =
            document.getElementById("lineTotalEquipment");
        const lineEquipmentWithOngoingEl = document.getElementById(
            "lineEquipmentWithOngoing"
        );
        const lineIdleEquipmentEl =
            document.getElementById("lineIdleEquipment");

        if (lineTotalEquipmentEl)
            lineTotalEquipmentEl.textContent = stats.total_equipment || 0;
        if (lineEquipmentWithOngoingEl)
            lineEquipmentWithOngoingEl.textContent =
                stats.equipment_with_ongoing || 0;
        if (lineIdleEquipmentEl)
            lineIdleEquipmentEl.textContent = stats.idle_equipment || 0;

        // Update progress bars
        const ongoingProgressBar = document.querySelector(
            ".line-equipment-ongoing-progress"
        );
        const idleProgressBar = document.querySelector(
            ".line-equipment-idle-progress"
        );
        const totalEquipment = stats.total_equipment || 1;

        if (ongoingProgressBar) {
            const ongoingPercentage =
                ((stats.equipment_with_ongoing || 0) / totalEquipment) * 100;
            ongoingProgressBar.style.width = ongoingPercentage + "%";
            ongoingProgressBar.style.backgroundColor = "rgba(50, 212, 132, 1)";
        }

        if (idleProgressBar) {
            const idlePercentage =
                ((stats.idle_equipment || 0) / totalEquipment) * 100;
            idleProgressBar.style.width = idlePercentage + "%";
        }

        // Update or create line equipment chart
        this.updateLineEquipmentChart(stats.equipment_percentage || 0);
    }

    /**
     * Update or create line equipment status chart
     */
    updateLineEquipmentChart(percentage) {
        const chartContainer = document.getElementById(
            "line-equipment-status-chart"
        );
        if (!chartContainer) {
            console.error("Line equipment chart container not found");
            return;
        }

        if (typeof ApexCharts === "undefined") {
            console.error("ApexCharts not available");
            return;
        }

        // If chart already exists, just update the series data
        if (
            window.lineEquipmentStatusChart &&
            window.lineEquipmentStatusChart.updateSeries
        ) {
            try {
                window.lineEquipmentStatusChart.updateSeries([percentage || 0]);
                return;
            } catch (error) {
                console.warn(
                    "Error updating line chart series, recreating:",
                    error
                );
                // Fall through to recreate chart
            }
        }

        // Create new chart if it doesn't exist or update failed
        this.createLineEquipmentChart(percentage || 0, chartContainer);
    }

    /**
     * Create new line equipment status chart
     */
    createLineEquipmentChart(percentage, chartContainer) {
        // Destroy existing chart if it exists
        if (window.lineEquipmentStatusChart) {
            try {
                window.lineEquipmentStatusChart.destroy();
                window.lineEquipmentStatusChart = null;
            } catch (error) {
                console.warn("Error destroying existing line chart:", error);
            }
        }

        // Clear the container
        chartContainer.innerHTML = "";

        try {
            const gradientOptions = {
                series: [percentage],
                chart: {
                    height: 300,
                    type: "radialBar",
                    toolbar: {
                        show: false,
                    },
                    animations: {
                        enabled: true,
                        easing: "easeinout",
                        speed: 800,
                    },
                },
                plotOptions: {
                    radialBar: {
                        startAngle: -135,
                        endAngle: 225,
                        hollow: {
                            margin: 0,
                            size: "70%",
                            background: "transparent",
                            dropShadow: {
                                enabled: true,
                                top: 3,
                                left: 0,
                                blur: 4,
                                opacity: 0.24,
                            },
                        },
                        track: {
                            background: "rgba(255, 255, 255, 0.95)",
                            strokeWidth: "70%",
                            margin: 0,
                            dropShadow: {
                                enabled: true,
                                top: -3,
                                left: 0,
                                blur: 4,
                                opacity: 0.35,
                            },
                        },
                        dataLabels: {
                            show: true,
                            name: {
                                offsetY: -10,
                                show: true,
                                color: "#888",
                                fontSize: "17px",
                                fontWeight: "normal",
                            },
                            value: {
                                formatter: function (val) {
                                    return parseInt(val) + "%";
                                },
                                color: "#111",
                                fontSize: "36px",
                                show: true,
                                fontWeight: "bold",
                            },
                        },
                    },
                },
                fill: {
                    type: "gradient",
                    gradient: {
                        shade: "light",
                        type: "horizontal",
                        shadeIntensity: 0.4,
                        gradientToColors: ["#00E396"],
                        inverseColors: false,
                        opacityFrom: 0.9,
                        opacityTo: 0.7,
                        stops: [0, 100],
                    },
                },
                stroke: {
                    lineCap: "round",
                },
                labels: ["UTILIZED EQP"],
                colors: ["#00D4FF"],
            };
            window.lineEquipmentStatusChart = new ApexCharts(
                chartContainer,
                gradientOptions
            );

            // Wait for container to be visible before rendering
            setTimeout(() => {
                if (
                    window.lineEquipmentStatusChart &&
                    chartContainer.offsetParent !== null
                ) {
                    window.lineEquipmentStatusChart.render().catch((error) => {
                        console.error(
                            "Error rendering line equipment chart:",
                            error
                        );
                    });
                }
            }, 100);
        } catch (error) {
            console.error("Error creating line equipment chart:", error);
            this.showToast("Error creating equipment chart", "error");
        }
    }

    /**
     * Start automatic carousel cycling
     */
    startEquipmentCarousel() {
        // Clear any existing interval first
        this.stopEquipmentCarousel();

        // Auto-cycle through lines every 20 seconds
        this.equipmentCarouselInterval = setInterval(() => {
            if (this.currentEquipmentLine && this.equipmentLines) {
                this.nextEquipmentLine();
            }
        }, 20000);
    }

    /**
     * Stop automatic carousel cycling
     */
    stopEquipmentCarousel() {
        if (this.equipmentCarouselInterval) {
            clearInterval(this.equipmentCarouselInterval);
            this.equipmentCarouselInterval = null;
        }
    }

    /**
     * Initialize equipment carousel controls
     */
    initializeEquipmentCarouselControls() {
        // Initialize equipment carousel state for manual navigation (toggle starts as OFF)
        this.equipmentLines = [
            "all",
            "A",
            "B",
            "C",
            "D",
            "E",
            "F",
            "G",
            "H",
            "I",
            "J",
            "K",
        ];
        this.currentEquipmentLine = "all";
        this.equipmentCarouselInterval = null;

        // Enable navigation buttons for manual navigation (toggle starts as OFF)
        const prevBtn = document.getElementById("prevLineBtn");
        const nextBtn = document.getElementById("nextLineBtn");
        const currentLineDisplay =
            document.getElementById("currentLineDisplay");
        const currentLineIndicator = document.getElementById(
            "equipmentCurrentLine"
        );

        if (prevBtn) prevBtn.disabled = false;
        if (nextBtn) nextBtn.disabled = false;
        if (currentLineDisplay) currentLineDisplay.textContent = "All";
        if (currentLineIndicator) currentLineIndicator.textContent = "Overall";
    }

    // ==================== SIMPLE FILTER PERSISTENCE ====================

    /**
     * Update URL with current filter values for simple persistence
     */
    updateURLWithFilters(date, shift, cutoff, workType, allocType) {
        try {
            const url = new URL(window.location);
            url.searchParams.set("dashboard_date", date);
            url.searchParams.set("dashboard_shift", shift);
            url.searchParams.set("dashboard_cutoff", cutoff);
            url.searchParams.set("dashboard_work_type", workType);
            url.searchParams.set("dashboard_alloc_type", allocType);

            // Update URL without page refresh
            window.history.replaceState({}, "", url);
        } catch (error) {
            // Silently handle any URL update errors
        }
    }
}

// Initialize Production Dashboard when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
    window.productionDashboard = new ProductionDashboard();
});

// Make Production Dashboard globally available
window.ProductionDashboard = ProductionDashboard;

// ==================== GLOBAL DASHBOARD FUNCTIONS ====================

/**
 * View All Alerts function
 * Opens a modal or navigates to alerts page
 */
window.viewAllAlerts = function () {
    // For now, show a modal with alert details
    // In a real application, this would redirect to an alerts management page
    Swal.fire({
        title: "Production Alerts",
        html: `
            <div class="text-start">
                <div class="alert alert-danger d-flex align-items-start mb-2">
                    <i class="fas fa-exclamation-circle text-danger me-2 mt-1"></i>
                    <div class="flex-grow-1">
                        <div class="fw-medium small">Line D Performance Drop</div>
                        <small class="text-muted">Efficiency below 80% - 5 minutes ago</small>
                        <div class="text-muted small mt-1">Current efficiency: 76.2%</div>
                    </div>
                </div>
                
                <div class="alert alert-warning d-flex align-items-start mb-2">
                    <i class="fas fa-clock text-warning me-2 mt-1"></i>
                    <div class="flex-grow-1">
                        <div class="fw-medium small">Shift Target Behind Schedule</div>
                        <small class="text-muted">15% behind target - 12 minutes ago</small>
                        <div class="text-muted small mt-1">Current progress: 85%</div>
                    </div>
                </div>
                
                <div class="alert alert-info d-flex align-items-start mb-2">
                    <i class="fas fa-lightbulb text-info me-2 mt-1"></i>
                    <div class="flex-grow-1">
                        <div class="fw-medium small">Optimization Suggestion</div>
                        <small class="text-muted">Line F can increase speed - 18 minutes ago</small>
                        <div class="text-muted small mt-1">Suggested speed increase: 5%</div>
                    </div>
                </div>
                
                <div class="alert alert-warning d-flex align-items-start mb-2">
                    <i class="fas fa-exclamation-triangle text-warning me-2 mt-1"></i>
                    <div class="flex-grow-1">
                        <div class="fw-medium small">Equipment Maintenance Due</div>
                        <small class="text-muted">Line H maintenance overdue - 1 hour ago</small>
                        <div class="text-muted small mt-1">Last maintenance: 7 days ago</div>
                    </div>
                </div>
                
                <div class="alert alert-success d-flex align-items-start mb-0">
                    <i class="fas fa-check-circle text-success me-2 mt-1"></i>
                    <div class="flex-grow-1">
                        <div class="fw-medium small">Target Achievement</div>
                        <small class="text-muted">Line B exceeded target - 2 hours ago</small>
                        <div class="text-muted small mt-1">Achievement: 104.8%</div>
                    </div>
                </div>
            </div>
        `,
        icon: null,
        confirmButtonText: "Close",
        confirmButtonColor: "#6366f1",
        customClass: {
            popup: "swal-wide",
        },
        showCloseButton: true,
        width: 600,
    });
};

// ==================== PRODUCTION INSIGHTS FUNCTIONS ====================

/**
 * Production Insights Manager
 * Handles the 3rd row data: Previous Shift, Line Performance, and Live Analysis
 */
class ProductionInsights {
    constructor() {
        this.updateInterval = null;
        this.init();
    }

    init() {
        this.updateCurrentTimeDisplay();
        this.fetchPreviousShiftData();
        this.fetchLinePerformanceData();
        this.startLiveUpdates();

        // Update time display every minute
        setInterval(() => {
            this.updateCurrentTimeDisplay();
        }, 60000);
    }

    updateCurrentTimeDisplay() {
        const now = new Date();
        const manilaTime = new Date(
            now.toLocaleString("en-US", { timeZone: "Asia/Manila" })
        );

        const dateStr = manilaTime.toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
        });

        const timeStr = manilaTime.toLocaleTimeString("en-US", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
        });

        const hours = manilaTime.getHours();
        const shiftType =
            hours >= 7 && hours < 19 ? "Day Shift" : "Night Shift";

        const displayElement = document.getElementById("current-time-display");
        if (displayElement) {
            displayElement.textContent = `${dateStr} ${timeStr} - ${shiftType} Progress`;
        }

        const timeMarker = document.getElementById("current-time-marker");
        if (timeMarker) {
            timeMarker.textContent = timeStr;
        }

        // Update progress based on time
        this.updateShiftProgress(hours);
    }

    updateShiftProgress(currentHour) {
        let progressPercent = 0;

        if (currentHour >= 7 && currentHour < 19) {
            // Day shift: 07:00 - 19:00 (12 hours)
            const shiftMinutes =
                (currentHour - 7) * 60 + new Date().getMinutes();
            progressPercent = Math.min((shiftMinutes / (12 * 60)) * 100, 100);
        } else {
            // Night shift: 19:00 - 07:00 (12 hours)
            let shiftMinutes;
            if (currentHour >= 19) {
                shiftMinutes =
                    (currentHour - 19) * 60 + new Date().getMinutes();
            } else {
                shiftMinutes = (currentHour + 5) * 60 + new Date().getMinutes(); // +5 because 24-19=5
            }
            progressPercent = Math.min((shiftMinutes / (12 * 60)) * 100, 100);
        }

        const progressBar = document.getElementById("current-progress-bar");
        const progressText = document.getElementById("current-progress-text");

        if (progressBar) {
            progressBar.style.width = `${progressPercent}%`;
        }

        if (progressText) {
            progressText.textContent = `${progressPercent.toFixed(1)}%`;
        }
    }

    async fetchPreviousShiftData() {
        try {
            // Simulate API call for previous night shift data
            const previousShiftData = {
                achievement_percent: 87.1,
                target_pcs: "271.7M",
                actual_pcs: "236.4M",
                total_hours: "11.98h",
                lines_active: "9/11",
                status: "Below Target",
                gap_percent: 12.9,
            };

            this.updatePreviousShiftUI(previousShiftData);
        } catch (error) {
            // Error fetching previous shift data
        }
    }

    updatePreviousShiftUI(data) {
        // Update achievement percentage and circle
        const achievementPercent = document.getElementById(
            "night-achievement-percent"
        );
        const achievementCircle = document.getElementById("achievement-circle");

        if (achievementPercent) {
            achievementPercent.textContent = `${data.achievement_percent}%`;
        }

        if (achievementCircle) {
            const dashOffset = 314 - (314 * data.achievement_percent) / 100;
            achievementCircle.style.strokeDashoffset = dashOffset;
        }

        // Update metrics
        this.updateElementText("night-target-pcs", data.target_pcs);
        this.updateElementText("night-actual-pcs", data.actual_pcs);
        this.updateElementText("night-total-hours", data.total_hours);
        this.updateElementText("night-lines-active", data.lines_active);
        this.updateElementText("night-status-text", data.status);
        this.updateElementText(
            "night-gap-text",
            `${data.gap_percent}% below target capacity`
        );
    }

    async fetchLinePerformanceData() {
        try {
            // Simulate API call for line performance data
            const lineData = {
                top_performers: [
                    {
                        line: "A",
                        area: "Area 1",
                        percentage: 105.2,
                        pcs: "28.5M",
                    },
                    {
                        line: "H",
                        area: "Area 2",
                        percentage: 102.8,
                        pcs: "31.2M",
                    },
                ],
                average_performers: [
                    {
                        line: "B",
                        area: "Area 1",
                        percentage: 94.6,
                        pcs: "19.8M",
                    },
                    {
                        line: "C",
                        area: "Area 1",
                        percentage: 91.3,
                        pcs: "21.4M",
                    },
                ],
                underperformers: [
                    {
                        line: "D",
                        area: "Area 2",
                        percentage: 78.4,
                        pcs: "15.7M",
                    },
                    {
                        line: "G",
                        area: "Area 3",
                        percentage: 72.1,
                        pcs: "13.8M",
                    },
                ],
                area_performance: {
                    "area-1-perf": 98.5,
                    "area-2-perf": 89.2,
                    "area-3-perf": 75.8,
                },
            };

            this.updateLinePerformanceUI(lineData);
        } catch (error) {
            // Error fetching line performance data
        }
    }

    updateLinePerformanceUI(data) {
        // Update area performance
        Object.entries(data.area_performance).forEach(([elementId, value]) => {
            this.updateElementText(elementId, `${value}%`);
        });

        // Update performer counts
        this.updateElementText(
            "top-performers-count",
            data.top_performers.length
        );
        this.updateElementText(
            "avg-performers-count",
            data.average_performers.length
        );
        this.updateElementText(
            "underperform-count",
            data.underperformers.length
        );
    }

    startLiveUpdates() {
        // DISABLED: This was causing conflicts with the real Live Performance Monitor
        // The real dashboard gets updates through the main ProductionDashboard class auto-refresh
        // console.log('ProductionInsights startLiveUpdates disabled to prevent conflicts with Live Performance Monitor');
    }

    async updateLiveMetrics() {
        // DISABLED: This was conflicting with the real Live Performance Monitor
        // The actual Live Performance Monitor gets its data from the server via updateCurrentPerformanceMonitor()
        // and doesn't need this simulated/hardcoded data that was causing confusion
        // console.log('ProductionInsights updateLiveMetrics disabled to prevent conflicts with Live Performance Monitor');
    }

    updateElementText(elementId, text) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = text;
        }
    }

    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
    }
}

// Global functions for button handlers
window.showDetailedReport = function () {
    Swal.fire({
        title: "Production Detailed Report",
        text: "This would open a detailed production report with charts and analytics.",
        icon: "info",
        confirmButtonText: "Close",
        confirmButtonColor: "#6366f1",
    });
};

window.exportReport = function () {
    Swal.fire({
        title: "Export Report",
        text: "Report export initiated. You will receive an email when ready.",
        icon: "success",
        timer: 3000,
        showConfirmButton: false,
    });
};

window.setTrendView = function (viewType) {
    // Update the dropdown button text and active state
    const dropdownItems = document.querySelectorAll(
        ".dropdown-menu .dropdown-item"
    );
    dropdownItems.forEach((item) => {
        item.classList.remove("active");
        if (item.textContent.toLowerCase().includes(viewType)) {
            item.classList.add("active");
        }
    });
};

// Initialize Production Insights when DOM is ready
document.addEventListener("DOMContentLoaded", () => {
    if (document.getElementById("production-insights-row")) {
        window.productionInsights = new ProductionInsights();
    }
});

// Clean up on page unload
window.addEventListener("beforeunload", () => {
    if (window.productionInsights) {
        window.productionInsights.destroy();
    }
});
