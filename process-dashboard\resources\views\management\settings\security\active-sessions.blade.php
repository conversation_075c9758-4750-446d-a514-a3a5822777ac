<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Active Sessions - Security Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container-fluid py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4><i class="fas fa-users me-2"></i>Active Sessions</h4>
            <button type="button" class="btn btn-outline-secondary" onclick="window.close()">
                <i class="fas fa-times me-2"></i>Close
            </button>
        </div>

        @if($activeSessions->count() > 0)
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>User</th>
                            <th>IP Address</th>
                            <th>Last Activity</th>
                            <th>User Agent</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($activeSessions as $session)
                            <tr class="{{ $session->is_current ? 'table-success' : '' }}">
                                <td>
                                    @if($session->user_id)
                                        <strong>{{ $session->name ?? 'Unknown' }}</strong><br>
                                        <small class="text-muted">{{ $session->email ?? 'No email' }}</small>
                                    @else
                                        <span class="text-muted">Guest Session</span>
                                    @endif
                                </td>
                                <td>
                                    <code>{{ $session->ip_address ?? 'Unknown' }}</code>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ $session->last_activity_human }}</span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ Str::limit($session->user_agent ?? 'Unknown', 50) }}
                                    </small>
                                </td>
                                <td>
                                    @if($session->is_current)
                                        <span class="badge bg-success">
                                            <i class="fas fa-user me-1"></i>Current Session
                                        </span>
                                    @else
                                        <span class="badge bg-info">
                                            <i class="fas fa-circle me-1"></i>Active
                                        </span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <div class="alert alert-info mt-4">
                <i class="fas fa-info-circle me-2"></i>
                <strong>{{ $activeSessions->count() }}</strong> active sessions found (last 30 minutes)
            </div>
        @else
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                No active sessions found in the last 30 minutes.
            </div>
        @endif

        <div class="mt-4">
            <button type="button" class="btn btn-outline-primary" onclick="window.location.reload()">
                <i class="fas fa-sync me-2"></i>Refresh
            </button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh every 30 seconds
        setInterval(function() {
            window.location.reload();
        }, 30000);
    </script>
</body>
</html>