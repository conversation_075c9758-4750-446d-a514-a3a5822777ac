<x-app-layout>
    <x-slot name="header">
        Equipment Details
    </x-slot>

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header py-2" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); border: none;">
                    <h6 class="mb-0 text-white">
                        <i class="fas fa-cogs me-2"></i>Equipment Details - {{ $equipment->eqp_no }}
                    </h6>
                </div>
                <div class="card-body py-3">
                    <!-- Basic Information & Specifications -->
                    <div class="row g-2 mb-3">
                        <!-- Basic Info -->
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header py-2 bg-primary text-white">
                                    <small class="fw-bold"><i class="fas fa-info-circle me-1"></i>Basic Information</small>
                                </div>
                                <div class="card-body py-2">
                                    <div class="row g-2">
                                        <div class="col-6">
                                            <small class="text-muted">Equipment Number:</small><br>
                                            <strong class="text-primary">{{ $equipment->eqp_no }}</strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Equipment Line:</small><br>
                                            <strong class="text-primary">{{ $equipment->eqp_line }}</strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Equipment Area:</small><br>
                                            <strong class="text-dark">{{ $equipment->eqp_area ?? 'N/A' }}</strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Equipment Type:</small><br>
                                            <strong class="text-dark">{{ $equipment->eqp_type }}</strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Equipment Maker:</small><br>
                                            <strong class="text-dark">{{ $equipment->eqp_maker }}</strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Size:</small><br>
                                            <strong class="text-dark">{{ $equipment->size }}</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Specifications & Status -->
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header py-2 bg-secondary text-white">
                                    <small class="fw-bold"><i class="fas fa-cogs me-1"></i>Specifications & Status</small>
                                </div>
                                <div class="card-body py-2">
                                    <div class="row g-2">
                                        <div class="col-6">
                                            <small class="text-muted">CAM Class:</small><br>
                                            <strong class="text-dark">{{ $equipment->cam_class ?? 'N/A' }}</strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Inspection Type:</small><br>
                                            <strong class="text-dark">{{ $equipment->insp_type ?? 'N/A' }}</strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Linear Type:</small><br>
                                            <strong class="text-dark">{{ $equipment->linear_type ?? 'N/A' }}</strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Allocation Type:</small><br>
                                            <strong class="text-dark">{{ $equipment->alloc_type ?? 'N/A' }}</strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Equipment Status:</small><br>
                                            <span class="badge {{ $equipment->eqp_status === 'OPERATIONAL' ? 'bg-success' : 'bg-secondary' }} fs-6">
                                                {{ $equipment->eqp_status }}
                                            </span>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Ongoing Lot:</small><br>
                                            <strong class="text-{{ $equipment->ongoing_lot ? 'success' : 'muted' }}">{{ $equipment->ongoing_lot ?? 'None' }}</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Reference Data & Performance Parameters -->
                    <div class="row g-2 mb-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header py-2 bg-success text-white">
                                    <small class="fw-bold"><i class="fas fa-tachometer-alt me-1"></i>Performance Parameters & Reference Data</small>
                                </div>
                                <div class="card-body py-2">
                                    <div class="row g-2">
                                        <div class="col-md-2">
                                            <small class="text-muted">Loading Speed:</small><br>
                                            <strong class="text-success">{{ number_format($equipment->loading_speed) }}</strong>
                                            <small class="text-muted">units/min</small>
                                        </div>
                                        <div class="col-md-2">
                                            <small class="text-muted">Operation Time:</small><br>
                                            <strong class="text-success">{{ number_format($equipment->operation_time) }}</strong>
                                            <small class="text-muted">minutes</small>
                                        </div>
                                        <div class="col-md-2">
                                            <small class="text-muted">Equipment OEE:</small><br>
                                            <strong class="text-dark">{{ number_format($equipment->eqp_oee, 4) }}</strong>
                                        </div>
                                        <div class="col-md-2">
                                            <small class="text-muted">Passing Rate:</small><br>
                                            <strong class="text-dark">{{ number_format($equipment->eqp_passing, 4) }}</strong>
                                        </div>
                                        <div class="col-md-2">
                                            <small class="text-muted">Yield Rate:</small><br>
                                            <strong class="text-dark">{{ number_format($equipment->eqp_yield, 4) }}</strong>
                                        </div>
                                        <div class="col-md-2">
                                            <small class="text-muted">Modified By:</small><br>
                                            <strong class="text-dark">{{ $equipment->modified_by ?? 'System' }}</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Calculated Capacities -->
                    <div class="row g-2 mb-3">
                        <div class="col-md-4">
                            <div class="card border-info h-100">
                                <div class="card-header bg-info text-white py-2">
                                    <small class="fw-bold"><i class="fas fa-chart-line me-1"></i>Ideal Capacity</small>
                                </div>
                                <div class="card-body py-2 text-center">
                                    <h4 class="text-info mb-1">{{ number_format($equipment->ideal_capa ?? 0) }}</h4>
                                    <small class="text-muted">units/day</small><br>
                                    <small class="text-muted fst-italic">{{ number_format($equipment->loading_speed) }} × {{ number_format($equipment->operation_time) }}</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card border-primary h-100">
                                <div class="card-header bg-primary text-white py-2">
                                    <small class="fw-bold"><i class="fas fa-cogs me-1"></i>OEE Capacity</small>
                                </div>
                                <div class="card-body py-2 text-center">
                                    <h4 class="text-primary mb-1">{{ number_format($equipment->oee_capa ?? 0) }}</h4>
                                    <small class="text-muted">units/day</small><br>
                                    <small class="text-muted fst-italic">with OEE {{ number_format($equipment->eqp_oee, 4) }}</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card border-success h-100">
                                <div class="card-header bg-success text-white py-2">
                                    <small class="fw-bold"><i class="fas fa-trophy me-1"></i>Output Capacity</small>
                                </div>
                                <div class="card-body py-2 text-center">
                                    <h4 class="text-success mb-1">{{ number_format($equipment->output_capa ?? 0) }}</h4>
                                    <small class="text-muted">units/day</small><br>
                                    <small class="text-muted fst-italic">with quality factors</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Calculation Logic -->
                    <div class="row g-2 mb-3">
                        <div class="col-12">
                            <div class="alert alert-info py-2 mb-0">
                                <div class="row">
                                    <div class="col-md-4">
                                        <small><strong>Ideal:</strong> loading_speed × operation_time</small>
                                    </div>
                                    <div class="col-md-4">
                                        <small><strong>OEE:</strong> loading_speed × eqp_oee × operation_time</small>
                                    </div>
                                    <div class="col-md-4">
                                        <small><strong>Output:</strong> loading_speed × eqp_oee × passing × yield × operation_time</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Additional Information & Actions -->
                    <div class="row g-2">
                        <div class="col-md-8">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <div class="row g-2">
                                        <div class="col-6">
                                            <small class="text-muted">Created:</small><br>
                                            <strong class="text-dark">{{ $equipment->created_at ? $equipment->created_at->format('Y-m-d H:i') : 'N/A' }}</strong>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Last Updated:</small><br>
                                            <strong class="text-dark">{{ $equipment->updated_at ? $equipment->updated_at->format('Y-m-d H:i') : 'N/A' }}</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light h-100">
                                <div class="card-body py-2 d-flex align-items-center justify-content-center">
                                    <div class="d-flex gap-2">
                                        @if(strtolower(Auth::user()->role ?? '') === 'admin' || strtolower(Auth::user()->role ?? '') === 'manager')
                                            <a href="{{ route('equipment.edit', $equipment) }}" class="btn btn-warning btn-sm">
                                                <i class="fas fa-edit me-1"></i>Edit
                                            </a>
                                            <form method="POST" action="{{ route('equipment.destroy', $equipment) }}" class="d-inline"
                                                  onsubmit="return confirm('Are you sure you want to delete this equipment?');">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-danger btn-sm">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        @endif
                                        <a href="{{ route('equipment.index') }}" class="btn btn-outline-secondary btn-sm">
                                            <i class="fas fa-arrow-left me-1"></i>Back
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
