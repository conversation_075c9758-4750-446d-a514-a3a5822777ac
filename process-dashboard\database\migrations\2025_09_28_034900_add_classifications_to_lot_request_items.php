<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('lot_request_items', function (Blueprint $table) {
            $table->string('size')->nullable()->after('equipment_code');
            $table->string('cam_class')->nullable()->after('size');
            $table->string('insp_type')->nullable()->after('cam_class');
            $table->string('alloc_type')->nullable()->after('insp_type');
        });
    }

    public function down(): void
    {
        Schema::table('lot_request_items', function (Blueprint $table) {
            $table->dropColumn(['size', 'cam_class', 'insp_type', 'alloc_type']);
        });
    }
};
