<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('equipment', function (Blueprint $table) {
            $table->id();
            $table->string('eqp_no')->unique();
            $table->string('eqp_line');
            $table->string('eqp_area');
            $table->string('eqp_maker');
            $table->string('cam_class');
            $table->string('insp_type');
            $table->string('linear_type');
            $table->string('eqp_type');
            $table->string('size');
            $table->string('alloc_type')->nullable();
            $table->string('eqp_status');
            $table->integer('loading_speed');
            $table->decimal('eqp_oee', 8, 2);
            $table->decimal('eqp_passing', 8, 2);
            $table->decimal('eqp_yield', 8, 2);
            $table->integer('operation_time');
            $table->bigInteger('ideal_capa');
            $table->bigInteger('oee_capa');
            $table->bigInteger('output_capa');
            $table->string('ongoing_lot')->nullable();
            $table->string('modified_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('equipment');
    }
};
