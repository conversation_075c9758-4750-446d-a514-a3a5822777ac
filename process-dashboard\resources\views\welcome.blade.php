<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="scroll-smooth">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Professional MLCC Visual Inspection Dashboard for streamlined production management control.">
    <title>{{ config('app.name') }} - MLCC Visual Inspection & Process Management</title>
    
    <!-- Local Fonts -->
    <link href="{{ asset('fonts/inter.css') }}" rel="stylesheet">
    
    <!-- Local Icons -->
    <link rel="stylesheet" href="{{ asset('libs/fontawesome/css/all.min.css') }}">
    
    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="antialiased">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top" id="navbar">
        <div class="container">
            <a class="navbar-brand" href="#home">
                <div class="brand-container">
                    <i class="fas fa-microchip brand-icon"></i>
                    <span class="brand-text">{{ config('app.name') }}</span>
                </div>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">Process Features</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#workflow">Inspection Workflow</a>
                    </li>
                </ul>
                <div class="navbar-nav">
                    @if (Route::has('login'))
                        @auth
                            <a href="{{ url('/dashboard') }}" class="btn btn-primary">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Go to Dashboard
                            </a>
                        @else
                            <a href="{{ route('login') }}" class="btn btn-outline-light me-2">Sign In</a>
                            @if (Route::has('register'))
                                <a href="{{ route('register') }}" class="btn btn-primary">
                                    Get Started
                                </a>
                            @endif
                        @endauth
                    @endif
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section" id="home">
        <div class="hero-background">
            <div class="hero-particles"></div>
        </div>
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <div class="hero-badge">
                            <i class="fas fa-microchip me-2"></i>
                            Visual Inspection Production Management System
                        </div>
                        <h1 class="hero-title">
                            <span class="text-gradient">MLCC Visual Inspection</span> <br>
                            Process Dashboard
                        </h1>
                        <p class="hero-subtitle">
                            
                        </p>
                        <div class="process-overview">
                            <div class="overview-item">
                                <div class="overview-icon"><i class="fas fa-network-wired"></i></div>
                                <div class="overview-text">
                                    <strong>Centralized Production Management</strong>
                                    <span>Unified dashboard for monitoring MLCC Visual Inspection process.</span>
                                </div>
                            </div>
                            <div class="overview-item">
                                <div class="overview-icon"><i class="fas fa-chart-line"></i></div>
                                <div class="overview-text">
                                    <strong>Accessible Data for Report Analysis</strong>
                                    <span>Instant access to inspection records and analytics for efficient reporting and decision-making.</span>
                                </div>
                            </div>
                            <div class="overview-item">
                                <div class="overview-icon"><i class="fas fa-shield-alt"></i></div>
                                <div class="overview-text">
                                    <strong>Secure and Reliable</strong>
                                    <span>Robust security and system reliability ensure data integrity and continuous operation.</span>
                                </div>
                            </div>
                        </div>
                        <div class="hero-actions">
                            @if (Route::has('login'))
                                @auth
                                    <a href="{{ url('/dashboard') }}" class="btn btn-primary btn-lg">
                                        <i class="fas fa-tachometer-alt me-2"></i>
                                        Access Dashboard
                                    </a>
                                @else
                                    <a href="{{ route('login') }}" class="btn btn-primary btn-lg">
                                        <i class="fas fa-sign-in-alt me-2"></i>
                                        Access System
                                    </a>
                                    @if (Route::has('register'))
                                        <a href="#features" class="btn btn-outline-white btn-lg">
                                            <i class="fas fa-info-circle me-2"></i>
                                            Learn More
                                        </a>
                                    @endif
                                @endauth
                            @endif
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-visual">
                        <div class="dashboard-mockup">
                            <div class="mockup-header">
                                <div class="mockup-controls">
                                    <span class="control red"></span>
                                    <span class="control yellow"></span>
                                    <span class="control green"></span>
                                </div>
                            </div>
                            <div class="mockup-content">
                                <div class="inspection-grid">
                                    <div class="inspection-item passed">
                                        <div class="item-status"><i class="fas fa-check"></i></div>
                                        <div class="item-info">
                                            <div class="item-id">Machine Endtime</div>
                                            <div class="item-result">ONLINE</div>
                                        </div>
                                    </div>
                                    <div class="inspection-item passed">
                                        <div class="item-status"><i class="fas fa-check"></i></div>
                                        <div class="item-info">
                                            <div class="item-id">Machine Submitted</div>
                                            <div class="item-result">ONLINE</div>
                                        </div>
                                    </div>
                                    <div class="inspection-item passed">
                                        <div class="item-status"><i class="fas fa-check"></i></div>
                                        <div class="item-info">
                                            <div class="item-id">Lot Request</div>
                                            <div class="item-result">ONLINE</div>
                                        </div>
                                    </div>
                                    <div class="inspection-item pending">
                                        <div class="item-status"><i class="fas fa-clock"></i></div>
                                        <div class="item-info">
                                            <div class="item-id">Machine Allocation</div>
                                            <div class="item-result">PENDING</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="metrics-bar">
                                    <div class="metric">
                                        <span class="metric-value">75.0%</span>
                                        <span class="metric-label">Progress Rate</span>
                                    </div>
                                    <div class="metric">
                                        <span class="metric-value">Sept. 2025</span>
                                        <span class="metric-label">Target Completion</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section">
        <div class="container">
            <div class="section-header text-center">
                <h2 class="section-title">MLCC Visual Inspection Process Features</h2>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <h3 class="feature-title">Visual Inspection Engine</h3>
                        <p class="feature-description">
                            Advanced imaging system captures high-resolution images of MLCC components 
                            and analyzes them for surface defects, dimensional accuracy, and quality issues.
                        </p>
                        <ul class="feature-list">
                            <li>High-resolution imaging capture</li>
                            <li>Surface defect detection</li>
                            <li>Dimensional measurement verification</li>
                            <li>Color and texture analysis</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <h3 class="feature-title">Inspection Classification</h3>
                        <p class="feature-description">
                            Automated classification system that categorizes MLCC components based on 
                            inspection results and routes them to appropriate quality bins.
                        </p>
                        <ul class="feature-list">
                            <li>Pass/Fail determination</li>
                            <li>Defect type classification</li>
                            <li>Quality grade assignment</li>
                            <li>Automated sorting instructions</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <h3 class="feature-title">Data Management</h3>
                        <p class="feature-description">
                            Comprehensive data tracking system that records inspection results, 
                            maintains historical records, and manages component traceability.
                        </p>
                        <ul class="feature-list">
                            <li>Inspection result logging</li>
                            <li>Component traceability</li>
                            <li>Historical data analysis</li>
                            <li>Batch tracking and reporting</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h3 class="feature-title">Process Monitoring</h3>
                        <p class="feature-description">
                            Real-time monitoring dashboard that tracks inspection throughput, 
                            quality metrics, and equipment performance indicators.
                        </p>
                        <ul class="feature-list">
                            <li>Real-time throughput tracking</li>
                            <li>Quality metrics display</li>
                            <li>Equipment status monitoring</li>
                            <li>Performance analytics</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h3 class="feature-title">Equipment Control</h3>
                        <p class="feature-description">
                            Integrated control system for managing inspection equipment, 
                            conveyor systems, and automated sorting mechanisms.
                        </p>
                        <ul class="feature-list">
                            <li>Camera and lighting control</li>
                            <li>Conveyor system integration</li>
                            <li>Automated sorting control</li>
                            <li>Equipment calibration management</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-file-chart-line"></i>
                        </div>
                        <h3 class="feature-title">Process Reporting</h3>
                        <p class="feature-description">
                            Comprehensive reporting system that generates detailed inspection reports, 
                            quality summaries, and production statistics for process optimization.
                        </p>
                        <ul class="feature-list">
                            <li>Inspection result reports</li>
                            <li>Quality trend analysis</li>
                            <li>Production summaries</li>
                            <li>Export and archival functions</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Workflow Section -->
    <section id="workflow" class="workflow-section">
        <div class="container">
            <div class="section-header text-center">
                <h2 class="section-title">MLCC Visual Inspection Workflow</h2>
            </div>
            <div class="workflow-steps">
                <div class="row g-4">
                    <div class="col-lg-3 col-md-6">
                        <div class="workflow-step">
                            <div class="step-number">01</div>
                            <div class="step-icon"><i class="fas fa-upload"></i></div>
                            <h4 class="step-title">Component Loading</h4>
                            <p class="step-description">
                                MLCC components are loaded into the inspection system via automated feeders 
                                or manual placement for quality assessment.
                            </p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="workflow-step">
                            <div class="step-number">02</div>
                            <div class="step-icon"><i class="fas fa-camera"></i></div>
                            <h4 class="step-title">Image Capture</h4>
                            <p class="step-description">
                                High-resolution cameras capture detailed images from multiple angles 
                                to ensure comprehensive visual inspection coverage.
                            </p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="workflow-step">
                            <div class="step-number">03</div>
                            <div class="step-icon"><i class="fas fa-search"></i></div>
                            <h4 class="step-title">Defect Analysis</h4>
                            <p class="step-description">
                                Advanced algorithms analyze captured images for surface defects, 
                                dimensional issues, and quality deviations.
                            </p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="workflow-step">
                            <div class="step-number">04</div>
                            <div class="step-icon"><i class="fas fa-route"></i></div>
                            <h4 class="step-title">Component Sorting</h4>
                            <p class="step-description">
                                Based on inspection results, components are automatically sorted 
                                into pass, fail, or rework categories for further processing.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer-section">
        <div class="container">
            <div class="footer-content">
                <div class="row">
                    <div class="col-lg-4">
                        <div class="footer-brand">
                            <div class="brand-container">
                                <i class="fas fa-microchip brand-icon"></i>
                                <span class="brand-text">{{ config('app.name') }}</span>
                            </div>
                            <p class="footer-description">
                                MLCC Visual Inspection Process Dashboard - A comprehensive system for 
                                managing multilayer ceramic capacitor quality control and inspection workflows.
                            </p>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <h5 class="footer-title">Process Components</h5>
                        <ul class="footer-links">
                            <li><a href="#features">Visual Inspection Engine</a></li>
                            <li><a href="#features">Classification System</a></li>
                            <li><a href="#features">Data Management</a></li>
                            <li><a href="#workflow">Inspection Workflow</a></li>
                        </ul>
                    </div>
                    <div class="col-lg-4">
                        <h5 class="footer-title">System Status</h5>
                        <div class="system-status">
                            <div class="status-item">
                                <span class="status-indicator online"></span>
                                <span class="status-text">All Systems Operational</span>
                            </div>
                            <div class="status-item">
                                <span class="status-indicator online"></span>
                                <span class="status-text">Real-time Monitoring Active</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="copyright">
                            © {{ date('Y') }} {{ config('app.name') }}. All rights reserved.
                        </p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p class="version-info">
                            MLCC Visual Inspection Platform v2.0
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --primary-light: #3b82f6;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-dark: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            --shadow-light: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-heavy: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--dark-color);
            background-color: #ffffff;
        }

        /* Navigation */
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding: 2rem 0 1rem 0;
            transition: all 0.3s ease;
            position: fixed !important;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1050;
            width: 100%;
            min-height: 96px;
            display: flex;
            align-items: center;
        }

        .navbar.scrolled,
        .navbar-scrolled {
            background: rgba(255, 255, 255, 0.98) !important;
            box-shadow: var(--shadow-light);
        }

        .brand-container {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 1.25rem;
            color: var(--dark-color);
            text-decoration: none;
        }

        .brand-icon {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-right: 0.5rem;
            font-size: 1.5rem;
        }

        .nav-link {
            color: var(--secondary-color) !important;
            font-weight: 500;
            transition: color 0.3s ease;
            padding: 0.5rem 1rem !important;
            display: flex;
            align-items: center;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
        }

        /* Hero Section */
        .hero-section {
            background: var(--gradient-dark);
            color: white;
            padding: 30px 0 60px;
            position: relative;
            overflow: hidden;
            min-height: 100vh;
        }

        .hero-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-dark);
            opacity: 0.9;
        }

        .hero-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: 1.5rem;
        }

        .text-gradient {
            background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            max-width: 600px;
        }

        .hero-stats {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #60a5fa;
        }

        .stat-label {
            font-size: 0.875rem;
            opacity: 0.8;
        }

        .process-overview {
            margin: 2rem 0;
        }
        
        .overview-item {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .overview-icon {
            width: 48px;
            height: 48px;
            background: var(--primary-color);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        
        .overview-text {
            display: flex;
            flex-direction: column;
        }
        
        .overview-text strong {
            font-size: 1rem;
            margin-bottom: 0.25rem;
        }
        
        .overview-text span {
            font-size: 0.875rem;
            opacity: 0.8;
        }

        .hero-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-lg {
            padding: 1rem 2.5rem;
            font-size: 1.1rem;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
            color: white;
        }

        .btn-outline-white {
            background: transparent;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .btn-outline-white:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-color: rgba(255, 255, 255, 0.5);
        }

        .btn-outline-light {
            background: transparent;
            color: var(--secondary-color);
            border: 2px solid var(--secondary-color);
        }

        .btn-outline-light:hover {
            background: var(--secondary-color);
            color: white;
        }

        /* Hero Visual */
        .hero-visual {
            position: relative;
            z-index: 2;
        }

        .dashboard-mockup {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 1.5rem;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: var(--shadow-heavy);
        }

        .mockup-header {
            display: flex;
            align-items: center;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 1rem;
        }

        .mockup-controls {
            display: flex;
            gap: 0.5rem;
        }

        .control {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .control.red { background: #ef4444; }
        .control.yellow { background: #f59e0b; }
        .control.green { background: #10b981; }

        .inspection-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .inspection-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .item-status {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
        }

        .inspection-item.passed .item-status {
            background: var(--success-color);
        }

        .inspection-item.failed .item-status {
            background: var(--danger-color);
        }

        .inspection-item.pending .item-status {
            background: var(--warning-color);
        }

        .item-id {
            font-size: 0.875rem;
            opacity: 0.8;
        }

        .item-result {
            font-size: 0.75rem;
            font-weight: 600;
        }

        .metrics-bar {
            display: flex;
            justify-content: space-between;
            padding-top: 1rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .metric {
            text-align: center;
        }

        .metric-value {
            display: block;
            font-size: 1.25rem;
            font-weight: 700;
            color: #60a5fa;
        }

        .metric-label {
            font-size: 0.75rem;
            opacity: 0.8;
        }

        /* Features Section */
        .features-section {
            padding: 100px 0;
            background: #ffffff;
        }

        .section-header {
            margin-bottom: 4rem;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 1rem;
        }

        .section-subtitle {
            font-size: 1.25rem;
            color: var(--secondary-color);
            max-width: 600px;
            margin: 0 auto;
        }

        .feature-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: var(--shadow-light);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-medium);
            border-color: var(--primary-light);
        }

        .feature-icon {
            width: 64px;
            height: 64px;
            background: var(--gradient-primary);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            color: white;
            font-size: 1.5rem;
        }

        .feature-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 1rem;
        }

        .feature-description {
            color: var(--secondary-color);
            margin-bottom: 1.5rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            color: var(--secondary-color);
            font-size: 0.875rem;
        }

        .feature-list li:before {
            content: "✓";
            color: var(--success-color);
            font-weight: bold;
            margin-right: 0.5rem;
        }

        /* Workflow Section */
        .workflow-section {
            padding: 100px 0;
            background: var(--light-color);
        }
        
        .workflow-steps {
            margin-top: 3rem;
        }
        
        .workflow-step {
            text-align: center;
            padding: 2rem 1.5rem;
            background: white;
            border-radius: 16px;
            box-shadow: var(--shadow-light);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            height: 100%;
            position: relative;
        }
        
        .workflow-step:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-medium);
            border-color: var(--primary-light);
        }
        
        .step-number {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 30px;
            background: var(--gradient-primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .workflow-step .step-icon {
            width: 64px;
            height: 64px;
            background: var(--gradient-primary);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 1rem auto 1.5rem;
            color: white;
            font-size: 1.5rem;
        }
        
        .step-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 1rem;
        }
        
        .step-description {
            color: var(--secondary-color);
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* Benefits Section */
        .benefits-section {
            padding: 100px 0;
            background: var(--light-color);
        }

        .benefits-list {
            margin-top: 2rem;
        }

        .benefit-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 2rem;
        }

        .benefit-icon {
            width: 48px;
            height: 48px;
            background: var(--gradient-primary);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .benefit-content h4 {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
        }

        .benefit-content p {
            color: var(--secondary-color);
            margin: 0;
        }

        /* Process Flow */
        .process-flow {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            margin-top: 2rem;
        }

        .flow-step {
            text-align: center;
            opacity: 0.5;
            transition: all 0.3s ease;
        }

        .flow-step.active {
            opacity: 1;
        }

        .step-icon {
            width: 48px;
            height: 48px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin: 0 auto 0.5rem;
        }

        .step-label {
            font-size: 0.875rem;
            font-weight: 500;
        }

        .flow-arrow {
            color: var(--primary-light);
            font-size: 1.25rem;
        }

        /* About Section */
        .about-section {
            padding: 100px 0;
            background: white;
        }

        .about-stats {
            margin-top: 3rem;
        }

        .about-stat {
            text-align: center;
            padding: 1.5rem;
        }

        .about-stat .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            display: block;
            margin-bottom: 0.5rem;
        }

        .about-stat .stat-label {
            color: var(--secondary-color);
            font-weight: 500;
        }

        /* CTA Section */
        .cta-section {
            padding: 100px 0;
            background: var(--gradient-primary);
            color: white;
        }

        .cta-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .cta-subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .cta-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Footer */
        .footer-section {
            background: var(--dark-color);
            color: white;
            padding: 3rem 0 1rem;
        }

        .footer-content {
            margin-bottom: 2rem;
        }

        .footer-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: white;
        }

        .footer-description {
            color: rgba(255, 255, 255, 0.7);
            margin-top: 1rem;
        }

        .footer-links {
            list-style: none;
            padding: 0;
        }

        .footer-links li {
            margin-bottom: 0.5rem;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: white;
        }

        .system-status {
            margin-top: 1rem;
        }

        .status-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-indicator.online {
            background: var(--success-color);
        }

        .status-text {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 1rem;
        }

        .copyright,
        .version-info {
            margin: 0;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.875rem;
        }

        /* Responsive Design */
        @media (max-width: 992px) {
            .hero-title {
                font-size: 2.75rem;
            }
            
            .hero-stats {
                justify-content: center;
            }
        }

        @media (max-width: 768px) {
            .hero-section {
                padding: 100px 0 80px;
            }
            
            .navbar {
                padding: 1.5rem 0 0.75rem 0;
                min-height: 80px;
            }
            
            .hero-title {
                font-size: 2.25rem;
            }
            
            .hero-stats {
                gap: 1rem;
            }
            
            .process-flow {
                flex-direction: column;
                gap: 1rem;
            }
            
            .flow-arrow {
                transform: rotate(90deg);
            }
            
            .section-title {
                font-size: 2rem;
            }
            
            .cta-title {
                font-size: 2rem;
            }
        }

        @media (max-width: 576px) {
            .hero-stats {
                flex-direction: column;
                gap: 1rem;
            }
            
            .hero-actions {
                justify-content: center;
            }
            
            .btn-lg {
                width: 100%;
                margin-bottom: 0.5rem;
            }
            
            .inspection-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hero-content > * {
            animation: fadeInUp 0.8s ease-out;
        }

        .hero-content > *:nth-child(2) { animation-delay: 0.1s; }
        .hero-content > *:nth-child(3) { animation-delay: 0.2s; }
        .hero-content > *:nth-child(4) { animation-delay: 0.3s; }
        .hero-content > *:nth-child(5) { animation-delay: 0.4s; }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Ensure body doesn't overlap with fixed navbar */
        body {
            padding-top: 0;
        }
        
        /* Navbar scroll effect */
        .navbar-scrolled {
            background: rgba(255, 255, 255, 0.98) !important;
            box-shadow: var(--shadow-light);
        }
    </style>

    <script>
        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('navbar-scrolled');
            } else {
                navbar.classList.remove('navbar-scrolled');
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                
                // Handle home/top scroll
                if (targetId === '#home' || targetId === '#') {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                    return;
                }
                
                const target = document.querySelector(targetId);
                if (target) {
                    const navbarHeight = document.querySelector('.navbar').offsetHeight;
                    const targetPosition = target.offsetTop - navbarHeight - 20;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>
</body>
</html>
