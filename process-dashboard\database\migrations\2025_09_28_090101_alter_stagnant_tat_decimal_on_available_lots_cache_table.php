<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Note: Changing column types requires doctrine/dbal
        // If you encounter an error, run: composer require doctrine/dbal
        Schema::table('available_lots_cache', function (Blueprint $table) {
            $table->decimal('stagnant_tat', 8, 2)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('available_lots_cache', function (Blueprint $table) {
            $table->integer('stagnant_tat')->nullable()->change();
        });
    }
};
