<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class UserManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:ADMIN');
    }

    /**
     * Display a listing of users.
     */
    public function index(Request $request)
    {
        $query = User::query();
        
        // Search functionality
        if ($request->filled('search')) {
            $searchTerm = $request->get('search');
            $query->where(function($q) use ($searchTerm) {
                $q->where('emp_name', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('emp_no', 'LIKE', "%{$searchTerm}%");
            });
        }
        
        // Role filter
        if ($request->filled('role') && $request->get('role') !== 'all') {
            $query->where('role', $request->get('role'));
        }
        
        // Status filter
        if ($request->filled('status')) {
            if ($request->get('status') === 'active') {
                $query->whereNotNull('emp_verified_at');
            } elseif ($request->get('status') === 'inactive') {
                $query->whereNull('emp_verified_at');
            }
        }
        
        // Sorting functionality
        $sortBy = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        
        // Validate sort column
        $allowedSorts = ['emp_name', 'role', 'emp_verified_at', 'created_at'];
        if (!in_array($sortBy, $allowedSorts)) {
            $sortBy = 'created_at';
        }
        
        // Validate sort direction
        if (!in_array($sortDirection, ['asc', 'desc'])) {
            $sortDirection = 'desc';
        }
        
        // Apply sorting
        if ($sortBy === 'emp_verified_at') {
            // Special handling for status sorting - active users first when desc, inactive first when asc
            if ($sortDirection === 'desc') {
                $query->orderByRaw('emp_verified_at IS NOT NULL DESC')
                      ->orderBy('emp_verified_at', 'desc');
            } else {
                $query->orderByRaw('emp_verified_at IS NULL DESC')
                      ->orderBy('emp_verified_at', 'asc');
            }
        } else {
            $query->orderBy($sortBy, $sortDirection);
        }
        
        // Add secondary sort for consistency
        if ($sortBy !== 'created_at') {
            $query->orderBy('created_at', 'desc');
        }
        
        $users = $query->paginate(15)->appends($request->query());
        
        $stats = [
            'total_users' => User::count(),
            'admin_users' => User::where('role', 'ADMIN')->count(),
            'manager_users' => User::where('role', 'MANAGER')->count(),
            'regular_users' => User::where('role', 'USER')->count(),
            'active_users' => User::whereNotNull('emp_verified_at')->count(),
            'inactive_users' => User::whereNull('emp_verified_at')->count(),
        ];
        
        return view('management.users.index', compact('users', 'stats'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        return view('management.users.create');
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'emp_no' => 'required|string|max:255|unique:users',
            'emp_name' => 'required|string|max:255',
            'position' => 'nullable|string|max:255',
            'title_class' => 'nullable|string|max:255',
            'rank' => 'nullable|string|max:255',
            'hr_job_name' => 'nullable|string|max:255',
            'job_assigned' => 'nullable|string|max:255',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:ADMIN,USER,MANAGER',
        ]);

        $user = User::create([
            'emp_no' => $request->emp_no,
            'emp_name' => $request->emp_name,
            'position' => $request->position,
            'title_class' => $request->title_class,
            'rank' => $request->rank,
            'hr_job_name' => $request->hr_job_name,
            'job_assigned' => $request->job_assigned,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'emp_verified_at' => now(), // Auto-verify admin-created users
        ]);

        return redirect()->route('management.users.index')
                        ->with('success', 'User created successfully!');
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        $userStats = [
            'total_lots_requested' => 0, // Can be updated later with LotRequest relationship if needed
            'total_endtime_entries' => 0, // Can be updated later with Endtime relationship if needed
            'last_login' => $user->updated_at,
            'account_created' => $user->created_at,
        ];
        
        return view('management.users.show', compact('user', 'userStats'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user)
    {
        return view('management.users.edit', compact('user'));
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'emp_no' => ['required', 'string', 'max:255', Rule::unique('users')->ignore($user->id)],
            'emp_name' => 'required|string|max:255',
            'position' => 'nullable|string|max:255',
            'title_class' => 'nullable|string|max:255',
            'rank' => 'nullable|string|max:255',
            'hr_job_name' => 'nullable|string|max:255',
            'job_assigned' => 'nullable|string|max:255',
            'password' => 'nullable|string|min:8|confirmed',
            'role' => 'required|in:ADMIN,USER,MANAGER',
        ]);

        $updateData = [
            'emp_no' => $request->emp_no,
            'emp_name' => $request->emp_name,
            'position' => $request->position,
            'title_class' => $request->title_class,
            'rank' => $request->rank,
            'hr_job_name' => $request->hr_job_name,
            'job_assigned' => $request->job_assigned,
            'role' => $request->role,
        ];

        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $user->update($updateData);

        return redirect()->route('management.users.index')
                        ->with('success', 'User updated successfully!');
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(User $user)
    {
        // Prevent self-deletion
        if ($user->id === auth()->id()) {
            return redirect()->route('management.users.index')
                           ->with('error', 'You cannot delete your own account!');
        }

        // Note: In a production system, you might want to check for related data
        // before allowing user deletion (endtime entries, lot requests, etc.)

        $user->delete();

        return redirect()->route('management.users.index')
                        ->with('success', 'User deleted successfully!');
    }

    /**
     * Toggle user status (activate/deactivate).
     */
    public function toggleStatus(User $user)
    {
        try {
            // Prevent self-deactivation
            if ($user->id === auth()->id()) {
                return redirect()->back()
                               ->with('error', 'You cannot deactivate your own account!');
            }

            // Prevent deactivation of ADMIN users entirely for extra safety
            if ($user->role === 'ADMIN' && !is_null($user->emp_verified_at)) {
                return redirect()->back()
                               ->with('error', 'Administrator accounts cannot be deactivated for security reasons!');
            }

            // Get the current status BEFORE the update
            $wasActive = !is_null($user->emp_verified_at);
            
            // Toggle the status
            if ($wasActive) {
                // User is currently active, so deactivate
                $user->update(['emp_verified_at' => null]);
                $statusText = 'deactivated';
            } else {
                // User is currently inactive, so activate
                $user->update(['emp_verified_at' => now()]);
                $statusText = 'activated';
            }
            
            return redirect()->route('management.users.index')
                            ->with('success', "User {$user->emp_name} {$statusText} successfully!");
                            
        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'Failed to toggle user status: ' . $e->getMessage());
        }
    }

    /**
     * Reset user password.
     */
    public function resetPassword(User $user)
    {
        $newPassword = 'Password123!';
        
        $user->update([
            'password' => Hash::make($newPassword),
        ]);

        return redirect()->back()
                        ->with('success', "Password reset successfully! New password: {$newPassword}");
    }
}