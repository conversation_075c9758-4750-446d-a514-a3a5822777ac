<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('equipment', function (Blueprint $table) {
            // Add indexes for performance optimization of manufacturing overview API
            $table->index(['eqp_status', 'alloc_type'], 'equipment_status_alloc_index');
            $table->index(['eqp_line', 'eqp_status'], 'equipment_line_status_index');
            $table->index(['eqp_status', 'eqp_line', 'alloc_type'], 'equipment_performance_composite_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('equipment', function (Blueprint $table) {
            $table->dropIndex('equipment_status_alloc_index');
            $table->dropIndex('equipment_line_status_index');
            $table->dropIndex('equipment_performance_composite_index');
        });
    }
};
