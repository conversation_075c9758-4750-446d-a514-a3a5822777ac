<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('endtime', function (Blueprint $table) {
            $table->id();
            
            // Core lot information
            $table->string('lot_id')->nullable();
            $table->string('model_15')->nullable();
            $table->string('lot_size')->nullable();
            $table->integer('lot_qty')->nullable();
            $table->string('work_type')->nullable();
            $table->string('lot_type')->nullable();
            $table->string('lipas_yn')->nullable();
            
            // Equipment assignments (up to 10 equipment units)
            $table->string('eqp_1')->nullable();
            $table->string('eqp_2')->nullable();
            $table->string('eqp_3')->nullable();
            $table->string('eqp_4')->nullable();
            $table->string('eqp_5')->nullable();
            $table->string('eqp_6')->nullable();
            $table->string('eqp_7')->nullable();
            $table->string('eqp_8')->nullable();
            $table->string('eqp_9')->nullable();
            $table->string('eqp_10')->nullable();
            
            // Equipment start times
            $table->datetime('start_time_1')->nullable()->comment('Start time for equipment 1');
            $table->datetime('start_time_2')->nullable()->comment('Start time for equipment 2');
            $table->datetime('start_time_3')->nullable()->comment('Start time for equipment 3');
            $table->datetime('start_time_4')->nullable()->comment('Start time for equipment 4');
            $table->datetime('start_time_5')->nullable()->comment('Start time for equipment 5');
            $table->datetime('start_time_6')->nullable()->comment('Start time for equipment 6');
            $table->datetime('start_time_7')->nullable()->comment('Start time for equipment 7');
            $table->datetime('start_time_8')->nullable()->comment('Start time for equipment 8');
            $table->datetime('start_time_9')->nullable()->comment('Start time for equipment 9');
            $table->datetime('start_time_10')->nullable()->comment('Start time for equipment 10');
            
            // NG percentages for each equipment
            $table->decimal('ng_percent_1', 5, 2)->nullable()->default(0)->comment('NG percentage for equipment 1 (0-100)');
            $table->decimal('ng_percent_2', 5, 2)->nullable()->default(0)->comment('NG percentage for equipment 2 (0-100)');
            $table->decimal('ng_percent_3', 5, 2)->nullable()->default(0)->comment('NG percentage for equipment 3 (0-100)');
            $table->decimal('ng_percent_4', 5, 2)->nullable()->default(0)->comment('NG percentage for equipment 4 (0-100)');
            $table->decimal('ng_percent_5', 5, 2)->nullable()->default(0)->comment('NG percentage for equipment 5 (0-100)');
            $table->decimal('ng_percent_6', 5, 2)->nullable()->default(0)->comment('NG percentage for equipment 6 (0-100)');
            $table->decimal('ng_percent_7', 5, 2)->nullable()->default(0)->comment('NG percentage for equipment 7 (0-100)');
            $table->decimal('ng_percent_8', 5, 2)->nullable()->default(0)->comment('NG percentage for equipment 8 (0-100)');
            $table->decimal('ng_percent_9', 5, 2)->nullable()->default(0)->comment('NG percentage for equipment 9 (0-100)');
            $table->decimal('ng_percent_10', 5, 2)->nullable()->default(0)->comment('NG percentage for equipment 10 (0-100)');
            
            // Aggregate equipment information
            $table->string('eqp_line')->nullable();
            $table->string('eqp_area')->nullable();
            
            // Status and timing
            $table->string('status')->nullable()->default('Ongoing');
            $table->datetime('est_endtime')->nullable()->comment('Calculated estimated end time');
            $table->datetime('actual_submitted_at')->nullable()->comment('Actual time when lot was submitted by user');
            
            // Submission results and audit
            $table->string('remarks')->nullable()->comment('Auto-calculated result: Early, OK, Delayed');
            $table->text('submission_notes')->nullable()->comment('Additional notes for submission, especially for delayed lots');
            $table->string('modified_by')->nullable();
            
            $table->timestamps();
            
            // Add indexes for better performance
            $table->index('lot_id');
            $table->index('est_endtime');
            $table->index('status');
            $table->index('eqp_line');
            $table->index('eqp_area');
            $table->index('remarks');
            $table->index('actual_submitted_at');
            $table->index(['lot_size', 'work_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('endtime');
    }
};
