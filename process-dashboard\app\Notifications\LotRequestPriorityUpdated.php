<?php

namespace App\Notifications;

use App\Models\LotRequest;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class LotRequestPriorityUpdated extends Notification
{
    use Queueable;

    public function __construct(
        public LotRequest $lotRequest,
        public User $manager,
        public bool $isUrgent,
        public int $manualPriority
    ) {}

    public function via(object $notifiable): array
    {
        return ['database'];
    }

    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'lot_request_priority_updated',
            'lot_request_id' => $this->lotRequest->id,
            'request_number' => $this->lotRequest->request_number,
            'new_status' => $this->lotRequest->status,
            'is_urgent' => $this->isUrgent,
            'manual_priority' => $this->manualPriority,
            'performed_by' => [
                'emp_no' => $this->manager->emp_no,
                'name' => $this->manager->getDisplayName(),
            ],
            'message' => sprintf(
                'Priority updated for request %s by %s (%s). Urgent: %s, Manual Priority: %d.',
                $this->lotRequest->request_number,
                $this->manager->getDisplayName(),
                $this->manager->emp_no,
                $this->isUrgent ? 'Yes' : 'No',
                $this->manualPriority
            ),
            'url' => '/lot-requests/' . $this->lotRequest->id,
        ];
    }
}
