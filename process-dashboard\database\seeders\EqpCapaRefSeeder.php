<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class EqpCapaRefSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing data
        DB::table('eqp_capa_ref')->truncate();

        $csvFile = base_path('eqp_capa_ref.csv');
        
        if (File::exists($csvFile)) {
            $csv = array_map('str_getcsv', file($csvFile));
            
            // Remove the header row
            $header = array_shift($csv);
            
            foreach ($csv as $row) {
                if (count($row) >= 5 && !empty($row[0])) {
                    DB::table('eqp_capa_ref')->insert([
                        'work_type' => trim($row[0]),
                        'size' => trim($row[1]),
                        'oee' => (float)trim($row[2]),
                        'passing' => (float)trim($row[3]),
                        'yield' => (float)trim($row[4]),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }
    }
}
