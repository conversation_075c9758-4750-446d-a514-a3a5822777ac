<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('updatewip', function (Blueprint $table) {
            $table->id();
            $table->string('lot_id')->unique();
            $table->string('model_15');
            $table->string('lot_size');
            $table->integer('lot_qty');
            $table->string('stagnant_tat');
            $table->string('qty_class');
            $table->string('work_type');
            $table->string('wip_status');
            $table->string('lot_status');
            $table->string('hold');
            $table->string('auto_yn');
            $table->string('lipas_yn');
            $table->string('eqp_type');
            $table->string('eqp_class');
            $table->string('lot_location');
            $table->string('lot_code');
            $table->string('modified_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('updatewip');
    }
};
