<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('available_lots_cache', function (Blueprint $table) {
            // Add nullable model_15 (short model identifier)
            if (!Schema::hasColumn('available_lots_cache', 'model_15')) {
                $table->string('model_15', 50)->nullable()->after('lot_size');
            }
            // Add nullable qty_class (e.g., PCS/REEL/etc.)
            if (!Schema::hasColumn('available_lots_cache', 'qty_class')) {
                $table->string('qty_class', 20)->nullable()->after('available_quantity');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('available_lots_cache', function (Blueprint $table) {
            if (Schema::hasColumn('available_lots_cache', 'model_15')) {
                $table->dropColumn('model_15');
            }
            if (Schema::hasColumn('available_lots_cache', 'qty_class')) {
                $table->dropColumn('qty_class');
            }
        });
    }
};
