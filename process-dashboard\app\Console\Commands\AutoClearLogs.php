<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\SettingsController;

class AutoClearLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'logs:auto-clear';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check and automatically clear logs based on system settings';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Checking auto clear logs settings...');
        
        $settingsController = new SettingsController();
        $settingsController->autoCheckClearLogs();
        
        $this->info('Auto clear logs check completed.');
        
        return Command::SUCCESS;
    }
}