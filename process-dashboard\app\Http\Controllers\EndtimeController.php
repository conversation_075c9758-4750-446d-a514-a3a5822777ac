<?php

namespace App\Http\Controllers;

use App\Models\Endtime;
use App\Models\Equipment;
use App\Models\UpdateWip;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class EndtimeController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of forecasted lots that can be finished by equipment.
     */
    public function index(Request $request)
    {
        // Calculate current cutoff defaults based on Asia/Manila timezone
        $currentCutoff = $this->getCurrentCutoffDefaults();

        // Get filter parameters with cutoff-based filtering
        $filters = [
            'status' => $request->get('status', 'all'),
            'eqp_line' => $request->get('eqp_line', 'all'),
            'eqp_area' => $request->get('eqp_area', 'all'),
            'work_type' => $request->get('work_type', 'all'),
            'lot_type' => $request->get('lot_type', 'all'),
            'cutoff_date' => $request->get('cutoff_date', $currentCutoff['date']),
            'cutoff_shift' => $request->get('cutoff_shift', $currentCutoff['shift']),
            'cutoff_period' => $request->get('cutoff_period', $currentCutoff['period']),
            'search' => $request->get('search', ''),
        ];
        
        // Build query for endtime data
        $endtimeQuery = Endtime::select('*');
        
        // Apply cutoff-based time filter
        $this->applyCutoffTimeFilter($endtimeQuery, $filters['cutoff_date'], $filters['cutoff_shift'], $filters['cutoff_period']);
        
        // Apply status filter
        if ($filters['status'] !== 'all') {
            $endtimeQuery->where('status', $filters['status']);
        }
        
        // Apply equipment line filter
        if ($filters['eqp_line'] !== 'all') {
            $endtimeQuery->where('eqp_line', $filters['eqp_line']);
        }
        
        // Apply equipment area filter
        if ($filters['eqp_area'] !== 'all') {
            $endtimeQuery->where('eqp_area', $filters['eqp_area']);
        }
        
        // Apply work type filter
        if ($filters['work_type'] !== 'all') {
            $endtimeQuery->where('work_type', $filters['work_type']);
        }
        
        // Apply lot type filter
        if ($filters['lot_type'] !== 'all') {
            $endtimeQuery->where('lot_type', $filters['lot_type']);
        }
        
        // Apply search filter - if search is provided, search across ALL lots regardless of other filters
        if (!empty($filters['search'])) {
            $searchTerm = $filters['search'];

            // Create a new query that searches across ALL lots in the database
            $endtimeQuery = Endtime::select('*')
                ->where(function($query) use ($searchTerm) {
                    $query->where('lot_id', 'LIKE', '%' . $searchTerm . '%')
                          ->orWhere('model_15', 'LIKE', '%' . $searchTerm . '%')
                          ->orWhere('eqp_1', 'LIKE', '%' . $searchTerm . '%')
                          ->orWhere('eqp_2', 'LIKE', '%' . $searchTerm . '%')
                          ->orWhere('eqp_3', 'LIKE', '%' . $searchTerm . '%')
                          ->orWhere('eqp_4', 'LIKE', '%' . $searchTerm . '%')
                          ->orWhere('eqp_5', 'LIKE', '%' . $searchTerm . '%')
                          ->orWhere('eqp_6', 'LIKE', '%' . $searchTerm . '%')
                          ->orWhere('eqp_7', 'LIKE', '%' . $searchTerm . '%')
                          ->orWhere('eqp_8', 'LIKE', '%' . $searchTerm . '%')
                          ->orWhere('eqp_9', 'LIKE', '%' . $searchTerm . '%')
                          ->orWhere('eqp_10', 'LIKE', '%' . $searchTerm . '%');
                });
        }
        
        $endtimeLots = $endtimeQuery
            ->orderBy('est_endtime', 'asc')
            ->paginate(15)
            ->withQueryString();
        
        // Get filter options
        $filterOptions = [
            'statuses' => Endtime::distinct()->pluck('status')->filter()->sort()->values(),
            'equipment_lines' => Endtime::distinct()->pluck('eqp_line')->filter()->sort()->values(),
            'equipment_areas' => Endtime::distinct()->pluck('eqp_area')->filter()->sort()->values(),
            'work_types' => Endtime::distinct()->pluck('work_type')->filter()->sort()->values(),
            'lot_types' => Endtime::distinct()->pluck('lot_type')->filter()->sort()->values(),
        ];
        
        // Get summary statistics focused on current filtered results
        // Clone the query to get statistics without affecting pagination
        $statsQuery = clone $endtimeQuery;
        $totalFilteredLots = $statsQuery->count();
        $totalFilteredQuantity = $statsQuery->sum('lot_qty') ?: 0;
        
        // Calculate equipment statistics
        $equipmentStats = $this->calculateEquipmentStats($filters);
        
        // Count ongoing lots specifically
        $ongoingLotsQuery = clone $endtimeQuery;
        $ongoingLotsCount = $ongoingLotsQuery->where('status', 'Ongoing')->count();
        
        // Count equipment that have ongoing lots assigned (based on equipment table, only OPERATIONAL status)
        $equipmentWithOngoingQuery = Equipment::where('eqp_status', 'OPERATIONAL')
            ->whereNotNull('ongoing_lot')
            ->where('ongoing_lot', '!=', '');
        
        // Apply equipment filters to ongoing equipment count
        if ($filters['eqp_line'] !== 'all') {
            $equipmentWithOngoingQuery->where('eqp_line', $filters['eqp_line']);
        }
        
        if ($filters['eqp_area'] !== 'all') {
            $equipmentWithOngoingQuery->where('eqp_area', $filters['eqp_area']);
        }
        
        $equipmentWithOngoingCount = $equipmentWithOngoingQuery->count();
        
        // Calculate equipment WITHOUT ongoing lots (respecting same filters)
        $equipmentWithoutOngoingQuery = Equipment::where('eqp_status', 'OPERATIONAL')
            ->where(function($query) {
                $query->whereNull('ongoing_lot')
                      ->orWhere('ongoing_lot', '')
                      ->orWhere('ongoing_lot', '0');
            });
        
        // Apply same filters to without-ongoing equipment count
        if ($filters['eqp_line'] !== 'all') {
            $equipmentWithoutOngoingQuery->where('eqp_line', $filters['eqp_line']);
        }
        
        if ($filters['eqp_area'] !== 'all') {
            $equipmentWithoutOngoingQuery->where('eqp_area', $filters['eqp_area']);
        }
        
        $equipmentWithoutOngoingCount = $equipmentWithoutOngoingQuery->count();
        
        $stats = [
            'total_filtered_lots' => $totalFilteredLots,
            'total_filtered_quantity' => $totalFilteredQuantity,
            'total_endtime_quantity' => $totalFilteredQuantity, // Add this for consistency with blade template
            'ideal_equipment_count' => $equipmentStats['ideal_count'],
            'actual_equipment_count' => $equipmentStats['actual_count'],
            'utilization_percentage' => $equipmentStats['utilization_percentage'],
            'ongoing_lots_count' => $equipmentWithOngoingCount,
            'equipment_without_ongoing' => $equipmentWithoutOngoingCount,
            'status_breakdown' => DB::table('endtime')
                ->select('status', DB::raw('COUNT(*) as count'))
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray(),
            'line_breakdown' => DB::table('endtime')
                ->select('eqp_line', DB::raw('COUNT(*) as count'))
                ->groupBy('eqp_line')
                ->pluck('count', 'eqp_line')
                ->toArray(),
            'cutoff_info' => [
                'date' => $filters['cutoff_date'],
                'shift' => $filters['cutoff_shift'],
                'period' => $filters['cutoff_period']
            ]
        ];
        
        // Get available equipment for the form (only OPERATIONAL status)
        $availableEquipment = Equipment::where('eqp_status', 'OPERATIONAL')
            ->select(
                'eqp_no', 'eqp_line', 'eqp_area', 'eqp_type', 'eqp_maker',
                'cam_class', 'insp_type', 'linear_type', 'size', 'alloc_type',
                'oee_capa', 'eqp_oee', 'eqp_passing', 'eqp_yield', 'loading_speed', 'operation_time'
            )->orderBy('eqp_line')->orderBy('eqp_area')->orderBy('eqp_no')->get();
        
        // Get WIP last updated information
        $lastWipUpdate = UpdateWip::latest('updated_at')->first();
        $wipLastUpdated = $lastWipUpdate ? $lastWipUpdate->updated_at : null;
        
        // Handle AJAX requests for indicator updates
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'stats' => $stats,
                'total_lots' => $totalFilteredLots
            ]);
        }
        
        return view('endtime.index', compact('endtimeLots', 'filters', 'filterOptions', 'stats', 'availableEquipment', 'wipLastUpdated'));
    }

    /**
     * Apply cutoff-based time filtering to the query based on shift operations.
     * 
     * Production operates 24/7 with two 12-hour shifts:
     * - Day shift: 7:00 AM - 7:00 PM (3 cutoff periods: 7-12, 12-16, 16-19)
     * - Night shift: 7:00 PM - 7:00 AM next day (3 cutoff periods: 19-24, 0-4, 4-7)
     */
    private function applyCutoffTimeFilter($query, $cutoffDate, $cutoffShift, $cutoffPeriod)
    {
        if (!$cutoffDate || !$cutoffShift || !$cutoffPeriod) {
            return; // No filtering if parameters are missing
        }

        $baseDate = Carbon::parse($cutoffDate);
        $startDateTime = null;
        $endDateTime = null;

        // Define cutoff periods for each shift
        if ($cutoffShift === 'all') {
            // All shifts: cover entire 24-hour period of the selected date only
            // Since this is endtime filtering, we want lots finishing on the selected date
            $startDateTime = $baseDate->copy()->startOfDay();
            $endDateTime = $baseDate->copy()->endOfDay();
        } elseif ($cutoffShift === 'day') {
            switch ($cutoffPeriod) {
                case '1': // 7:00 AM - 12:00 PM
                    $startDateTime = $baseDate->copy()->setTime(7, 0, 0);
                    $endDateTime = $baseDate->copy()->setTime(11, 59, 59);
                    break;
                case '2': // 12:00 PM - 4:00 PM
                    $startDateTime = $baseDate->copy()->setTime(12, 0, 0);
                    $endDateTime = $baseDate->copy()->setTime(15, 59, 59);
                    break;
                case '3': // 4:00 PM - 7:00 PM
                    $startDateTime = $baseDate->copy()->setTime(16, 0, 0);
                    $endDateTime = $baseDate->copy()->setTime(18, 59, 59);
                    break;
                case 'all': // Entire day shift: 7:00 AM - 7:00 PM
                    $startDateTime = $baseDate->copy()->setTime(7, 0, 0);
                    $endDateTime = $baseDate->copy()->setTime(18, 59, 59);
                    break;
            }
        } elseif ($cutoffShift === 'night') {
            switch ($cutoffPeriod) {
                case '1': // 7:00 PM - 12:00 AM (same day)
                    $startDateTime = $baseDate->copy()->setTime(19, 0, 0);
                    $endDateTime = $baseDate->copy()->setTime(23, 59, 59);
                    break;
                case '2': // 12:00 AM - 4:00 AM (next day)
                    $startDateTime = $baseDate->copy()->addDay()->setTime(0, 0, 0);
                    $endDateTime = $baseDate->copy()->addDay()->setTime(3, 59, 59);
                    break;
                case '3': // 4:00 AM - 7:00 AM (next day)
                    $startDateTime = $baseDate->copy()->addDay()->setTime(4, 0, 0);
                    $endDateTime = $baseDate->copy()->addDay()->setTime(6, 59, 59);
                    break;
                case 'all': // Entire night shift: 7:00 PM - 7:00 AM next day
                    $startDateTime = $baseDate->copy()->setTime(19, 0, 0);
                    $endDateTime = $baseDate->copy()->addDay()->setTime(6, 59, 59);
                    break;
            }
        }

        // Apply the time range filter if we have valid start and end times
        if ($startDateTime && $endDateTime) {
            // For submitted lots, use actual_submitted_at; for ongoing lots, use est_endtime
            $query->where(function($q) use ($startDateTime, $endDateTime) {
                $q->where(function($subQ) use ($startDateTime, $endDateTime) {
                    // Submitted lots: filter by actual_submitted_at
                    $subQ->where('status', 'Submitted')
                         ->whereBetween('actual_submitted_at', [$startDateTime, $endDateTime]);
                })->orWhere(function($subQ) use ($startDateTime, $endDateTime) {
                    // Ongoing lots: filter by est_endtime
                    $subQ->where('status', 'Ongoing')
                         ->whereBetween('est_endtime', [$startDateTime, $endDateTime]);
                });
            });
        }
    }

    /**
     * Calculate equipment statistics based on filters.
     * 
     * @param array $filters
     * @return array
     */
    private function calculateEquipmentStats($filters)
    {
        // Build equipment query based on filters (only OPERATIONAL status)
        $equipmentQuery = Equipment::where('eqp_status', 'OPERATIONAL');
        
        // Apply equipment line filter
        if ($filters['eqp_line'] !== 'all') {
            $equipmentQuery->where('eqp_line', $filters['eqp_line']);
        }
        
        // Apply equipment area filter
        if ($filters['eqp_area'] !== 'all') {
            $equipmentQuery->where('eqp_area', $filters['eqp_area']);
        }
        
        // Get ideal equipment count (total available equipment matching filters)
        $idealEquipmentCount = $equipmentQuery->count();
        
        // Get actual equipment count (equipment with ongoing lots)
        $actualEquipmentQuery = Endtime::query()
            ->where('status', 'Ongoing')
            ->select([
                'eqp_1', 'eqp_2', 'eqp_3', 'eqp_4', 'eqp_5',
                'eqp_6', 'eqp_7', 'eqp_8', 'eqp_9', 'eqp_10'
            ]);
        
        // Apply equipment filters to ongoing lots
        if ($filters['eqp_line'] !== 'all') {
            $actualEquipmentQuery->where('eqp_line', $filters['eqp_line']);
        }
        
        if ($filters['eqp_area'] !== 'all') {
            $actualEquipmentQuery->where('eqp_area', $filters['eqp_area']);
        }
        
        // Apply work type filter to ongoing lots
        if ($filters['work_type'] !== 'all') {
            $actualEquipmentQuery->where('work_type', $filters['work_type']);
        }
        
        // Get unique equipment numbers from ongoing lots
        $ongoingLots = $actualEquipmentQuery->get();
        $activeEquipment = collect();
        
        foreach ($ongoingLots as $lot) {
            for ($i = 1; $i <= 10; $i++) {
                $eqpField = 'eqp_' . $i;
                if (!empty($lot->$eqpField)) {
                    $activeEquipment->push($lot->$eqpField);
                }
            }
        }
        
        // Count unique active equipment
        $actualEquipmentCount = $activeEquipment->unique()->count();
        
        // Calculate utilization percentage
        $utilizationPercentage = $idealEquipmentCount > 0 
            ? round(($actualEquipmentCount / $idealEquipmentCount) * 100, 1) 
            : 0;
        
        return [
            'ideal_count' => $idealEquipmentCount,
            'actual_count' => $actualEquipmentCount,
            'utilization_percentage' => $utilizationPercentage
        ];
    }

    /**
     * Calculate current cutoff defaults based on Asia/Manila timezone
     *
     * Shift Schedule:
     * - Day Shift: 07:00 - 18:59 (3 cutoffs: 7-12, 12-16, 16-19)
     * - Night Shift: 19:00 - 06:59 (3 cutoffs: 19-24, 0-4, 4-7)
     */
    private function getCurrentCutoffDefaults()
    {
        // Get current time in Asia/Manila timezone
        $now = Carbon::now('Asia/Manila');
        $hours = $now->hour;

        $shift = '';
        $period = '';
        $cutoffDate = null;

        if ($hours >= 7 && $hours <= 18) {
            // Day Shift: 07:00 - 18:59
            $shift = 'day';
            $cutoffDate = $now; // Same day

            if ($hours >= 7 && $hours < 12) {
                $period = '1'; // 07:00 - 11:59
            } elseif ($hours >= 12 && $hours < 16) {
                $period = '2'; // 12:00 - 15:59
            } else {
                $period = '3'; // 16:00 - 18:59
            }
        } else {
            // Night Shift: 19:00 - 06:59
            $shift = 'night';

            if ($hours >= 19) {
                // 19:00 - 23:59 (same day)
                $period = '1';
                $cutoffDate = $now; // Same day
            } elseif ($hours >= 0 && $hours < 4) {
                // 00:00 - 03:59 (next day, but shift started previous day)
                $period = '2';
                $cutoffDate = $now->copy()->subDay(); // Previous day
            } else {
                // 04:00 - 06:59 (next day, but shift started previous day)
                $period = '3';
                $cutoffDate = $now->copy()->subDay(); // Previous day
            }
        }

        return [
            'date' => $cutoffDate->format('Y-m-d'),
            'shift' => $shift,
            'period' => $period
        ];
    }

    /**
     * Show the form for creating a new lot entry.
     */
    public function create(Request $request)
    {
        // Check permission for create action
        if (!auth()->user()->isUser() && !auth()->user()->isManager() && !auth()->user()->isAdmin()) {
            abort(403, 'You do not have permission to create endtime records.');
        }

        // Get available equipment for the form (only OPERATIONAL status)
        $availableEquipment = Equipment::where('eqp_status', 'OPERATIONAL')
            ->select(
                'eqp_no', 'eqp_line', 'eqp_area', 'eqp_type', 'eqp_maker',
                'cam_class', 'insp_type', 'linear_type', 'size', 'alloc_type',
                'oee_capa', 'eqp_oee', 'eqp_passing', 'eqp_yield', 'loading_speed', 'operation_time'
            )->orderBy('eqp_line')->orderBy('eqp_area')->orderBy('eqp_no')->get();

        // Check if this is an edit request
        $lot = null;
        $isEditMode = false;
        if ($request->has('edit') && $request->edit) {
            try {
                $lot = Endtime::findOrFail($request->edit);
                $isEditMode = true;

                // Check if lot status allows editing - only 'Ongoing' lots can be edited
                if ($lot->status !== 'Ongoing') {
                    return redirect()->route('endtime.index')
                        ->with('error', "Cannot edit lot {$lot->lot_id} - Only lots with 'Ongoing' status can be edited. Current status: {$lot->status}");
                }

                // Add info message for edit mode
                session()->flash('info', "Editing lot: {$lot->lot_id} (Status: {$lot->status})");
            } catch (\Exception $e) {
                return redirect()->route('endtime.create')
                    ->with('error', 'Lot not found.');
            }
        }

        return view('endtime.create', compact('availableEquipment', 'lot', 'isEditMode'));
    }

    /**
     * Show the form for submitting ongoing lots.
     */
    public function showSubmit()
    {
        // Check permission for update action
        if (!auth()->user()->isUser() && !auth()->user()->isManager() && !auth()->user()->isAdmin()) {
            abort(403, 'You do not have permission to submit endtime records.');
        }
        
        return view('endtime.submit');
    }

    /**
     * Store a new lot entry or update existing lot with endtime calculation.
     */
    public function store(Request $request)
    {
        // Check if we're in edit mode
        $isEditMode = !empty($request->edit_lot_id);
        $editLotId = $isEditMode ? $request->edit_lot_id : null;
        
        \Log::info('Endtime store method called', [
            'method' => $request->method(),
            'edit_mode' => $isEditMode,
            'edit_lot_id' => $editLotId,
            'has_data' => $request->all() ? 'yes' : 'no',
            'lot_id' => $request->get('lot_id'),
            'user' => auth()->user() ? auth()->user()->emp_no : 'not authenticated'
        ]);
        
        // Check permission for create action
        if (!auth()->user()->isUser() && !auth()->user()->isManager() && !auth()->user()->isAdmin()) {
            \Log::error('Permission denied for endtime store', [
                'user' => auth()->user() ? auth()->user()->emp_no : 'not authenticated'
            ]);
            abort(403, 'You do not have permission to create endtime records.');
        }
        
        // Log request data for debugging
        \Log::info('Request data received', [
            'lot_id' => $request->get('lot_id'),
            'lot_qty' => $request->get('lot_qty'),
            'lot_type' => $request->get('lot_type'),
            'work_type' => $request->get('work_type'),
            'is_edit_mode' => $isEditMode,
            'edit_lot_id' => $editLotId,
            'equipment_count' => is_array($request->get('equipment')) ? count($request->get('equipment')) : 0,
            'equipment' => $request->get('equipment'),
            'equipment_structure' => $request->get('equipment') ? array_map(function($eq, $key) {
                return [
                    'index' => $key,
                    'eqp_no' => $eq['eqp_no'] ?? 'NULL',
                    'start_time' => $eq['start_time'] ?? 'NULL',
                    'ng_percent' => $eq['ng_percent'] ?? 'NULL'
                ];
            }, $request->get('equipment'), array_keys($request->get('equipment'))) : 'NO_EQUIPMENT',
            'all_data' => $request->except(['_token'])
        ]);
        
        // Validate the request
        try {
            // Build validation rules based on whether we're editing or creating
            $validationRules = [
                'model_15' => 'nullable|string|max:15',
                'lot_size' => 'required|string|in:03,05,10,21,31',
                'lot_qty' => 'required|integer|min:1',
                'work_type' => 'required|string|in:NORMAL,OI REWORK,ADV,COMB,LY,RL,PROCESS RW,FSTOP,WH REWORK',
                'lot_type' => 'required|string|in:MAIN,WL/RW,RL/LY',
                'lipas_yn' => 'required|string|in:Y,N',
                'equipment' => 'required|array|min:1|max:10',
                'equipment.*.eqp_no' => 'required|string|exists:equipment,eqp_no',
                'equipment.*.start_time' => 'required|date',
                'equipment.*.ng_percent' => 'nullable|sometimes|numeric|min:0|max:100',
                'rl_ly_quantity' => 'nullable|integer|min:1',
            ];

            // Handle lot_id validation based on edit mode and lot type
            if ($isEditMode) {
                // For edit mode, no uniqueness check on lot_id (allow same lot_id)
                $validationRules['lot_id'] = 'required|string|max:20';
                $validationRules['edit_lot_id'] = 'required|exists:endtime,id';
            } else {
                // For create mode, lot_id uniqueness depends on lot type
                // Only MAIN lots need to be unique, WL/RW and RL/LY can have duplicates
                if ($request->lot_type === 'MAIN') {
                    $validationRules['lot_id'] = 'required|string|max:20|unique:endtime,lot_id';
                } else {
                    $validationRules['lot_id'] = 'required|string|max:20';
                }
            }

            // Basic validation first
            $request->validate($validationRules);

            // Custom duplicate validation logic (only for new entries)
            if (!$isEditMode) {
                $duplicateCheck = $this->checkForDuplicateLot($request, $isEditMode, $editLotId);
                if ($duplicateCheck['isDuplicate']) {
                    throw \Illuminate\Validation\ValidationException::withMessages([
                        'lot_id' => [$duplicateCheck['message']]
                    ]);
                }
            }

            \Log::info('Validation passed successfully');
        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::error('Validation failed', [
                'errors' => $e->errors(),
                'request_data' => $request->except(['_token'])
            ]);
            throw $e;
        }

        try {
            DB::beginTransaction();

            // Get equipment data for calculations
            $equipmentAssignments = collect($request->equipment)
                ->filter(function ($assignment) {
                    return !empty($assignment['eqp_no']) && !empty($assignment['start_time']);
                })
                ->values();

            if ($equipmentAssignments->isEmpty()) {
                return back()->withErrors(['equipment' => 'At least one equipment assignment is required.']);
            }

            // Calculate endtime using shared-load algorithm
            $result = $this->calculateEndtime($request->lot_qty, $equipmentAssignments);
            
            \Log::info('Endtime calculation result', [
                'success' => $result['success'],
                'lot_qty' => $request->lot_qty,
                'equipment_count' => count($equipmentAssignments),
                'est_endtime' => $result['success'] ? $result['est_endtime']->format('Y-m-d H:i:s') : null,
                'message' => $result['message'] ?? null
            ]);

            if (!$result['success']) {
                return back()->withErrors(['equipment' => $result['message']]);
            }

            // Create or update endtime record
            if ($isEditMode) {
                // Update existing record
                $endtime = Endtime::findOrFail($editLotId);
                
                // Check if lot status allows updating - only 'Ongoing' lots can be updated
                if ($endtime->status !== 'Ongoing') {
                    return back()->withErrors(['error' => "Cannot update lot {$endtime->lot_id} - Only lots with 'Ongoing' status can be updated. Current status: {$endtime->status}"]);
                }
                
                \Log::info('Updating existing lot', ['original_lot_id' => $endtime->lot_id, 'new_lot_id' => $request->lot_id]);
                
                // Clear existing equipment ongoing_lot assignments before updating
                $this->clearEquipmentOngoingLot($endtime->lot_id);
            } else {
                // Create new record
                $endtime = new Endtime();
                $endtime->status = 'Ongoing'; // Default status for new lots
                \Log::info('Creating new lot', ['lot_id' => $request->lot_id]);
            }
            
            // Set/update lot data
            $endtime->lot_id = $request->lot_id;
            $endtime->model_15 = $request->model_15;
            $endtime->lot_size = $request->lot_size;
            $endtime->lot_qty = $request->lot_qty;
            $endtime->work_type = $request->work_type;
            $endtime->lot_type = $request->lot_type;
            $endtime->lipas_yn = $request->lipas_yn;
            $endtime->est_endtime = $result['est_endtime'];
            
            // Set user information for both create and update
            $endtime->modified_by = Auth::user()->emp_name ?? Auth::user()->emp_no;
            
            // Clear existing equipment assignments if in edit mode
            if ($isEditMode) {
                for ($i = 1; $i <= 10; $i++) {
                    $equipmentFieldName = 'eqp_' . $i;
                    $startTimeFieldName = 'start_time_' . $i;
                    $ngPercentFieldName = 'ng_percent_' . $i;
                    $endtime->$equipmentFieldName = null;
                    $endtime->$startTimeFieldName = null;
                    $endtime->$ngPercentFieldName = null;
                }
            }
            
            // Set equipment assignments, start times, and ng_percent (up to 10 equipment units)
            foreach ($equipmentAssignments as $index => $assignment) {
                $equipmentFieldIndex = $index + 1;
                if ($equipmentFieldIndex <= 10) {
                    $equipmentFieldName = 'eqp_' . $equipmentFieldIndex;
                    $startTimeFieldName = 'start_time_' . $equipmentFieldIndex;
                    $ngPercentFieldName = 'ng_percent_' . $equipmentFieldIndex;
                    $endtime->$equipmentFieldName = $assignment['eqp_no'];
                    // Ensure start time is stored in local timezone
                    // Normalize 'datetime-local' (YYYY-MM-DDTHH:MM) to DB format without timezone shifting
                    $startTimeString = str_replace('T', ' ', $assignment['start_time']);
                    if (substr_count($startTimeString, ':') === 1) {
                        $startTimeString .= ':00';
                    }
                    // Parse and store in app timezone without conversion
                    $startTimeString = Carbon::createFromFormat('Y-m-d H:i:s', $startTimeString, config('app.timezone'))->format('Y-m-d H:i:s');
                    // Store as plain "Y-m-d H:i:s" string to avoid implicit timezone conversions
                    $endtime->$startTimeFieldName = $startTimeString;
                    $endtime->$ngPercentFieldName = $assignment['ng_percent'] ?? 0;
                }
            }
            
            // Set equipment line and area from the first equipment
            $firstEquipment = Equipment::where('eqp_no', $equipmentAssignments->first()['eqp_no'])->first();
            if ($firstEquipment) {
                $endtime->eqp_line = $firstEquipment->eqp_line;
                $endtime->eqp_area = $firstEquipment->eqp_area;
            }
            
            $endtime->save();
            
            // Update equipment ongoing_lot column for each assigned equipment
            $this->updateEquipmentOngoingLot($equipmentAssignments, $request->lot_id, 'assign');

            DB::commit();

            $action = $isEditMode ? 'updated' : 'added';
            $successMessage = "Lot {$request->lot_id} has been successfully {$action} with estimated end time: " . 
                             $result['est_endtime']->format('M d, Y H:i');
            
            return redirect()->route('endtime.index')
                ->with('success', $successMessage);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error saving lot entry', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return redirect()->route('endtime.create')->withErrors(['error' => 'Failed to save lot entry: ' . $e->getMessage()]);
        }
    }

    /**
     * Check for duplicate lot based on custom business rules
     *
     * Duplicate conditions:
     * 1. Same lot number (lot_id)
     * 2. Same lot quantity (lot_qty)
     * 3. Same work type (work_type)
     * 4. Lot type = "MAIN" (WL/RW and RL/LY lots can have duplicates)
     * 5. Status = "Submitted"
     */
    private function checkForDuplicateLot($request, $isEditMode = false, $editLotId = null)
    {
        \Log::info('Checking for duplicate lot', [
            'lot_id' => $request->lot_id,
            'lot_qty' => $request->lot_qty,
            'work_type' => $request->work_type,
            'lot_type' => $request->lot_type,
            'is_edit_mode' => $isEditMode,
            'edit_lot_id' => $editLotId
        ]);

        // Only check for duplicates if lot_type is "MAIN"
        // WL/RW and RL/LY lots are allowed to have duplicates
        if ($request->lot_type !== 'MAIN') {
            \Log::info('Skipping duplicate check - lot_type allows duplicates', ['lot_type' => $request->lot_type]);
            return ['isDuplicate' => false, 'message' => ''];
        }

        $query = Endtime::where('lot_id', $request->lot_id)
                        ->where('lot_qty', $request->lot_qty)
                        ->where('work_type', $request->work_type)
                        ->where('lot_type', 'MAIN')
                        ->where('status', 'Submitted');

        // If in edit mode, exclude the current record being edited
        if ($isEditMode && $editLotId) {
            $query->where('id', '!=', $editLotId);
        }

        $existingLot = $query->first();

        \Log::info('Duplicate check query result', [
            'found_duplicate' => $existingLot ? true : false,
            'existing_lot_id' => $existingLot ? $existingLot->id : null,
            'query_conditions' => [
                'lot_id' => $request->lot_id,
                'lot_qty' => $request->lot_qty,
                'work_type' => $request->work_type,
                'lot_type' => 'MAIN',
                'status' => 'Submitted'
            ]
        ]);

        if ($existingLot) {
            $message = "The lot id has already been submitted.";

            \Log::warning('Duplicate lot validation triggered', [
                'lot_id' => $request->lot_id,
                'lot_qty' => $request->lot_qty,
                'work_type' => $request->work_type,
                'lot_type' => $request->lot_type,
                'existing_lot_id' => $existingLot->id,
                'existing_status' => $existingLot->status,
                'existing_created_at' => $existingLot->created_at,
                'message' => $message
            ]);

            return ['isDuplicate' => true, 'message' => $message];
        }

        \Log::info('No duplicate found - lot is unique');
        return ['isDuplicate' => false, 'message' => ''];
    }

    /**
     * Format equipment details for display
     */
    private function formatEquipmentDetails($equipmentAssignments)
    {
        $details = [];
        foreach ($equipmentAssignments as $assignment) {
            $equipment = Equipment::where('eqp_no', $assignment['eqp_no'])->first();
            if ($equipment) {
                // Use the pre-calculated oee_capa field directly
                $dailyCapacity = $equipment->oee_capa ?? $equipment->getDailyCapacity();
                
                $details[] = [
                    'eqp_no' => $assignment['eqp_no'],
                    'line' => $equipment->eqp_line,
                    'area' => $equipment->eqp_area,
                    'start_time' => $assignment['start_time'],
                    'ng_percent' => $assignment['ng_percent'] ?? 0,
                    'oee_capacity' => $dailyCapacity
                ];
            }
        }
        return $details;
    }

    /**
     * Calculate estimated endtime using shared-load algorithm.
     * Matches frontend calculation logic exactly with enhanced error handling.
     */
    private function calculateEndtime($lotQty, $equipmentAssignments)
    {
        try {
            // Validate input parameters
            if (empty($lotQty) || $lotQty <= 0) {
                return [
                    'success' => false,
                    'message' => 'Invalid lot quantity provided.'
                ];
            }
            
            if (empty($equipmentAssignments)) {
                return [
                    'success' => false,
                    'message' => 'No equipment assignments provided.'
                ];
            }
            
            $profiles = [];
            
            // Build equipment runtime profiles with enhanced error checking
            foreach ($equipmentAssignments as $index => $assignment) {
                // Validate assignment structure
                if (!is_array($assignment) || !isset($assignment['eqp_no'])) {
                    \Log::warning('Invalid equipment assignment structure', ['index' => $index, 'assignment' => $assignment]);
                    continue;
                }
                
                $equipment = Equipment::where('eqp_no', $assignment['eqp_no'])->first();
                
                if (!$equipment) {
                    \Log::warning('Equipment not found', ['eqp_no' => $assignment['eqp_no']]);
                    continue; // Skip missing equipment instead of failing completely
                }
                
                // Parse equipment parameters with enhanced validation
                // Use pre-calculated oee_capa field from database
                $dailyCapacity = null;
                if ($equipment->oee_capa && is_numeric($equipment->oee_capa)) {
                    $dailyCapacity = floatval($equipment->oee_capa);
                    \Log::info('Using database oee_capa', [
                        'eqp_no' => $assignment['eqp_no'],
                        'oee_capa' => $dailyCapacity
                    ]);
                } else {
                    // Fallback calculation if oee_capa is not available
                    \Log::warning('oee_capa not available, using fallback calculation', [
                        'eqp_no' => $assignment['eqp_no'],
                        'oee_capa_value' => $equipment->oee_capa
                    ]);
                    
                    // Calculate manually using the equipment model helper method
                    $dailyCapacity = $equipment->getDailyCapacity();
                    
                    if (!$dailyCapacity) {
                        \Log::warning('Failed to get daily capacity', [
                            'eqp_no' => $assignment['eqp_no'],
                            'oee_capa' => $equipment->oee_capa,
                            'loading_speed' => $equipment->loading_speed,
                            'operation_time' => $equipment->operation_time
                        ]);
                        continue;
                    }
                }
                
                if ($dailyCapacity <= 0) {
                    \Log::warning('Invalid daily capacity', [
                        'eqp_no' => $assignment['eqp_no'],
                        'oee_capacity' => $dailyCapacity
                    ]);
                    continue;
                }
                
                // Parse and validate NG percentage
                $ngPercent = floatval($assignment['ng_percent'] ?? 0);
                
                // Validate NG percentage
                if ($ngPercent < 0 || $ngPercent > 100) {
                    \Log::warning('Invalid NG percentage, using 0', [
                        'eqp_no' => $assignment['eqp_no'],
                        'ng_percent' => $ngPercent
                    ]);
                    $ngPercent = 0;
                }
                
                // Calculate net good production rate per minute
                $grossRatePerMinute = $dailyCapacity / 1440; // Convert daily to per minute
                $netGoodRatePerMinute = $grossRatePerMinute * (1 - $ngPercent / 100); // Apply NG percentage
                
                // Validate calculated rates
                if ($netGoodRatePerMinute <= 0) {
                    \Log::warning('Invalid net production rate', [
                        'eqp_no' => $assignment['eqp_no'],
                        'oee_capacity' => $dailyCapacity,
                        'gross_rate' => $grossRatePerMinute,
                        'ng_percent' => $ngPercent,
                        'net_rate' => $netGoodRatePerMinute
                    ]);
                    continue;
                }
                
                // Parse start time with enhanced error handling
                $startRaw = $assignment['start_time'] ?? '';
                if (empty($startRaw)) {
                    \Log::warning('Empty start time for equipment', ['eqp_no' => $assignment['eqp_no']]);
                    continue;
                }
                
                // Normalize start time format
                if (strpos($startRaw, 'T') !== false) {
                    $startRaw = str_replace('T', ' ', $startRaw);
                }
                if (substr_count($startRaw, ':') === 1) {
                    $startRaw .= ':00';
                }
                
                // Parse start time with multiple fallback methods
                // NOTE: datetime-local input from frontend is already in local timezone (Asia/Manila)
                // We should NOT apply additional timezone conversion to avoid time shifting
                $startTime = null;
                try {
                    // Parse as local time WITHOUT timezone conversion to prevent time shifting
                    // Create Carbon instance in the app timezone directly without conversion
                    $startTime = Carbon::createFromFormat('Y-m-d H:i:s', $startRaw, config('app.timezone'));

                } catch (\Exception $e1) {
                    try {
                        // Fallback: Parse without timezone and assume it's already in app timezone
                        $startTime = Carbon::parse($startRaw, config('app.timezone'));
                    } catch (\Exception $e2) {
                        \Log::warning('Failed to parse start time', [
                            'eqp_no' => $assignment['eqp_no'],
                            'raw_start_time' => $startRaw,
                            'error1' => $e1->getMessage(),
                            'error2' => $e2->getMessage()
                        ]);
                        continue; // Skip equipment with unparseable start time
                    }
                }
                
                \Log::info('Start time parsing debug', [
                    'eqp_no' => $assignment['eqp_no'],
                    'raw_input' => $startRaw,
                    'parsed_time' => $startTime ? $startTime->format('Y-m-d H:i:s') : 'null',
                    'timezone' => $startTime ? $startTime->timezone->getName() : 'null',
                    'timestamp' => $startTime ? $startTime->timestamp : 'null'
                ]);
                
                if (!$startTime) {
                    \Log::warning('Start time is null after parsing', ['eqp_no' => $assignment['eqp_no']]);
                    continue; // Skip equipment with null start time
                }
                
                // Build equipment profile for shared-load calculation
                $profiles[] = [
                    'eqp_no' => $assignment['eqp_no'],
                    'start_time' => $startTime,
                    'rate_per_minute' => $netGoodRatePerMinute,
                    'oee_capacity' => $dailyCapacity,
                    'ng_percent' => $ngPercent
                ];
            }
            
            if (empty($profiles)) {
                \Log::warning('No valid equipment profiles created from assignments', [
                    'total_assignments' => count($equipmentAssignments),
                    'lot_qty' => $lotQty
                ]);
                return [
                    'success' => false,
                    'message' => 'No valid equipment assignments found after validation.'
                ];
            }
            
            \Log::info('Starting shared-load calculation', [
                'lot_qty' => $lotQty,
                'equipment_count' => count($profiles),
                'current_time' => now()->format('Y-m-d H:i:s'),
                'current_timestamp' => now()->timestamp,
                'timezone' => config('app.timezone'),
                'profiles' => array_map(function($p) {
                    return [
                        'eqp_no' => $p['eqp_no'],
                        'start_time' => $p['start_time']->format('Y-m-d H:i:s'),
                        'start_timestamp' => $p['start_time']->timestamp,
                        'rate_per_minute' => round($p['rate_per_minute'], 4),
                        'oee_capacity' => $p['oee_capacity']
                    ];
                }, $profiles)
            ]);
            
            // Sort profiles by start time for consistent calculation
            usort($profiles, function($a, $b) {
                return $a['start_time']->timestamp <=> $b['start_time']->timestamp;
            });
            
            // CORRECTED CALCULATION: Match frontend logic
            // Parallel processing with staggered equipment starts
            $earliestStart = $profiles[0]['start_time'];
            $latestStart = end($profiles)['start_time'];
            $totalRate = array_sum(array_column($profiles, 'rate_per_minute'));
            
            // Validate total rate to prevent division by zero
            if ($totalRate <= 0) {
                \Log::error('Total production rate is zero or negative', [
                    'total_rate' => $totalRate,
                    'equipment_count' => count($profiles)
                ]);
                return [
                    'success' => false,
                    'message' => 'Equipment production rate is invalid. Please check equipment data.'
                ];
            }
            
            // Create timeline of equipment starts
            $startEvents = [];
            foreach ($profiles as $profile) {
                $startEvents[] = [
                    'time' => $profile['start_time'],
                    'rate' => $profile['rate_per_minute']
                ];
            }
            
            // Sort by start time
            usort($startEvents, function($a, $b) {
                return $a['time']->timestamp <=> $b['time']->timestamp;
            });
            
            $remainingQty = $lotQty;
            $currentTime = $earliestStart;
            $currentRate = 0;
            
            // Process each time period as equipment comes online
            for ($i = 0; $i < count($startEvents); $i++) {
                $event = $startEvents[$i];
                $nextTime = $i < count($startEvents) - 1 ? $startEvents[$i + 1]['time'] : null;

                // Add this equipment's rate
                $currentRate += $event['rate'];

                if ($nextTime && $remainingQty > 0) {
                    // Calculate production until next equipment starts
                    $timePeriodMinutes = $event['time']->diffInMinutes($nextTime, false);
                    $production = min($remainingQty, $currentRate * $timePeriodMinutes);
                    $remainingQty -= $production;
                    $currentTime = $nextTime;

                    \Log::info('Processing time period', [
                        'event_time' => $event['time']->format('Y-m-d H:i:s'),
                        'next_time' => $nextTime->format('Y-m-d H:i:s'),
                        'time_period_minutes' => $timePeriodMinutes,
                        'current_rate' => $currentRate,
                        'production' => $production,
                        'remaining_qty' => $remainingQty
                    ]);
                }
            }
            
            // Calculate final completion time with all equipment running
            $finalTimeMinutes = $remainingQty / $totalRate;
            $estimatedEndtime = $currentTime->copy()->addMinutes($finalTimeMinutes);

            \Log::info('Final calculation step', [
                'remaining_qty' => $remainingQty,
                'total_rate' => $totalRate,
                'final_time_minutes' => $finalTimeMinutes,
                'current_time' => $currentTime->format('Y-m-d H:i:s'),
                'estimated_endtime' => $estimatedEndtime->format('Y-m-d H:i:s')
            ]);
            
            // Calculate summary statistics
            $totalCapacity = array_sum(array_column($profiles, 'oee_capacity'));
            $baselineMinutes = $lotQty / $totalRate;
            $actualProcessingMinutes = $earliestStart->diffInMinutes($estimatedEndtime, false);
            $processingHours = $actualProcessingMinutes / 60;
            
            \Log::info('Corrected calculation completed successfully', [
                'lot_qty' => $lotQty,
                'estimated_endtime' => $estimatedEndtime->format('Y-m-d H:i:s'),
                'processing_hours' => round($processingHours, 2),
                'total_capacity' => $totalCapacity,
                'equipment_count' => count($profiles),
                'earliest_start' => $earliestStart->format('Y-m-d H:i:s'),
                'latest_start' => $latestStart->format('Y-m-d H:i:s'),
                'baseline_minutes' => round($baselineMinutes, 2),
                'calculation_method' => 'staggered_parallel_processing'
            ]);
            
            return [
                'success' => true,
                'est_endtime' => $estimatedEndtime,
                'processing_hours' => round($processingHours, 2),
                'total_capacity' => $totalCapacity,
                'equipment_count' => count($profiles),
                'earliest_start' => $profiles[0]['start_time'],
                'latest_start' => end($profiles)['start_time'],
                'total_active_rate_per_minute' => $totalRate,
                'calculation_method' => 'staggered_parallel_processing',
                'baseline_minutes' => $baselineMinutes,
                'remaining_qty' => $remainingQty,
                'profiles_used' => count($profiles)
            ];
            
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Calculation error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Update equipment ongoing_lot column when lots are assigned or unassigned
     */
    private function updateEquipmentOngoingLot($equipmentAssignments, $lotId, $action = 'assign')
    {
        try {
            foreach ($equipmentAssignments as $assignment) {
                $equipmentNo = $assignment['eqp_no'];
                
                if ($action === 'assign') {
                    // Assign lot to equipment
                    Equipment::where('eqp_no', $equipmentNo)
                        ->update(['ongoing_lot' => $lotId]);
                } elseif ($action === 'unassign') {
                    // Remove lot from equipment (set to null)
                    Equipment::where('eqp_no', $equipmentNo)
                        ->where('ongoing_lot', $lotId)
                        ->update(['ongoing_lot' => null]);
                } elseif ($action === 'clear') {
                    // Clear specific equipment (used for updates)
                    Equipment::where('eqp_no', $equipmentNo)
                        ->update(['ongoing_lot' => null]);
                }
            }
            
            \Log::info("Equipment ongoing_lot updated", [
                'action' => $action,
                'lot_id' => $lotId,
                'equipment_count' => count($equipmentAssignments)
            ]);
            
        } catch (\Exception $e) {
            \Log::error("Failed to update equipment ongoing_lot", [
                'error' => $e->getMessage(),
                'lot_id' => $lotId,
                'action' => $action
            ]);
        }
    }
    
    /**
     * Clear equipment ongoing_lot for a specific lot (used when lot is submitted or deleted)
     */
    private function clearEquipmentOngoingLot($lotId)
    {
        try {
            Equipment::where('ongoing_lot', $lotId)
                ->update(['ongoing_lot' => null]);
                
            \Log::info("Equipment ongoing_lot cleared for lot", [
                'lot_id' => $lotId
            ]);
            
        } catch (\Exception $e) {
            \Log::error("Failed to clear equipment ongoing_lot", [
                'error' => $e->getMessage(),
                'lot_id' => $lotId
            ]);
        }
    }

    /**
     * Lookup lot details from updatewip database.
     */
    public function lookupLot($lotId)
    {
        try {
            // Look up lot in updatewip table
            $wipLot = UpdateWip::where('lot_id', $lotId)
                ->select('lot_id', 'model_15', 'lot_size', 'lot_qty', 'stagnant_tat', 'work_type', 'lot_status', 'lipas_yn')
                ->first();
            
            if ($wipLot) {
                return response()->json([
                    'success' => true,
                    'lot' => [
                        'lot_id' => $wipLot->lot_id,
                        'model_15' => $wipLot->model_15,
                        'lot_size' => $wipLot->lot_size,
                        'lot_qty' => $wipLot->lot_qty,
                        'stagnant_tat' => $wipLot->stagnant_tat,
                        'work_type' => $wipLot->work_type,
                        'lot_type' => $wipLot->lot_status, // Map lot_status to lot_type
                        'lipas_yn' => $wipLot->lipas_yn,
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Lot not found in database.'
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error looking up lot: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the details of a specific lot entry.
     * Recalculates endtime to show current accurate values.
     */
    public function show($id)
    {
        try {
            $lot = Endtime::findOrFail($id);
            
            // Format datetime fields properly for frontend consumption
            $lotData = $lot->toArray();
            
            // Format start time fields to ensure proper datetime format
            for ($i = 1; $i <= 10; $i++) {
                $startTimeField = 'start_time_' . $i;
                if (!empty($lotData[$startTimeField])) {
                    try {
                        // Ensure datetime is in proper format (Y-m-d H:i:s)
                        $lotData[$startTimeField] = Carbon::parse($lotData[$startTimeField])->format('Y-m-d H:i:s');
                    } catch (\Exception $e) {
                        \Log::warning('Error formatting start time', ['field' => $startTimeField, 'value' => $lotData[$startTimeField], 'error' => $e->getMessage()]);
                        // Keep original value if parsing fails
                    }
                }
            }
            
            // Recalculate endtime with current equipment assignments to show accurate values
            $equipmentAssignments = [];
            for ($i = 1; $i <= 10; $i++) {
                $eqpField = 'eqp_' . $i;
                $startTimeField = 'start_time_' . $i;
                $ngPercentField = 'ng_percent_' . $i;
                
                if (!empty($lot->$eqpField) && !empty($lot->$startTimeField)) {
                    $equipmentAssignments[] = [
                        'eqp_no' => $lot->$eqpField,
                        'start_time' => $lot->$startTimeField,
                        'ng_percent' => $lot->$ngPercentField ?: 0
                    ];
                }
            }
            
            // Try to recalculate endtime if we have equipment assignments, but don't fail if it errors
            if (!empty($equipmentAssignments)) {
                try {
                    $calculationResult = $this->calculateEndtime($lot->lot_qty, $equipmentAssignments);
                    
                    if ($calculationResult['success']) {
                        // Update the endtime in the response with recalculated value
                        $lotData['est_endtime'] = $calculationResult['est_endtime']->format('Y-m-d H:i:s');
                        $lotData['recalculated'] = true;
                        $lotData['calculation_details'] = [
                            'processing_hours' => $calculationResult['processing_hours'] ?? 0,
                            'total_capacity' => $calculationResult['total_capacity'] ?? 0,
                            'equipment_count' => $calculationResult['equipment_count'] ?? 0,
                            'earliest_start' => isset($calculationResult['earliest_start']) ? 
                                $calculationResult['earliest_start']->format('Y-m-d H:i:s') : null,
                            'calculation_method' => $calculationResult['calculation_method'] ?? 'piecewise_integration',
                            'equipment_details' => $this->formatEquipmentDetails($equipmentAssignments)
                        ];
                    } else {
                        \Log::warning('Endtime recalculation failed', ['lot_id' => $lot->id, 'message' => $calculationResult['message'] ?? 'Unknown error']);
                        // Fall back to formatting stored endtime
                        if (!empty($lotData['est_endtime'])) {
                            $lotData['est_endtime'] = Carbon::parse($lotData['est_endtime'])->format('Y-m-d H:i:s');
                        }
                        $lotData['recalculated'] = false;
                    }
                } catch (\Exception $e) {
                    \Log::error('Exception during endtime recalculation', ['lot_id' => $lot->id, 'error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
                    // Fall back to formatting stored endtime
                    if (!empty($lotData['est_endtime'])) {
                        $lotData['est_endtime'] = Carbon::parse($lotData['est_endtime'])->format('Y-m-d H:i:s');
                    }
                    $lotData['recalculated'] = false;
                }
            } else {
                // Format stored est_endtime if no recalculation possible
                if (!empty($lotData['est_endtime'])) {
                    try {
                        $lotData['est_endtime'] = Carbon::parse($lotData['est_endtime'])->format('Y-m-d H:i:s');
                    } catch (\Exception $e) {
                        \Log::warning('Error formatting stored endtime', ['lot_id' => $lot->id, 'endtime' => $lotData['est_endtime']]);
                    }
                }
                $lotData['recalculated'] = false;
            }
            
            return response()->json([
                'success' => true,
                'lot' => $lotData
            ]);
        } catch (\Exception $e) {
            \Log::error('EndtimeController@show error', [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Lot not found.'
            ], 404);
        }
    }

    /**
     * Get equipment data for JavaScript consumption (AJAX endpoint).
     */
    public function getEquipmentData()
    {
        try {
            $availableEquipment = Equipment::where('eqp_status', 'OPERATIONAL')
                ->select(
                    'eqp_no', 'eqp_line', 'eqp_area', 'eqp_type', 'eqp_maker',
                    'cam_class', 'insp_type', 'linear_type', 'size', 'alloc_type',
                    'oee_capa', 'eqp_oee', 'eqp_passing', 'eqp_yield', 'loading_speed', 'operation_time'
                )->orderBy('eqp_line')->orderBy('eqp_area')->orderBy('eqp_no')->get();
            
            return response()->json([
                'success' => true,
                'equipment' => $availableEquipment,
                'count' => $availableEquipment->count()
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load equipment data.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get lot data for edit modal (AJAX endpoint).
     */
    public function getEditData($id)
    {
        // Check permission for update action
        if (!auth()->user()->isUser() && !auth()->user()->isManager() && !auth()->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to edit endtime records.'
            ], 403);
        }
        
        try {
            $lot = Endtime::findOrFail($id);
            
            // Check if lot status allows editing - only 'Ongoing' lots can be edited
            if ($lot->status !== 'Ongoing') {
                return response()->json([
                    'success' => false,
                    'message' => "Cannot edit lot {$lot->lot_id} - Only lots with 'Ongoing' status can be edited. Current status: {$lot->status}"
                ], 400);
            }
            
            // Get available equipment for the form (only OPERATIONAL status)
            $availableEquipment = Equipment::where('eqp_status', 'OPERATIONAL')
                ->select(
                    'eqp_no', 'eqp_line', 'eqp_area', 'eqp_type', 'eqp_maker',
                    'cam_class', 'insp_type', 'linear_type', 'size', 'alloc_type',
                    'oee_capa', 'eqp_oee', 'eqp_passing', 'eqp_yield', 'loading_speed', 'operation_time'
                )->orderBy('eqp_line')->orderBy('eqp_area')->orderBy('eqp_no')->get();
            
            // Format datetime fields properly for frontend consumption
            $lotData = $lot->toArray();
            
            // Format start time fields to ensure proper datetime format for datetime-local inputs
            for ($i = 1; $i <= 10; $i++) {
                $startTimeField = 'start_time_' . $i;
                if (!empty($lotData[$startTimeField])) {
                    // Format for datetime-local input (Y-m-d\TH:i)
                    $lotData[$startTimeField] = Carbon::parse($lotData[$startTimeField])->format('Y-m-d\TH:i');
                }
            }
            
            return response()->json([
                'success' => true,
                'lot' => $lotData,
                'availableEquipment' => $availableEquipment
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lot not found.'
            ], 404);
        }
    }

    /**
     * Show the form for editing the specified lot entry.
     */
    public function edit($id)
    {
        // Check permission for update action
        if (!auth()->user()->isUser() && !auth()->user()->isManager() && !auth()->user()->isAdmin()) {
            abort(403, 'You do not have permission to edit endtime records.');
        }
        try {
            $lot = Endtime::findOrFail($id);
            
            // Check if lot status allows editing - only 'Ongoing' lots can be edited
            if ($lot->status !== 'Ongoing') {
                return redirect()->route('endtime.index')
                    ->with('error', "Cannot edit lot {$lot->lot_id} - Only lots with 'Ongoing' status can be edited. Current status: {$lot->status}");
            }
            
            // Get available equipment for the form (only OPERATIONAL status)
            $availableEquipment = Equipment::where('eqp_status', 'OPERATIONAL')
                ->select(
                    'eqp_no', 'eqp_line', 'eqp_area', 'eqp_type', 'eqp_maker',
                    'cam_class', 'insp_type', 'linear_type', 'size', 'alloc_type',
                    'oee_capa', 'eqp_oee', 'eqp_passing', 'eqp_yield', 'loading_speed', 'operation_time'
                )->orderBy('eqp_line')->orderBy('eqp_area')->orderBy('eqp_no')->get();
            
            return view('endtime.edit', compact('lot', 'availableEquipment'));
        } catch (\Exception $e) {
            return redirect()->route('endtime.index')
                ->with('error', 'Lot not found.');
        }
    }

    /**
     * Update the specified lot entry.
     */
    public function update(Request $request, $id)
    {
        // Check permission for update action
        if (!auth()->user()->isUser() && !auth()->user()->isManager() && !auth()->user()->isAdmin()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to update endtime records.'
                ], 403);
            }
            abort(403, 'You do not have permission to update endtime records.');
        }
        // Validate the request (no uniqueness check for lot_id in edit mode)
        $request->validate([
            'lot_id' => 'required|string|max:20',
            'model_15' => 'nullable|string|max:15',
            'lot_size' => 'required|string|in:03,05,10,21,31',
            'lot_qty' => 'required|integer|min:1',
            'work_type' => 'required|string|in:NORMAL,OI REWORK,ADV,COMB,LY,RL,PROCESS RW,FSTOP,WH REWORK',
            'lot_type' => 'required|string|in:MAIN,WL/RW,RL/LY',
            'lipas_yn' => 'required|string|in:Y,N',
            'equipment' => 'required|array|min:1|max:10',
            'equipment.*.eqp_no' => 'required|string|exists:equipment,eqp_no',
            'equipment.*.start_time' => 'required|date',
            'equipment.*.ng_percent' => 'nullable|sometimes|numeric|min:0|max:100',
            'rl_ly_quantity' => 'nullable|integer|min:1',
        ]);

        try {
            DB::beginTransaction();

            $lot = Endtime::findOrFail($id);
            
            // Check if lot status allows updating - only 'Ongoing' lots can be updated
            if ($lot->status !== 'Ongoing') {
                $errorMessage = "Cannot update lot {$lot->lot_id} - Only lots with 'Ongoing' status can be updated. Current status: {$lot->status}";
                
                // Return JSON response for AJAX requests
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => $errorMessage
                    ], 400);
                }
                
                return redirect()->route('endtime.index')
                    ->with('error', $errorMessage);
            }

            // Get equipment data for calculations
            $equipmentAssignments = collect($request->equipment)
                ->filter(function ($assignment) {
                    return !empty($assignment['eqp_no']) && !empty($assignment['start_time']);
                })
                ->values();

            if ($equipmentAssignments->isEmpty()) {
                return back()->withErrors(['equipment' => 'At least one equipment assignment is required.']);
            }

            // Calculate endtime based on equipment assignments
            $result = $this->calculateEndtime($request->lot_qty, $equipmentAssignments);

            if (!$result['success']) {
                return back()->withErrors(['equipment' => $result['message']]);
            }

            // Update lot record
            $lot->lot_id = $request->lot_id;
            $lot->model_15 = $request->model_15;
            $lot->lot_size = $request->lot_size;
            $lot->lot_qty = $request->lot_qty;
            $lot->work_type = $request->work_type;
            $lot->lot_type = $request->lot_type;
            $lot->lipas_yn = $request->lipas_yn;
            $lot->est_endtime = $result['est_endtime'];
            
            // Clear existing equipment assignments
            for ($i = 1; $i <= 10; $i++) {
                $lot->{'eqp_' . $i} = null;
                $lot->{'start_time_' . $i} = null;
                $lot->{'ng_percent_' . $i} = 0;
            }
            
            // Set new equipment assignments, start times, and ng_percent
            foreach ($equipmentAssignments as $index => $assignment) {
                $equipmentFieldIndex = $index + 1;
                if ($equipmentFieldIndex <= 10) {
                    $equipmentFieldName = 'eqp_' . $equipmentFieldIndex;
                    $startTimeFieldName = 'start_time_' . $equipmentFieldIndex;
                    $ngPercentFieldName = 'ng_percent_' . $equipmentFieldIndex;
                    $lot->$equipmentFieldName = $assignment['eqp_no'];
                    // Normalize 'datetime-local' (YYYY-MM-DDTHH:MM) to DB format without timezone shifting
                    $startTimeString = str_replace('T', ' ', $assignment['start_time']);
                    if (substr_count($startTimeString, ':') === 1) {
                        $startTimeString .= ':00';
                    }
                    // Parse and store in app timezone without conversion
                    $startTimeString = Carbon::createFromFormat('Y-m-d H:i:s', $startTimeString, config('app.timezone'))->format('Y-m-d H:i:s');
                    // Store as plain "Y-m-d H:i:s" string to avoid implicit timezone conversions
                    $lot->$startTimeFieldName = $startTimeString;
                    $lot->$ngPercentFieldName = $assignment['ng_percent'] ?? 0;
                }
            }
            
            // Update equipment line and area from the first equipment
            $firstEquipment = Equipment::where('eqp_no', $equipmentAssignments->first()['eqp_no'])->first();
            if ($firstEquipment) {
                $lot->eqp_line = $firstEquipment->eqp_line;
                $lot->eqp_area = $firstEquipment->eqp_area;
            }
            
            $lot->save();
            
            // Clear old equipment assignments and set new ones for ongoing_lot
            $this->clearEquipmentOngoingLot($lot->lot_id);
            $this->updateEquipmentOngoingLot($equipmentAssignments, $lot->lot_id, 'assign');

            DB::commit();

            $successMessage = "Lot {$request->lot_id} has been successfully updated with estimated end time: " . 
                             $result['est_endtime']->format('M d, Y H:i');
            
            // Return JSON response for AJAX requests
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $successMessage,
                    'lot' => $lot->fresh(), // Return updated lot data
                    'est_endtime' => $result['est_endtime']->format('Y-m-d H:i:s')
                ]);
            }

            return redirect()->route('endtime.index')
                ->with('success', $successMessage);

        } catch (\Exception $e) {
            DB::rollBack();
            
            $errorMessage = 'Failed to update lot entry: ' . $e->getMessage();
            
            // Return JSON response for AJAX requests
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $errorMessage
                ], 500);
            }
            
            return back()->withErrors(['error' => $errorMessage]);
        }
    }

    /**
     * Remove the specified lot entry from storage.
     */
    public function destroy($id)
    {
        // Check permission for delete action - only Manager and Admin can delete
        if (!auth()->user()->isManager() && !auth()->user()->isAdmin()) {
            abort(403, 'You do not have permission to delete endtime records.');
        }
        try {
            $lot = Endtime::findOrFail($id);
            $lotId = $lot->lot_id;
            
            // Clear equipment ongoing_lot before deleting the lot
            $this->clearEquipmentOngoingLot($lotId);
            
            $lot->delete();
            
            return response()->json([
                'success' => true,
                'message' => "Lot {$lotId} has been successfully deleted."
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete the lot. Please try again.'
            ], 500);
        }
    }
    
    /**
     * Get all ongoing lots for the submitted lot modal.
     */
    public function getOngoingLots()
    {
        try {
            $ongoingLots = Endtime::where('status', 'Ongoing')
                ->select('id', 'lot_id', 'model_15', 'lot_qty', 'work_type', 'lot_type', 'eqp_line', 'eqp_area', 'est_endtime')
                ->orderBy('est_endtime', 'asc')
                ->get();
            
            return response()->json([
                'success' => true,
                'lots' => $ongoingLots
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch ongoing lots: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Submit a lot (update from Ongoing to Submitted status).
     */
    public function submitLot(Request $request)
    {
        // Check permission for update action
        if (!auth()->user()->isUser() && !auth()->user()->isManager() && !auth()->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have permission to submit lots.'
            ], 403);
        }
        
        // Validate the request
        $request->validate([
            'lot_id' => 'required|exists:endtime,id',
            'status' => 'required|string|in:Submitted',
            'actual_submitted_at' => 'required|date',
            'submission_notes' => 'nullable|string|max:1000',
            'remarks' => 'nullable|string|max:50'
        ]);
        
        try {
            DB::beginTransaction();
            
            $lot = Endtime::findOrFail($request->lot_id);
            
            // Check if lot is in Ongoing status
            if ($lot->status !== 'Ongoing') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only ongoing lots can be submitted. Current status: ' . $lot->status
                ], 400);
            }
            
            // Parse the actual submission time in app timezone without conversion
            $actualSubmittedAt = Carbon::parse($request->actual_submitted_at, config('app.timezone'));
            $estEndTime = Carbon::parse($lot->est_endtime, config('app.timezone'));
            
            // Calculate the difference in minutes
            $timeDifferenceMinutes = $actualSubmittedAt->diffInMinutes($estEndTime, false);
            
            // Determine the result based on 30-minute threshold
            $remarks = '';
            if ($timeDifferenceMinutes > 30) {
                // Submitted more than 30 minutes before estimated end time
                $remarks = 'Early';
            } elseif ($timeDifferenceMinutes < -30) {
                // Submitted more than 30 minutes after estimated end time
                $remarks = 'Delayed';
            } else {
                // Submitted within 30 minutes of estimated end time
                $remarks = 'OK';
            }
            
            // Update the lot status and submitted information
            $lot->status = $request->status;
            $lot->actual_submitted_at = $actualSubmittedAt;
            $lot->modified_by = Auth::user()->emp_name ?? Auth::user()->emp_no;
            $lot->remarks = $remarks;
            $lot->submission_notes = $request->submission_notes;
            
            $lot->save();
            
            // Clear equipment ongoing_lot for this lot since it's now submitted
            $this->clearEquipmentOngoingLot($lot->lot_id);
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => "Lot {$lot->lot_id} has been successfully submitted with result: {$remarks}.",
                'lot' => $lot,
                'result_details' => [
                    'remarks' => $remarks,
                    'time_difference_minutes' => $timeDifferenceMinutes,
                    'actual_submitted_at' => $actualSubmittedAt->format('Y-m-d H:i:s'),
                    'est_endtime' => $estEndTime->format('Y-m-d H:i:s')
                ]
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit lot: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Submit an existing ongoing lot (alternative method for the create page form).
     */
    public function submitExisting(Request $request)
    {
        // Check permission for update action
        if (!auth()->user()->isUser() && !auth()->user()->isManager() && !auth()->user()->isAdmin()) {
            return redirect()->route('endtime.index')
                ->with('error', 'You do not have permission to submit lots.');
        }
        
        // Validate the request
        $request->validate([
            'lot_id' => 'required|exists:endtime,id',
            'status' => 'required|string|in:Submitted',
            'actual_submitted_at' => 'required|date',
            'submission_notes' => 'nullable|string|max:1000',
            'remarks' => 'nullable|string|max:50'
        ]);
        
        try {
            DB::beginTransaction();
            
            $lot = Endtime::findOrFail($request->lot_id);
            
            // Check if lot is in Ongoing status
            if ($lot->status !== 'Ongoing') {
                return redirect()->route('endtime.create')
                    ->with('error', 'Only ongoing lots can be submitted. Current status: ' . $lot->status);
            }
            
            // Parse the actual submission time in app timezone without conversion
            $actualSubmittedAt = Carbon::parse($request->actual_submitted_at, config('app.timezone'));
            $estEndTime = Carbon::parse($lot->est_endtime, config('app.timezone'));
            
            // Calculate the difference in minutes
            $timeDifferenceMinutes = $actualSubmittedAt->diffInMinutes($estEndTime, false);
            
            // Determine the result based on 30-minute threshold
            $remarks = '';
            if ($timeDifferenceMinutes > 30) {
                // Submitted more than 30 minutes before estimated end time
                $remarks = 'Early';
            } elseif ($timeDifferenceMinutes < -30) {
                // Submitted more than 30 minutes after estimated end time
                $remarks = 'Delayed';
            } else {
                // Submitted within 30 minutes of estimated end time
                $remarks = 'OK';
            }
            
            // Update the lot status and submitted information
            $lot->status = $request->status;
            $lot->actual_submitted_at = $actualSubmittedAt;
            $lot->modified_by = Auth::user()->emp_name ?? Auth::user()->emp_no;
            $lot->remarks = $remarks;
            $lot->submission_notes = $request->submission_notes;
            
            $lot->save();
            
            // Clear equipment ongoing_lot for this lot since it's now submitted
            $this->clearEquipmentOngoingLot($lot->lot_id);
            
            DB::commit();
            
            return redirect()->route('endtime.index')
                ->with('success', "Lot {$lot->lot_id} has been successfully submitted with result: {$remarks}.");
            
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route('endtime.create')
                ->with('error', 'Failed to submit lot: ' . $e->getMessage());
        }
    }
    
    /**
     * Format start time for datetime-local input with error handling
     */
    private function formatStartTimeForInput($startTime)
    {
        if (!$startTime) {
            return null;
        }
        
        try {
            $carbonTime = Carbon::parse($startTime);
            return $carbonTime->format('Y-m-d\TH:i');
        } catch (\Exception $e) {
            \Log::warning('Failed to format start time for input', [
                'start_time' => $startTime,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    
    /**
     * Get lot details for API (for modal view)
     */
    public function getLot($id)
    {
        try {
            $lot = Endtime::findOrFail($id);
            
            // Format the data for display
            $formattedLot = [
                'id' => $lot->id,
                'lot_id' => $lot->lot_id,
                'model_15' => $lot->model_15,
                'lot_size' => $lot->lot_size,
                'lot_qty' => $lot->lot_qty,
                'work_type' => $lot->work_type,
                'lot_type' => $lot->lot_type,
                'lipas_yn' => $lot->lipas_yn,
                'status' => $lot->status,
                'remarks' => $lot->remarks,
                'eqp_1' => $lot->eqp_1,
                'eqp_2' => $lot->eqp_2,
                'eqp_3' => $lot->eqp_3,
                'eqp_4' => $lot->eqp_4,
                'eqp_5' => $lot->eqp_5,
                'eqp_6' => $lot->eqp_6,
                'eqp_7' => $lot->eqp_7,
                'eqp_8' => $lot->eqp_8,
                'eqp_9' => $lot->eqp_9,
                'eqp_10' => $lot->eqp_10,
                'start_time_1' => $lot->start_time_1 ? $this->formatStartTimeForInput($lot->start_time_1) : null,
                'start_time_2' => $lot->start_time_2 ? $this->formatStartTimeForInput($lot->start_time_2) : null,
                'start_time_3' => $lot->start_time_3 ? $this->formatStartTimeForInput($lot->start_time_3) : null,
                'start_time_4' => $lot->start_time_4 ? $this->formatStartTimeForInput($lot->start_time_4) : null,
                'start_time_5' => $lot->start_time_5 ? $this->formatStartTimeForInput($lot->start_time_5) : null,
                'start_time_6' => $lot->start_time_6 ? $this->formatStartTimeForInput($lot->start_time_6) : null,
                'start_time_7' => $lot->start_time_7 ? $this->formatStartTimeForInput($lot->start_time_7) : null,
                'start_time_8' => $lot->start_time_8 ? $this->formatStartTimeForInput($lot->start_time_8) : null,
                'start_time_9' => $lot->start_time_9 ? $this->formatStartTimeForInput($lot->start_time_9) : null,
                'start_time_10' => $lot->start_time_10 ? $this->formatStartTimeForInput($lot->start_time_10) : null,
                'ng_percent_1' => $lot->ng_percent_1 ?? 0,
                'ng_percent_2' => $lot->ng_percent_2 ?? 0,
                'ng_percent_3' => $lot->ng_percent_3 ?? 0,
                'ng_percent_4' => $lot->ng_percent_4 ?? 0,
                'ng_percent_5' => $lot->ng_percent_5 ?? 0,
                'ng_percent_6' => $lot->ng_percent_6 ?? 0,
                'ng_percent_7' => $lot->ng_percent_7 ?? 0,
                'ng_percent_8' => $lot->ng_percent_8 ?? 0,
                'ng_percent_9' => $lot->ng_percent_9 ?? 0,
                'ng_percent_10' => $lot->ng_percent_10 ?? 0,
                'eqp_line' => $lot->eqp_line,
                'eqp_area' => $lot->eqp_area,
                'est_endtime' => $lot->est_endtime,
                'est_endtime_formatted' => $lot->est_endtime ? Carbon::parse($lot->est_endtime)->format('M d, H:i') : null,
                'est_endtime_relative' => $lot->getProcessingDurationFormatted() ?: 'Duration N/A',
                'actual_submitted_at' => $lot->actual_submitted_at,
                'actual_submitted_at_formatted' => $lot->actual_submitted_at ? Carbon::parse($lot->actual_submitted_at)->format('M d, H:i') : null,
                'created_at' => $lot->created_at,
                'created_at_formatted' => $lot->created_at ? $lot->created_at->format('M d, Y H:i') : null,
                'updated_at' => $lot->updated_at,
                'created_by' => $lot->created_by,
                'modified_by' => $lot->modified_by,
                'submission_notes' => $lot->submission_notes,
            ];
            
            return response()->json([
                'success' => true,
                'lot' => $formattedLot
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lot not found or error retrieving lot details: ' . $e->getMessage()
            ], 404);
        }
    }

    /**
     * Check if lot ID exists with Ongoing status (AJAX endpoint).
     */
    public function checkOngoingLot($lotId)
    {
        try {
            $lot = Endtime::where('lot_id', $lotId)
                          ->where('status', 'Ongoing')
                          ->first();

            if ($lot) {
                return response()->json([
                    'exists' => true,
                    'status' => 'Ongoing',
                    'id' => $lot->id,
                    'lot_id' => $lot->lot_id
                ]);
            } else {
                return response()->json([
                    'exists' => false,
                    'status' => null,
                    'id' => null,
                    'lot_id' => $lotId
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'exists' => false,
                'status' => null,
                'id' => null,
                'lot_id' => $lotId,
                'error' => 'Error checking lot status'
            ], 500);
        }
    }

    /**
     * Get equipment without ongoing lots (AJAX endpoint for modal).
     */
    public function getEquipmentWithoutOngoing(Request $request)
    {
        try {
            // Get filters and sorting from request
            $filters = [
                'eqp_line' => $request->get('eqp_line', 'all'),
                'eqp_area' => $request->get('eqp_area', 'all'),
            ];
            
            $sortBy = $request->get('sort_by', 'updated_at');
            $sortOrder = $request->get('sort_order', 'desc'); // desc = longest elapsed time first
            
            // Build query for equipment without ongoing lots
            $query = Equipment::where('eqp_status', 'OPERATIONAL')
                ->where(function($query) {
                    $query->whereNull('ongoing_lot')
                          ->orWhere('ongoing_lot', '')
                          ->orWhere('ongoing_lot', '0');
                });
            
            // Apply filters
            if ($filters['eqp_line'] !== 'all') {
                $query->where('eqp_line', $filters['eqp_line']);
            }
            
            if ($filters['eqp_area'] !== 'all') {
                $query->where('eqp_area', $filters['eqp_area']);
            }
            
            // Get equipment with essential details including updated_at
            $equipment = $query->select([
                'eqp_no', 'eqp_line', 'eqp_area', 'eqp_type', 'eqp_maker',
                'alloc_type', 'eqp_oee', 'eqp_passing', 'eqp_yield', 
                'loading_speed', 'operation_time', 'oee_capa', 'updated_at'
            ]);
            
            // Apply sorting
            switch ($sortBy) {
                case 'eqp_no':
                    $equipment->orderBy('eqp_no', $sortOrder);
                    break;
                case 'eqp_line':
                    $equipment->orderBy('eqp_line', $sortOrder)->orderBy('eqp_area', 'asc')->orderBy('eqp_no', 'asc');
                    break;
                case 'eqp_area':
                    $equipment->orderBy('eqp_area', $sortOrder)->orderBy('eqp_line', 'asc')->orderBy('eqp_no', 'asc');
                    break;
                case 'alloc_type':
                    $equipment->orderBy('alloc_type', $sortOrder)->orderBy('eqp_no', 'asc');
                    break;
                case 'oee_capa':
                    $equipment->orderBy('oee_capa', $sortOrder)->orderBy('eqp_no', 'asc');
                    break;
                case 'updated_at':
                default:
                    $equipment->orderBy('updated_at', $sortOrder)->orderBy('eqp_no', 'asc');
                    break;
            }
            
            $equipmentList = $equipment->get();
            
            // Add elapsed time calculation to each equipment
            $equipmentWithElapsed = $equipmentList->map(function ($eqp) {
                $updatedAt = $eqp->updated_at ? Carbon::parse($eqp->updated_at) : null;
                $now = Carbon::now();
                
                if ($updatedAt) {
                    $diffInMinutes = $updatedAt->diffInMinutes($now);
                    $elapsedTime = $this->formatElapsedTime($diffInMinutes);
                    $elapsedMinutes = $diffInMinutes; // For sorting purposes
                } else {
                    $elapsedTime = 'Unknown';
                    $elapsedMinutes = 999999; // Put unknowns at the end
                }
                
                return [
                    'eqp_no' => $eqp->eqp_no,
                    'eqp_line' => $eqp->eqp_line,
                    'eqp_area' => $eqp->eqp_area,
                    'eqp_type' => $eqp->eqp_type,
                    'eqp_maker' => $eqp->eqp_maker,
                    'alloc_type' => $eqp->alloc_type,
                    'eqp_oee' => $eqp->eqp_oee,
                    'oee_capa' => $eqp->oee_capa,
                    'updated_at' => $eqp->updated_at ? $eqp->updated_at->toISOString() : null,
                    'elapsed_time' => $elapsedTime,
                    'elapsed_minutes' => $elapsedMinutes
                ];
            });
            
            return response()->json([
                'success' => true,
                'equipment' => $equipmentWithElapsed,
                'count' => $equipmentWithElapsed->count(),
                'filters_applied' => $filters,
                'sort_by' => $sortBy,
                'sort_order' => $sortOrder
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching equipment without ongoing lots: ' . $e->getMessage(),
                'equipment' => [],
                'count' => 0
            ], 500);
        }
    }
    
    /**
     * Format elapsed time in a human-readable format
     */
    private function formatElapsedTime($minutes)
    {
        // Ensure we're working with integers
        $totalMinutes = intval($minutes);
        
        if ($totalMinutes < 60) {
            return $totalMinutes . 'm';
        } elseif ($totalMinutes < 1440) { // Less than 24 hours
            $hours = intval($totalMinutes / 60);
            $remainingMinutes = $totalMinutes % 60;
            return $hours . 'h' . ($remainingMinutes > 0 ? ' ' . $remainingMinutes . 'm' : '');
        } else {
            $days = intval($totalMinutes / 1440);
            $remainingMinutes = $totalMinutes % 1440;
            $remainingHours = intval($remainingMinutes / 60);
            return $days . 'd' . ($remainingHours > 0 ? ' ' . $remainingHours . 'h' : '');
        }
    }
}
