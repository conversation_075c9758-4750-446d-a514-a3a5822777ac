<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class LotAssignment extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'lot_request_id',
        'lot_id',
        'lot_code',
        'equipment_number',
        'equipment_code',
        'lot_quantity',
        'assigned_date',
        'assigned_by',
        'assigned_by_manager',
        'assignment_status',
        'picked_at',
        'delivered_at',
        'delivered_by',
        'lot_size',
        'eqp_type',
        'eqp_class',
        'work_type',
        'lipas_yn',
        'stagnant_tat',
        'lot_location'
    ];
    
    protected $casts = [
        'assigned_date' => 'datetime',
        'picked_at' => 'datetime',
        'delivered_at' => 'datetime',
        'lipas_yn' => 'boolean',
        'stagnant_tat' => 'integer',
        'lot_quantity' => 'integer',
    ];
    
    /**
     * Get the lot request this assignment belongs to
     */
    public function lotRequest()
    {
        return $this->belongsTo(LotRequest::class);
    }
    
    /**
     * Get the user who made the assignment
     */
    public function assignedBy()
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }
    
    /**
     * Get the equipment this lot is assigned to
     */
    public function equipment()
    {
        return $this->belongsTo(Equipment::class, 'equipment_number', 'eqp_no');
    }
    
    /**
     * Get the manager who made the assignment
     */
    public function assignedByManager()
    {
        return $this->belongsTo(User::class, 'assigned_by_manager', 'emp_no');
    }
    
    /**
     * Get the person who delivered the lot
     */
    public function deliveredByUser()
    {
        return $this->belongsTo(User::class, 'delivered_by', 'emp_no');
    }
    
    /**
     * Get assignment status badge class
     */
    public function getAssignmentStatusBadgeClass()
    {
        return match($this->assignment_status) {
            'pending' => 'bg-warning text-dark',
            'picked' => 'bg-info text-white',
            'delivered' => 'bg-success text-white',
            default => 'bg-secondary text-white'
        };
    }
    
    /**
     * Get formatted assignment status
     */
    public function getFormattedAssignmentStatusAttribute()
    {
        return match($this->assignment_status) {
            'pending' => 'Pending Pickup',
            'picked' => 'Picked from Storage',
            'delivered' => 'Delivered to Operator',
            default => ucfirst($this->assignment_status)
        };
    }
    
    /**
     * Mark lot as picked
     */
    public function markPicked($pickedBy = null)
    {
        $this->update([
            'assignment_status' => 'picked',
            'picked_at' => now()
        ]);
        
        return $this;
    }
    
    /**
     * Mark lot as delivered
     */
    public function markDelivered($deliveredBy = null)
    {
        $this->update([
            'assignment_status' => 'delivered',
            'delivered_at' => now(),
            'delivered_by' => $deliveredBy
        ]);
        
        return $this;
    }
    
    /**
     * Get priority level based on LIPAS and stagnant TAT
     */
    public function getPriorityLevelAttribute()
    {
        if ($this->lipas_yn) {
            return 'LIPAS';
        }
        
        $tat = $this->stagnant_tat ?? 0;
        if ($tat >= 72) return 'Critical';
        if ($tat >= 48) return 'High';
        if ($tat >= 24) return 'Medium';
        return 'Normal';
    }
    
    /**
     * Get priority badge class
     */
    public function getPriorityBadgeClassAttribute()
    {
        return match($this->priority_level) {
            'LIPAS' => 'bg-purple text-white',
            'Critical' => 'bg-danger',
            'High' => 'bg-warning',
            'Medium' => 'bg-info',
            default => 'bg-secondary'
        };
    }
    
    /**
     * Check if assignment matches equipment requirements
     */
    public function matchesEquipmentRequirements($equipment)
    {
        $matches = true;
        
        if ($equipment->size && $this->lot_size !== $equipment->size) {
            $matches = false;
        }
        
        if ($equipment->cam_class && $this->eqp_type !== $equipment->cam_class) {
            $matches = false;
        }
        
        if ($equipment->insp_type && $this->eqp_class !== $equipment->insp_type) {
            $matches = false;
        }
        
        if ($equipment->alloc_type && $this->work_type !== $equipment->alloc_type) {
            $matches = false;
        }
        
        return $matches;
    }
}
