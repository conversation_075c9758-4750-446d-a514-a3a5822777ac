<x-app-layout>
    <x-slot name="header">
        Data Management
    </x-slot>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-6 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary bg-opacity-10 text-primary me-3">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ number_format($stats['total_users']) }}</h3>
                            <p class="text-muted mb-0">Total Users</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-warning bg-opacity-10 text-warning me-3">
                            <i class="fas fa-hdd"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ $stats['storage_used'] }} MB</h3>
                            <p class="text-muted mb-0">Storage Used</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row g-4">
        <!-- Export Data -->
        <div class="col-12">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-download text-primary me-2"></i>Export Data
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">Download your data in CSV format for external use or backup purposes.</p>
                    
                    <div class="d-grid gap-3">
                        <a href="{{ route('management.data.export.users') }}" class="btn btn-outline-primary">
                            <i class="fas fa-users me-2"></i>Export Users
                            <small class="text-muted ms-2">({{ number_format($stats['total_users']) }} records)</small>
                        </a>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Backup Management -->
    <div class="row g-4 mt-2">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-database text-warning me-2"></i>Database Backup
                        </h5>
                        <div class="d-flex gap-2">
                            <a href="{{ route('management.data.backups') }}" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-list me-2"></i>View All Backups
                            </a>
                            <form action="{{ route('management.data.backup.create') }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn-warning btn-sm">
                                    <i class="fas fa-database me-2"></i>Create Backup
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <p class="text-muted mb-3">
                                Regular backups help protect your data. Create and download backups of your entire database including users and system data.
                            </p>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-calendar-alt text-muted me-2"></i>
                                        <span class="text-muted">Last Backup:</span>
                                        <strong class="ms-2">{{ $stats['last_backup'] }}</strong>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-hdd text-muted me-2"></i>
                                        <span class="text-muted">Storage Used:</span>
                                        <strong class="ms-2">{{ $stats['storage_used'] }} MB</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="backup-icon">
                                <i class="fas fa-shield-alt fa-4x text-warning mb-3"></i>
                                <p class="text-muted small">Your data is protected</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Management Tips -->
    <div class="row g-4 mt-2">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb text-info me-2"></i>Data Management Tips
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="tip-item">
                                <i class="fas fa-download text-primary mb-2"></i>
                                <h6>Regular Exports</h6>
                                <p class="text-muted small">Export your data regularly to keep external backups and for reporting purposes.</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="tip-item">
                                <i class="fas fa-database text-warning mb-2"></i>
                                <h6>Database Backups</h6>
                                <p class="text-muted small">Create database backups before major updates or changes to ensure data safety.</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="tip-item">
                                <i class="fas fa-shield-alt text-success mb-2"></i>
                                <h6>Data Security</h6>
                                <p class="text-muted small">Keep your exported data secure and only share with authorized personnel.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .stats-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }
        
        .tip-item {
            text-align: center;
            padding: 1rem;
        }
        
        .tip-item i {
            font-size: 2rem;
            display: block;
        }
    </style>
</x-app-layout>