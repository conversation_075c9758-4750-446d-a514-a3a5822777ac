<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        Lot Request Management System
     <?php $__env->endSlot(); ?>

    <style>
        /* Clean page background */
        .lot-requests-page {
            background: #f8f9ff;
            min-height: 100vh;
        }
        
        /* Simple request card */
        .request-card {
            background: white;
            border: 1px solid #e1e8ff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            transition: all 0.2s ease;
        }
        
        .request-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        
        /* Simple primary button */
        .btn-primary {
            background: #667eea;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.2s ease;
        }
        
        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        /* Simple fade-in effect */
        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .action-buttons-group {
                flex-direction: row;
                justify-content: center;
            }
            
            .action-btn {
                width: 28px;
                height: 28px;
                font-size: 0.75rem;
            }
        }
        
        /* Clean table styling */
        .table-responsive {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            background: white;
        }
        
        .table thead th {
            background: #667eea;
            color: white;
            font-weight: 600;
            padding: 1rem;
            border: none;
        }
        
        .table tbody tr {
            transition: background-color 0.2s ease;
            border: none;
        }
        
        .table tbody tr:hover {
            background: #f8f9ff;
        }
        
        .table tbody td {
            padding: 1rem;
            border-top: 1px solid #e1e8ff;
            vertical-align: middle;
        }
        
        /* Simple badges */
        .badge {
            font-weight: 500;
            padding: 0.4rem 0.7rem;
            border-radius: 6px;
        }
        
        /* Action buttons styling */
        .action-buttons-group {
            min-height: 32px;
        }
        
        .action-buttons-group .btn {
            min-width: 70px;
            padding: 0.375rem 0.75rem;
        }
        
        .action-buttons-group .vr {
            border-left: 1px solid #dee2e6;
            margin: 0;
        }
        
        /* Sortable header styling */
        .sortable {
            user-select: none;
            transition: background-color 0.2s ease;
        }
        
        .sortable:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
        }
        
        .sortable.sort-asc .fa-sort:before {
            content: "\f0de"; /* fa-sort-up */
        }
        
        .sortable.sort-desc .fa-sort:before {
            content: "\f0dd"; /* fa-sort-down */
        }
        
        
        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            border: none;
            font-size: 0.85rem;
        }
        
        .action-btn:hover {
            transform: translateY(-1px);
        }
        
        .view-btn {
            background: #17a2b8;
            color: white;
        }
        
        .view-btn:hover {
            background: #138496;
            color: white;
        }
        
        .edit-btn {
            background: #ffc107;
            color: #000;
        }
        
        .edit-btn:hover {
            background: #e0a800;
            color: #000;
        }
        
        .delete-btn {
            background: #dc3545;
            color: white;
        }
        
        .delete-btn:hover {
            background: #c82333;
            color: white;
        }
        
    </style>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-3 align-items-center flex-wrap">
                    <!-- Search Input -->
                    <div class="position-relative">
                        <input type="text" 
                               class="form-control search-input" 
                               placeholder="Search requests..."
                               id="searchInput"
                               style="width: 200px; padding-left: 2.5rem;">
                        <i class="fas fa-search position-absolute" 
                           style="left: 0.75rem; top: 50%; transform: translateY(-50%); color: #6c757d;"></i>
                    </div>
                    
                    <!-- Status Filter -->
                    <div class="filter-group">
                        <label class="form-label mb-1" style="font-size: 0.875rem; color: #6c757d;">Status</label>
                        <select class="form-select form-select-sm" id="statusFilter" style="width: 140px;">
                            <option value="all">All Status</option>
                            <option value="pending" selected>Pending</option>
                            <option value="accepted">Accepted</option>
                            <option value="assigned">Assigned</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                    
                    <!-- Size Filter -->
                    <div class="filter-group">
                        <label class="form-label mb-1" style="font-size: 0.875rem; color: #6c757d;">Size</label>
                        <select class="form-select form-select-sm" id="sizeFilter" style="width: 100px;">
                            <option value="all">All Sizes</option>
                            <option value="03">03</option>
                            <option value="05">05</option>
                            <option value="10">10</option>
                            <option value="21">21</option>
                            <option value="31">31</option>
                            <option value="32">32</option>
                        </select>
                    </div>
                    
                    <!-- Line Filter -->
                    <div class="filter-group">
                        <label class="form-label mb-1" style="font-size: 0.875rem; color: #6c757d;">Line</label>
                        <select class="form-select form-select-sm" id="lineFilter" style="width: 120px;">
                            <option value="all">All Lines</option>
                            <?php
                                // Get unique equipment lines from the lot requests
                                $uniqueLines = collect();
                                foreach($lotRequests as $request) {
                                    foreach($request->lotRequestItems as $item) {
                                        if($item->equipment && $item->equipment->eqp_line) {
                                            $uniqueLines->push($item->equipment->eqp_line);
                                        }
                                    }
                                }
                                $uniqueLines = $uniqueLines->unique()->sort()->values();
                            ?>
                            <?php $__currentLoopData = $uniqueLines; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $line): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($line); ?>"><?php echo e($line); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    
                    <!-- Clear Filters Button -->
                    <button type="button" class="btn btn-outline-secondary btn-sm" id="clearFilters" title="Clear all filters">
                        <i class="fas fa-times me-1"></i>Clear
                    </button>
                    
                    <a href="<?php echo e(route('lot-requests.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Lot Request
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="request-card fade-in">
                <div class="card-body p-4">
                    <?php if($lotRequests->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th class="sortable" data-sort="request_number" style="cursor: pointer;">
                                                            Request #
                                                        </th>
                                                        <th class="sortable" data-sort="requestor" style="cursor: pointer;">
                                                            Requestor
                                                        </th>
                                                        <th class="sortable" data-sort="equipment_type" style="cursor: pointer;">
                                                            Equipment Type
                                                        </th>
                                                        <th class="sortable" data-sort="line_area" style="cursor: pointer;">
                                                            Line / Area
                                                        </th>
                                                        <th class="sortable" data-sort="total_lots" style="cursor: pointer;">
                                                            Total Lots
                                                        </th>
                                                        <th class="sortable" data-sort="status" style="cursor: pointer;">
                                                            Status
                                                        </th>
                                                        <th class="sortable" data-sort="assigned_by" style="cursor: pointer;">
                                                            Assigned By
                                                        </th>
                                                        <th class="sortable" data-sort="request_time" style="cursor: pointer;">
                                                            Request Time
                                                        </th>
                                                        <th class="sortable" data-sort="elapsed_time" style="cursor: pointer;">
                                                            Elapsed Time 
                                                        </th>
                                                        <th>Management Actions</th>
                                                    </tr>
                                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $lotRequests; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lotRequest): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="lot-request-row" data-status="<?php echo e($lotRequest->status); ?>" data-priority="<?php echo e($lotRequest->priority_score ?? 0); ?>">
                                            <!-- Request Number -->
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <button type="button" 
                                                            class="btn btn-link p-0 text-decoration-none fw-medium js-view-lot-request"
                                                            data-lot-request-id="<?php echo e($lotRequest->id); ?>"
                                                            title="View details">
                                                        <?php echo e($lotRequest->request_number); ?>

                                                    </button>
                                                    <?php if($lotRequest->is_urgent): ?>
                                                        <span class="badge bg-danger ms-2" style="font-size: 0.65rem;">URGENT</span>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            
                                            <!-- Requestor -->
                                            <!-- Requestor -->
                                            <td>
                                                <div class="fw-medium"><?php echo e($lotRequest->user->emp_name); ?></div>
                                            </td>
                                            
                                            <!-- Equipment Type (eqp_type only) -->
                                            <td>
                                                <div class="small text-muted"><?php echo e($lotRequest->equipment_types ?: 'N/A'); ?></div>
                                            </td>
                                            
                                            <!-- Line / Area (eqp_area) -->
                                            <td data-size="<?php echo e($lotRequest->lotRequestItems->first()?->equipment?->size ?? 'N/A'); ?>" data-line="<?php echo e($lotRequest->lotRequestItems->first()?->equipment?->eqp_line ?? 'N/A'); ?>">
                                                <div class="small text-muted"><?php echo e($lotRequest->area_stations ?: 'N/A'); ?></div>
                                            </td>
                                            
                                            <!-- Total Lots -->
                                            <td>
                                                <span class="fw-bold text-primary fs-5"><?php echo e($lotRequest->total_quantity); ?></span>
                                            </td>
                                            
                                            <!-- Status -->
                                            <td>
                                                <span class="badge <?php echo e($lotRequest->getStatusBadgeClass()); ?>">
                                                    <?php echo e($lotRequest->formatted_status); ?>

                                                </span>
                                                
                                                <?php if($lotRequest->status === 'assigned'): ?>
                                                    <?php
                                                        $assignedCount = $lotRequest->lotAssignments->count();
                                                        $deliveredCount = $lotRequest->lotAssignments->where('assignment_status', 'delivered')->count();
                                                    ?>
                                                    <div class="small text-muted mt-1">
                                                        <?php echo e($deliveredCount); ?>/<?php echo e($assignedCount); ?> delivered
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            
                                            <!-- Assigned By -->
                                            <td>
                                                <?php if($lotRequest->assigned_to_manager): ?>
                                                    <?php $manager = $lotRequest->assignedManager; ?>
                                                    <?php if($manager): ?>
                                                        <div class="fw-medium"><?php echo e($manager->emp_name); ?></div>
                                                    <?php else: ?>
                                                        <small class="text-muted"><?php echo e($lotRequest->assigned_to_manager); ?></small>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Unassigned</span>
                                                <?php endif; ?>
                                            </td>
                                            
                                            <!-- Request Time -->
                                            <td>
                                                <div><?php echo e($lotRequest->request_date->format('M d, H:i')); ?></div>
                                            </td>

                                            <!-- Elapsed Time -->
                                            <td>
                                                <span class="badge bg-secondary elapsed-timer"
                                                      data-request-date="<?php echo e($lotRequest->request_date->timestamp); ?>"
                                                      data-status="<?php echo e($lotRequest->status); ?>">
                                                    <i class="fas fa-stopwatch me-1"></i><?php echo e($lotRequest->elapsed_human); ?>

                                                </span>
                                            </td>
                                            
                                            <!-- Management Actions -->
                                            <!-- Management Actions -->
                                            <td>
                                                <?php $role = strtoupper(Auth::user()->role ?? 'USER'); ?>
                                                <div class="action-buttons-group d-flex flex-row align-items-center" style="gap: 0.75rem;">
                                                    <!-- View Details - open modal -->
                                                    <button type="button" 
                                                            class="btn btn-outline-primary btn-sm js-view-lot-request"
                                                            data-lot-request-id="<?php echo e($lotRequest->id); ?>" title="View">
                                                        <i class="fas fa-eye me-1"></i>View
                                                    </button>
                                                    
                                                    <?php if($role === 'ADMIN' || $role === 'MANAGER'): ?>
                                                        <?php if($lotRequest->canBeAccepted()): ?>
                                                            <!-- Separator -->
                                                            <div class="vr" style="height: 24px; opacity: 0.3;"></div>
                                                            <!-- Accept Request -->
                                                            <form action="<?php echo e(route('lot-requests.accept', $lotRequest)); ?>" method="POST" class="d-inline">
                                                                <?php echo csrf_field(); ?>
                                                                <?php echo method_field('PATCH'); ?>
                                                                <button type="submit" class="btn btn-warning btn-sm" title="Accept">
                                                                    <i class="fas fa-check me-1"></i>Accept
                                                                </button>
                                                            </form>
                                                        <?php elseif($lotRequest->status === 'accepted' && $lotRequest->canAssignLots()): ?>
                                                            <!-- Separator -->
                                                            <div class="vr" style="height: 24px; opacity: 0.3;"></div>
                                                            <!-- Assign Lots for accepted requests -->
                                                            <a href="<?php echo e(route('lot-requests.assign-lots', $lotRequest)); ?>" class="btn btn-info btn-sm" title="Assign Lots">
                                                                <i class="fas fa-tasks me-1"></i>Assign Lots
                                                            </a>
                                                        <?php elseif($lotRequest->status === 'completed'): ?>
                                                            <!-- Separator -->
                                                            <div class="vr" style="height: 24px; opacity: 0.3;"></div>
                                                            <!-- Completed (disabled) -->
                                                            <button type="button" class="btn btn-success btn-sm" title="Completed" disabled>
                                                                <i class="fas fa-check-double me-1"></i>Completed
                                                            </button>
                                                        <?php endif; ?>
                                                    <?php endif; ?>

                                                    <?php if($role === 'ADMIN'): ?>
                                                        <!-- Separator -->
                                                        <div class="vr" style="height: 24px; opacity: 0.3;"></div>
                                                        <!-- Delete - Admin only -->
                                                        <form action="<?php echo e(route('lot-requests.destroy', $lotRequest)); ?>" method="POST" class="d-inline" onsubmit="return confirm('Delete this lot request?');">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('DELETE'); ?>
                                                            <button type="submit" class="btn btn-danger btn-sm" title="Delete">
                                                                <i class="fas fa-trash me-1"></i>Delete
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            <?php echo e($lotRequests->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted mb-3">No Lot Requests Found</h5>
                            <p class="text-muted mb-4">You haven't created any lot requests yet.</p>
                            <a href="<?php echo e(route('lot-requests.create')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Create Your First Lot Request
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Lot Request Details Modal -->
    <div class="modal fade" id="lotRequestModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" tabindex="-1"><i class="fas fa-eye me-2"></i>Lot Request Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="lotRequestModalBody">
                    <div class="text-center py-5 text-muted">
                        <i class="fas fa-spinner fa-spin me-2"></i>Loading...
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            const statusFilter = document.getElementById('statusFilter');
            const sizeFilter = document.getElementById('sizeFilter');
            const lineFilter = document.getElementById('lineFilter');
            const clearFiltersBtn = document.getElementById('clearFilters');
            const tableRows = document.querySelectorAll('tbody tr');
            
            let currentFilters = {
                search: '',
                status: 'pending',
                size: 'all',
                line: 'all'
            };

            // Modal helpers with proper accessibility handling
            let lotRequestModal, bsModal;
            const modalEl = document.getElementById('lotRequestModal');
            if (modalEl) {
                lotRequestModal = modalEl;
                bsModal = new bootstrap.Modal(lotRequestModal);
                
                // Handle modal events for accessibility
                modalEl.addEventListener('show.bs.modal', function() {
                    // Remove aria-hidden when modal is about to be shown
                    modalEl.removeAttribute('aria-hidden');
                });
                
                modalEl.addEventListener('shown.bs.modal', function() {
                    // Focus on the modal title when fully shown for screen readers
                    const modalTitle = modalEl.querySelector('.modal-title');
                    if (modalTitle) {
                        modalTitle.focus();
                    }
                });
                
                modalEl.addEventListener('hide.bs.modal', function() {
                    // Ensure focus is returned to trigger element if possible
                    const activeElement = document.activeElement;
                    if (activeElement && modalEl.contains(activeElement)) {
                        activeElement.blur();
                    }
                });
                
                modalEl.addEventListener('hidden.bs.modal', function() {
                    // Re-add aria-hidden when modal is fully hidden
                    modalEl.setAttribute('aria-hidden', 'true');
                });
            }

            function openLotRequestModal(id) {
                if (!bsModal) return;
                const body = document.getElementById('lotRequestModalBody');
                body.innerHTML = '<div class="text-center py-5 text-muted"><i class="fas fa-spinner fa-spin me-2"></i>Loading...</div>';
                bsModal.show();
                fetch(`/lot-requests/${id}/details`, {
                    headers: { 'X-Requested-With': 'XMLHttpRequest' }
                })
                    .then(r => {
                        if (!r.ok) throw new Error('Failed to load');
                        return r.text();
                    })
                    .then(html => {
                        body.innerHTML = html;
                    })
                    .catch(() => {
                        body.innerHTML = '<div class="alert alert-danger">Failed to load details. Please try again.</div>';
                    });
            }

            // Delegate clicks for all view buttons/links
            document.body.addEventListener('click', function(e) {
                const btn = e.target.closest('.js-view-lot-request');
                if (btn) {
                    e.preventDefault();
                    const id = btn.getAttribute('data-lot-request-id');
                    if (id) openLotRequestModal(id);
                }
            });
            
            // Clear filters functionality
                clearFiltersBtn.addEventListener('click', function() {
                searchInput.value = '';
                statusFilter.value = 'pending'; // Reset to default pending instead of all
                sizeFilter.value = 'all';
                lineFilter.value = 'all';
                
                currentFilters = {
                    search: '',
                    status: 'pending', // Reset to default pending instead of all
                    size: 'all',
                    line: 'all'
                };
                
                applyFilters();
            });
            
            // Search functionality
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    currentFilters.search = this.value.toLowerCase().trim();
                    applyFilters();
                });
            }
            
            // Status filter functionality
            if (statusFilter) {
                statusFilter.addEventListener('change', function() {
                    currentFilters.status = this.value;
                    applyFilters();
                });
            }
            
            // Size filter functionality
            if (sizeFilter) {
                sizeFilter.addEventListener('change', function() {
                    currentFilters.size = this.value;
                    applyFilters();
                });
            }
            
            // Line filter functionality
            if (lineFilter) {
                lineFilter.addEventListener('change', function() {
                    currentFilters.line = this.value;
                    applyFilters();
                });
            }
            
            function applyFilters() {
                let visibleCount = 0;

                tableRows.forEach(row => {
                    const requestNumber = row.cells[0].textContent.toLowerCase();
                    const requestor = row.cells[1].textContent.toLowerCase();
                    const equipmentType = row.cells[2].textContent.toLowerCase();
                    const areaStation = row.cells[3].textContent.toLowerCase();
                    const statusCell = row.cells[5]; // Status column index
                    const status = statusCell.textContent.toLowerCase().trim();
                    
                    // Get size and line from data attributes
                    const size = row.cells[3].dataset.size || '';
                    const line = row.cells[3].dataset.line || '';

                    // Check search match
                    const searchMatch = currentFilters.search === '' || 
                        requestNumber.includes(currentFilters.search) || 
                        requestor.includes(currentFilters.search) ||
                        equipmentType.includes(currentFilters.search) ||
                        areaStation.includes(currentFilters.search);
                    
                    // Check status filter
                    let statusMatch = currentFilters.status === 'all';
                    if (!statusMatch) {
                        if (currentFilters.status === 'pending' && status.includes('pending')) {
                            statusMatch = true;
                        } else if (currentFilters.status === 'accepted' && status.includes('accepted')) {
                            statusMatch = true;
                        } else if (currentFilters.status === 'assigned' && status.includes('assigned')) {
                            statusMatch = true;
                        } else if (currentFilters.status === 'completed' && status.includes('completed')) {
                            statusMatch = true;
                        } else if (status === currentFilters.status || status.includes(currentFilters.status)) {
                            statusMatch = true;
                        }
                    }
                    
                    // Check size filter
                    const sizeMatch = currentFilters.size === 'all' || size === currentFilters.size;
                    
                    // Check line filter
                    const lineMatch = currentFilters.line === 'all' || line === currentFilters.line;

                    if (searchMatch && statusMatch && sizeMatch && lineMatch) {
                        row.style.display = '';
                        visibleCount++;
                    } else {
                        row.style.display = 'none';
                    }
                });

                // Show/hide no results message
                updateNoResultsMessage(visibleCount);
            }
            
            function updateNoResultsMessage(visibleCount) {
                let noResultsRow = document.getElementById('noResultsRow');
                
                if (visibleCount === 0) {
                    if (!noResultsRow) {
                        const tbody = document.querySelector('tbody');
                        noResultsRow = document.createElement('tr');
                        noResultsRow.id = 'noResultsRow';
                        noResultsRow.innerHTML = `
                            <td colspan="10" class="text-center py-4">
                                <i class="fas fa-search fa-2x text-muted mb-2"></i>
                                <div class="text-muted">No requests match your search criteria</div>
                            </td>
                        `;
                        tbody.appendChild(noResultsRow);
                    }
                    noResultsRow.style.display = '';
                } else if (noResultsRow) {
                    noResultsRow.style.display = 'none';
                }
            }
            
            // Real-time elapsed time updates for active requests
            function updateElapsedTimers() {
                const activeTimers = document.querySelectorAll('.elapsed-timer');
                const now = Math.floor(Date.now() / 1000);
                
                activeTimers.forEach(timer => {
                    const requestDate = parseInt(timer.dataset.requestDate);
                    const status = timer.dataset.status;
                    
                    // Only live-update when still pending or accepted
                    if (requestDate && (status === 'pending' || status === 'accepted')) {
                        const diffInSeconds = now - requestDate;
                        const diffInMinutes = Math.floor(diffInSeconds / 60);
                        const diffInHours = Math.floor(diffInMinutes / 60);
                        const diffInDays = Math.floor(diffInHours / 24);
                        
                        let timeText;
                        if (diffInMinutes < 60) {
                            timeText = diffInMinutes + 'm';
                        } else if (diffInHours < 24) {
                            timeText = diffInHours + 'h ' + (diffInMinutes % 60) + 'm';
                        } else if (diffInDays < 7) {
                            timeText = diffInDays + 'd ' + (diffInHours % 24) + 'h';
                        } else {
                            const weeks = Math.floor(diffInDays / 7);
                            const remainingDays = diffInDays % 7;
                            timeText = weeks + 'w' + (remainingDays > 0 ? ' ' + remainingDays + 'd' : '');
                        }
                        
                        timer.innerHTML = '<i class="fas fa-stopwatch me-1"></i>' + timeText;
                    }
                });
            }
            
            
            // Update elapsed timers every minute
            updateElapsedTimers();
            setInterval(updateElapsedTimers, 60000); // Update every 60 seconds
            
            // Table sorting functionality
            const sortableHeaders = document.querySelectorAll('.sortable');
            let currentSort = { column: 'elapsed_time', direction: 'desc' };
            
            // Initialize with default sorting by elapsed time (descending) for pending requests
            const elapsedTimeHeader = document.querySelector('[data-sort="elapsed_time"]');
            if (elapsedTimeHeader) {
                elapsedTimeHeader.classList.add('sort-desc');
                sortTable('elapsed_time', 'desc');
            }
            
            // Apply initial filters to show pending requests by default
            applyFilters();
            
            sortableHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    const sortBy = this.dataset.sort;
                    
                    // Toggle sort direction
                    if (currentSort.column === sortBy) {
                        currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
                    } else {
                        currentSort.direction = 'asc';
                    }
                    currentSort.column = sortBy;
                    
                    // Update header visual indicators
                    sortableHeaders.forEach(h => {
                        h.classList.remove('sort-asc', 'sort-desc');
                    });
                    this.classList.add(currentSort.direction === 'asc' ? 'sort-asc' : 'sort-desc');
                    
                    // Sort table rows
                    sortTable(sortBy, currentSort.direction);
                });
            });
            
            function sortTable(sortBy, direction) {
                const tbody = document.querySelector('tbody');
                const rows = Array.from(tbody.querySelectorAll('tr:not(#noResultsRow)'));
                
                rows.sort((a, b) => {
                    let aValue, bValue;
                    
                    switch(sortBy) {
                        case 'request_number':
                            aValue = a.cells[0].textContent.trim();
                            bValue = b.cells[0].textContent.trim();
                            break;
                        case 'requestor':
                            aValue = a.cells[1].textContent.trim();
                            bValue = b.cells[1].textContent.trim();
                            break;
                        case 'equipment_type':
                            aValue = a.cells[2].textContent.trim();
                            bValue = b.cells[2].textContent.trim();
                            break;
                        case 'line_area':
                            aValue = a.cells[3].textContent.trim();
                            bValue = b.cells[3].textContent.trim();
                            break;
                        case 'total_lots':
                            aValue = parseInt(a.cells[4].textContent.trim()) || 0;
                            bValue = parseInt(b.cells[4].textContent.trim()) || 0;
                            break;
                        case 'status':
                            aValue = a.cells[5].textContent.trim();
                            bValue = b.cells[5].textContent.trim();
                            break;
                        case 'assigned_by':
                            aValue = a.cells[6].textContent.trim();
                            bValue = b.cells[6].textContent.trim();
                            break;
                        case 'request_time':
                            // Parse date for proper sorting
                            aValue = new Date(a.cells[7].textContent.trim());
                            bValue = new Date(b.cells[7].textContent.trim());
                            break;
                        case 'elapsed_time':
                            // Extract elapsed time for sorting (parse time units)
                            const aTime = a.cells[8].textContent.trim();
                            const bTime = b.cells[8].textContent.trim();
                            aValue = parseElapsedTime(aTime);
                            bValue = parseElapsedTime(bTime);
                            break;
                        default:
                            aValue = a.cells[0].textContent.trim();
                            bValue = b.cells[0].textContent.trim();
                    }
                    
                    // Handle numeric sorting
                    if (typeof aValue === 'number' && typeof bValue === 'number') {
                        return direction === 'asc' ? aValue - bValue : bValue - aValue;
                    }
                    
                    // Handle date sorting
                    if (aValue instanceof Date && bValue instanceof Date) {
                        return direction === 'asc' ? aValue - bValue : bValue - aValue;
                    }
                    
                    // Handle string sorting
                    const comparison = aValue.localeCompare(bValue);
                    return direction === 'asc' ? comparison : -comparison;
                });
                
                // Reorder rows in DOM
                rows.forEach(row => tbody.appendChild(row));
            }
            
            function parseElapsedTime(timeStr) {
                // Parse elapsed time string like "2h 15m" or "1d 3h" into minutes
                let totalMinutes = 0;
                
                const dayMatch = timeStr.match(/(\d+)d/);
                const hourMatch = timeStr.match(/(\d+)h/);
                const minuteMatch = timeStr.match(/(\d+)m/);
                
                if (dayMatch) totalMinutes += parseInt(dayMatch[1]) * 24 * 60;
                if (hourMatch) totalMinutes += parseInt(hourMatch[1]) * 60;
                if (minuteMatch) totalMinutes += parseInt(minuteMatch[1]);
                
                return totalMinutes;
            }
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>

<?php /**PATH C:\inetpub\wwwroot\process-dashboard\resources\views/lot-requests/index.blade.php ENDPATH**/ ?>