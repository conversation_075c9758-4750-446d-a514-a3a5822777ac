<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class SettingsHelper
{
    /**
     * Get application setting value
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get($key, $default = null)
    {
        $settings = Cache::get('app_settings', []);
        
        // If not in cache, try to load from file
        if (empty($settings) && Storage::disk('local')->exists('settings.json')) {
            try {
                $settingsJson = Storage::disk('local')->get('settings.json');
                $settings = json_decode($settingsJson, true) ?? [];
                // Cache the loaded settings
                Cache::put('app_settings', $settings, now()->addDays(30));
            } catch (\Exception $e) {
                $settings = [];
            }
        }
        
        return $settings[$key] ?? $default;
    }
    
    /**
     * Get all application settings
     *
     * @return array
     */
    public static function all()
    {
        return [
            'app_name' => config('app.name', 'Process Dashboard'),
            'app_description' => self::get('app_description', 'Business Process Management Dashboard'),
            'contact_email' => self::get('contact_email', '<EMAIL>'),
            'timezone' => config('app.timezone', 'Asia/Tokyo'),
            'unit' => self::get('unit', 'PCS'),
        ];
    }
    
    /**
     * Get the application unit setting
     *
     * @return string
     */
    public static function getUnit()
    {
        return self::get('unit', 'PCS');
    }
    
    /**
     * Get the application name
     *
     * @return string
     */
    public static function getAppName()
    {
        return config('app.name', 'Process Dashboard');
    }
    
    /**
     * Get the contact email
     *
     * @return string
     */
    public static function getContactEmail()
    {
        return self::get('contact_email', '<EMAIL>');
    }
    
    /**
     * Get the application timezone
     *
     * @return string
     */
    public static function getTimezone()
    {
        return config('app.timezone', 'Asia/Tokyo');
    }
}