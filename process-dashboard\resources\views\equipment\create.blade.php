<x-app-layout>
    <x-slot name="header">
        Add New Equipment
    </x-slot>

    <div class="row justify-content-center">
        <div class="col-12 col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>Add New Equipment
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('equipment.store') }}" id="equipment-form">
                        @csrf
                        
                        <!-- Basic Information Section -->
                        <div class="mb-4">
                            <h6 class="text-primary mb-3"><i class="fas fa-info-circle me-2"></i>Basic Information</h6>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="eqp_no" class="form-label">Equipment Number <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('eqp_no') is-invalid @enderror" 
                                           name="eqp_no" id="eqp_no" value="{{ old('eqp_no') }}" required>
                                    @error('eqp_no')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="eqp_line" class="form-label">Equipment Line <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('eqp_line') is-invalid @enderror" 
                                           name="eqp_line" id="eqp_line" value="{{ old('eqp_line') }}" required>
                                    @error('eqp_line')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="eqp_area" class="form-label">Equipment Area</label>
                                    <input type="text" class="form-control @error('eqp_area') is-invalid @enderror" 
                                           name="eqp_area" id="eqp_area" value="{{ old('eqp_area') }}">
                                    @error('eqp_area')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="eqp_maker" class="form-label">Equipment Maker <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('eqp_maker') is-invalid @enderror" 
                                           name="eqp_maker" id="eqp_maker" value="{{ old('eqp_maker') }}" required>
                                    @error('eqp_maker')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <!-- Equipment Specifications Section -->
                        <div class="mb-4">
                            <h6 class="text-primary mb-3"><i class="fas fa-cogs me-2"></i>Equipment Specifications</h6>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="eqp_type" class="form-label">Equipment Type <span class="text-danger">*</span></label>
                                    <select class="form-select @error('eqp_type') is-invalid @enderror" 
                                            name="eqp_type" id="eqp_type" required onchange="loadReferenceData()">
                                        <option value="">Select Equipment Type</option>
                                        @if(isset($referenceData['eqp_types']))
                                            @foreach($referenceData['eqp_types'] as $type)
                                                <option value="{{ $type }}" {{ old('eqp_type') === $type ? 'selected' : '' }}>{{ $type }}</option>
                                            @endforeach
                                        @endif
                                    </select>
                                    @error('eqp_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="size" class="form-label">Size <span class="text-danger">*</span></label>
                                    <select class="form-select @error('size') is-invalid @enderror" 
                                            name="size" id="size" required onchange="loadReferenceData()">
                                        <option value="">Select Size</option>
                                        @if(isset($referenceData['sizes']))
                                            @foreach($referenceData['sizes'] as $size)
                                                <option value="{{ $size }}" {{ old('size') === $size ? 'selected' : '' }}>{{ $size }}</option>
                                            @endforeach
                                        @endif
                                    </select>
                                    @error('size')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="alloc_type" class="form-label">Allocation Type</label>
                                    <select class="form-select @error('alloc_type') is-invalid @enderror" 
                                            name="alloc_type" id="alloc_type" onchange="loadReferenceData()">
                                        <option value="">Select Allocation Type</option>
                                        @if(isset($referenceData['alloc_types']))
                                            @foreach($referenceData['alloc_types'] as $type)
                                                <option value="{{ $type }}" {{ old('alloc_type') === $type ? 'selected' : '' }}>{{ $type }}</option>
                                            @endforeach
                                        @endif
                                    </select>
                                    @error('alloc_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="cam_class" class="form-label">CAM Class</label>
                                    <input type="text" class="form-control @error('cam_class') is-invalid @enderror" 
                                           name="cam_class" id="cam_class" value="{{ old('cam_class') }}">
                                    @error('cam_class')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="insp_type" class="form-label">Inspection Type</label>
                                    <input type="text" class="form-control @error('insp_type') is-invalid @enderror" 
                                           name="insp_type" id="insp_type" value="{{ old('insp_type') }}">
                                    @error('insp_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="linear_type" class="form-label">Linear Type</label>
                                    <input type="text" class="form-control @error('linear_type') is-invalid @enderror" 
                                           name="linear_type" id="linear_type" value="{{ old('linear_type') }}">
                                    @error('linear_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="eqp_status" class="form-label">Equipment Status <span class="text-danger">*</span></label>
                                    <select class="form-select @error('eqp_status') is-invalid @enderror"
                                            name="eqp_status" id="eqp_status" required>
                                        <option value="">Select Status</option>
                                        <option value="OPERATIONAL" {{ old('eqp_status') === 'OPERATIONAL' ? 'selected' : '' }}>OPERATIONAL</option>
                                        <option value="PLANSTOP" {{ old('eqp_status') === 'PLANSTOP' ? 'selected' : '' }}>PLANSTOP</option>
                                        <option value="IDDLE" {{ old('eqp_status') === 'IDDLE' ? 'selected' : '' }}>IDDLE</option>
                                        <option value="BREAKDOWN" {{ old('eqp_status') === 'BREAKDOWN' ? 'selected' : '' }}>BREAKDOWN</option>
                                        <option value="NEW" {{ old('eqp_status') === 'NEW' ? 'selected' : '' }}>NEW</option>
                                        <option value="ADVANCE" {{ old('eqp_status') === 'ADVANCE' ? 'selected' : '' }}>ADVANCE</option>
                                    </select>
                                    @error('eqp_status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <!-- Performance Parameters Section -->
                        <div class="mb-4">
                            <h6 class="text-primary mb-3"><i class="fas fa-tachometer-alt me-2"></i>Performance Parameters</h6>
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label for="loading_speed" class="form-label">Loading Speed <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" step="1" min="0" class="form-control @error('loading_speed') is-invalid @enderror" 
                                               name="loading_speed" id="loading_speed" value="{{ old('loading_speed') }}" required onchange="calculateCapacities()">
                                        <span class="input-group-text">units/min</span>
                                    </div>
                                    <div class="form-text">Auto-populated from reference data</div>
                                    @error('loading_speed')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="operation_time" class="form-label">Operation Time <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" step="1" min="0" class="form-control @error('operation_time') is-invalid @enderror" 
                                               name="operation_time" id="operation_time" value="{{ old('operation_time', 1440) }}" required onchange="calculateCapacities()">
                                        <span class="input-group-text">min</span>
                                    </div>
                                    <div class="form-text">1440 min = 1 day</div>
                                    @error('operation_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-2">
                                    <label for="eqp_oee" class="form-label">Equipment OEE <span class="text-danger">*</span></label>
                                    <input type="number" step="0.0001" min="0" max="1" class="form-control @error('eqp_oee') is-invalid @enderror" 
                                           name="eqp_oee" id="eqp_oee" value="{{ old('eqp_oee') }}" required onchange="calculateCapacities()">
                                    <div class="form-text">0-1 (e.g., 0.85)</div>
                                    @error('eqp_oee')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-2">
                                    <label for="eqp_passing" class="form-label">Passing Rate <span class="text-danger                        
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>Create Equipment
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="loadReferenceData()">
                                        <i class="fas fa-sync me-1"></i>Load Reference Data
                                    </button>
                                    <a href="{{ route('equipment.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Reference data for auto-completion
        const referenceData = @json($referenceData ?? []);
        
        function loadReferenceData() {
            const eqpType = document.getElementById('eqp_type').value;
            const size = document.getElementById('size').value;
            const allocType = document.getElementById('alloc_type').value;
            
            if (!eqpType && !size && !allocType) {
                return;
            }
            
            // Show loading indicator
            const loadingSpinner = `<i class="fas fa-spinner fa-spin"></i>`;
            const submitBtn = document.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;
            submitBtn.innerHTML = loadingSpinner + ' Loading...';
            submitBtn.disabled = true;
            
            fetch('/equipment/reference-data?' + new URLSearchParams({
                eqp_type: eqpType,
                size: size,
                alloc_type: allocType
            }))
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    // Populate fields with reference data
                    if (data.data.loading_speed) {
                        document.getElementById('loading_speed').value = data.data.loading_speed;
                    }
                    if (data.data.eqp_oee) {
                        document.getElementById('eqp_oee').value = data.data.eqp_oee;
                    }
                    if (data.data.eqp_passing) {
                        document.getElementById('eqp_passing').value = data.data.eqp_passing;
                    }
                    if (data.data.eqp_yield) {
                        document.getElementById('eqp_yield').value = data.data.eqp_yield;
                    }
                    
                    // Calculate capacities after populating data
                    calculateCapacities();
                    
                    // Show success message
                    showAlert('Reference data loaded successfully!', 'success');
                } else {
                    showAlert('No reference data found for the selected combination.', 'warning');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('Error loading reference data. Please try again.', 'danger');
            })
            .finally(() => {
                // Restore button
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;
            });
        }
        
        function calculateCapacities() {
            const loadingSpeed = parseFloat(document.getElementById('loading_speed').value) || 0;
            const operationTime = parseFloat(document.getElementById('operation_time').value) || 0;
            const eqpOee = parseFloat(document.getElementById('eqp_oee').value) || 0;
            const eqpPassing = parseFloat(document.getElementById('eqp_passing').value) || 0;
            const eqpYield = parseFloat(document.getElementById('eqp_yield').value) || 0;
            
            if (loadingSpeed && operationTime) {
                // Calculate capacities using correct formulas
                const idealCapa = loadingSpeed * operationTime;
                const oeeCapa = Math.floor(loadingSpeed * eqpOee * operationTime);
                const outputCapa = Math.floor(loadingSpeed * eqpOee * eqpPassing * eqpYield * operationTime);
                
                // Update display fields
                document.getElementById('ideal_capa_display').value = numberFormat(idealCapa);
                document.getElementById('oee_capa_display').value = numberFormat(oeeCapa);
                document.getElementById('output_capa_display').value = numberFormat(outputCapa);
                
                // Update hidden fields
                document.getElementById('ideal_capa').value = idealCapa;
                document.getElementById('oee_capa').value = oeeCapa;
                document.getElementById('output_capa').value = outputCapa;
            } else {
                // Clear fields if inputs are not complete
                document.getElementById('ideal_capa_display').value = '';
                document.getElementById('oee_capa_display').value = '';
                document.getElementById('output_capa_display').value = '';
            }
        }
        
        function numberFormat(num) {
            return new Intl.NumberFormat().format(Math.round(num));
        }
        
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show mt-3`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const form = document.getElementById('equipment-form');
            form.insertBefore(alertDiv, form.firstChild);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
        
        // Auto-calculate on page load if values are present
        document.addEventListener('DOMContentLoaded', function() {
            calculateCapacities();
            
            // Load reference data if type and size are already selected
            const eqpType = document.getElementById('eqp_type').value;
            const size = document.getElementById('size').value;
            if (eqpType && size) {
                loadReferenceData();
            }
        });
    </script>
</x-app-layout>
