<?php

namespace App\Helpers;

class NumberHelper
{
    /**
     * Format number to millions format with PCS unit
     *
     * @param mixed $number
     * @return string
     */
    public static function formatToPCS($number)
    {
        if ($number === null || $number === '' || !is_numeric($number)) {
            return '0.0 M PCS';
        }

        $millions = floatval($number) / 1000000;
        return number_format($millions, 1) . ' M PCS';
    }

    /**
     * Format number to regular number with thousand separators
     *
     * @param mixed $number
     * @return string
     */
    public static function formatNumber($number)
    {
        if ($number === null || $number === '' || !is_numeric($number)) {
            return '0';
        }

        return number_format(intval($number));
    }

    /**
     * Format percentage to 1 decimal place
     *
     * @param mixed $percentage
     * @return string
     */
    public static function formatPercentage($percentage)
    {
        if ($percentage === null || $percentage === '' || !is_numeric($percentage)) {
            return '0.0%';
        }

        return number_format(floatval($percentage), 1) . '%';
    }
}