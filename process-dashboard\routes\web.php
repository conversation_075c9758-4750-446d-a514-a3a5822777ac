<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\EquipmentController;
use App\Http\Controllers\UpdateWipController;
use App\Http\Controllers\EndtimeController;
use App\Http\Controllers\LotRequestController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\UserManagementController;
use App\Http\Controllers\DataManagementController;
use App\Http\Controllers\SettingsController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    if (auth()->check()) {
        return redirect()->route('dashboard');
    }
    return redirect()->route('login');
});

// Home page for authenticated users (former welcome page)
Route::get('/home', function () {
    return view('welcome');
})->middleware(['auth', 'verified', 'active.user'])->name('home');

// Main Dashboard - All roles can access
Route::get('/dashboard', [DashboardController::class, 'index'])->middleware(['auth', 'verified', 'active.user', 'permission:dashboard'])->name('dashboard');

// Dashboard Export Route
Route::post('/dashboard/export-endtime', [DashboardController::class, 'exportEndtimeData'])->middleware(['auth', 'verified', 'active.user', 'permission:dashboard'])->name('dashboard.export.endtime');

// Analytics Dashboard Routes with role-based access
Route::get('/requests-rpt', [DashboardController::class, 'lotRequestDashboard'])->middleware(['auth', 'verified', 'active.user', 'permission:requests-rpt'])->name('requests.dashboard');
Route::get('/process-wip-rpt', [DashboardController::class, 'wipDashboard'])->middleware(['auth', 'verified', 'active.user', 'permission:process-wip-rpt'])->name('wip.dashboard');
Route::get('/mc-alloc-rpt', [DashboardController::class, 'machineAllocation'])->middleware(['auth', 'verified', 'active.user', 'permission:mc-alloc-rpt'])->name('machine.allocation.dashboard');
Route::get('/endline-rpt', [DashboardController::class, 'endlineReportDashboard'])->middleware(['auth', 'verified', 'active.user', 'permission:endline-rpt'])->name('endline.report.dashboard');
Route::get('/endline-wip', [DashboardController::class, 'endlineWipForm'])->middleware(['auth', 'verified', 'active.user', 'permission:endline-wip'])->name('endline.wip.form');

Route::middleware('auth')->group(function () {
    // API route for lot lookup
    Route::get('/api/lookup-lot/{lotId}', [EndtimeController::class, 'lookupLot'])->name('api.lookup.lot');
    
    // Debug route for equipment data
    Route::get('/debug-equipment/{eqpNo}', function ($eqpNo) {
        $equipment = \App\Models\Equipment::where('eqp_no', $eqpNo)->first();
        if (!$equipment) {
            return response()->json(['error' => 'Equipment not found'], 404);
        }
        
        // Use oee_capa directly from equipment
        $oeeCapa = floatval($equipment->oee_capa) ?: 0;
        $currentCalculated = $oeeCapa;
        
        return response()->json([
            'equipment_no' => $equipment->eqp_no,
            'raw_data' => [
                'eqp_oee' => $equipment->eqp_oee,
                'eqp_passing' => $equipment->eqp_passing,
                'eqp_yield' => $equipment->eqp_yield,
                'loading_speed' => $equipment->loading_speed,
                'operation_time' => $equipment->operation_time,
                'eqp_line' => $equipment->eqp_line,
                'eqp_area' => $equipment->eqp_area,
                'eqp_type' => $equipment->eqp_type,
                'eqp_maker' => $equipment->eqp_maker,
                'oee_capa' => $equipment->oee_capa,
            ],
            'parsed_values' => [
                'oee_capa' => $oeeCapa,
            ],
            'calculation' => [
                'formula' => 'Pre-calculated OEE Capacity',
                'calculation_steps' => 'Using database oee_capa field',
                'current_oee_capacity' => $currentCalculated,
                'formatted_capacity' => number_format($currentCalculated),
            ],
            'notes' => [
                'expected_capacity' => 9720000,
                'difference' => 9720000 - $currentCalculated,
                'issue' => $currentCalculated != 9720000 ? 'Calculated capacity does not match expected' : 'Capacity matches expected'
            ]
        ]);
    })->name('debug.equipment');
    
    // Manufacturing overview chart API
    Route::get('/api/manufacturing-overview', [DashboardController::class, 'getManufacturingOverviewData'])->name('api.manufacturing.overview');
    
    // Dashboard stats API for auto-refresh
    Route::get('/api/dashboard-stats', [DashboardController::class, 'updateDashboardStats'])->name('api.dashboard.stats');
    
    // Line area performance API
    Route::get('/api/line-area-performance', [DashboardController::class, 'getLineAreaPerformanceData'])->name('api.line.area.performance');
    
    // Line equipment stats API
    Route::get('/api/line-equipment-stats', [DashboardController::class, 'getLineEquipmentStats'])->name('api.line.equipment.stats');
    
    // CSRF token refresh for long-running sessions
    Route::get('/csrf-refresh', function() {
        return response()->json(['token' => csrf_token()]);
    });
    
    Route::middleware(['permission:profile'])->group(function () {
        Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
        Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
        Route::post('/profile/avatar', [ProfileController::class, 'updateAvatar'])->name('profile.avatar.update');
        Route::delete('/profile/avatar', [ProfileController::class, 'removeAvatar'])->name('profile.avatar.remove');
        Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    });
    
    // Equipment reference data API routes (must be BEFORE resource routes to avoid conflicts)
    Route::get('/equipment/reference-data', [EquipmentController::class, 'getEquipmentReferenceData'])->name('equipment.reference.data');
    Route::post('/equipment/calculate-capacities', [EquipmentController::class, 'calculateCapacities'])->name('equipment.calculate.capacities');
    
    // Equipment routes - Manager and Admin only
    Route::middleware(['permission:equipment'])->group(function () {
        Route::resource('equipment', EquipmentController::class);
    });
    
    // Equipment group details route
    Route::get('/equipment-group-details', [EquipmentController::class, 'getEquipmentGroupDetails'])->name('equipment.group.details');
    
    // UpdateWip routes
    Route::prefix('updatewip')->name('updatewip.')->group(function () {
        // WIP viewing routes (accessible to all authenticated users)
        Route::get('/', [UpdateWipController::class, 'index'])->middleware(['permission:updatewip'])->name('index');
        Route::get('/wip-group-details', [UpdateWipController::class, 'getWipGroupDetails'])->middleware(['permission:updatewip'])->name('wip.group.details');
        
        // Create/Update routes for authorized users
        Route::get('/create', [UpdateWipController::class, 'create'])->middleware(['permission:updatewip/create'])->name('create');
        Route::post('/', [UpdateWipController::class, 'store'])->middleware(['permission:updatewip/create:create'])->name('store');
        Route::get('/download-template', [UpdateWipController::class, 'downloadTemplate'])->middleware(['permission:updatewip/create'])->name('download.template');
    });
    
    // Endtime routes - Forecasted lots that can be finished by equipment
    Route::prefix('endtime')->name('endtime.')->group(function () {
        Route::get('/', [EndtimeController::class, 'index'])->middleware(['permission:endtime'])->name('index');
        Route::get('/create', [EndtimeController::class, 'create'])->middleware(['permission:endtime:create'])->name('create');
        Route::post('/', [EndtimeController::class, 'store'])->middleware(['permission:endtime:create'])->name('store');
        Route::get('/submit', [EndtimeController::class, 'showSubmit'])->middleware(['permission:endtime:update'])->name('submit.show');
        Route::post('/submit', [EndtimeController::class, 'submitExisting'])->middleware(['permission:endtime:update'])->name('submit');
        Route::get('/{id}/edit', [EndtimeController::class, 'edit'])->middleware(['permission:endtime:update'])->name('edit');
        Route::put('/{id}', [EndtimeController::class, 'update'])->middleware(['permission:endtime:update'])->name('update');
        Route::delete('/{id}', [EndtimeController::class, 'destroy'])->middleware(['permission:endtime:delete'])->name('destroy');
    });
    
    // API routes for endtime
    // API route for getting equipment without ongoing lots (for modal) - MUST be before {id} route
    Route::get('/api/endtime/equipment-without-ongoing', [EndtimeController::class, 'getEquipmentWithoutOngoing'])->middleware(['permission:endtime'])->name('api.endtime.equipment.without.ongoing');
    
    Route::get('/api/endtime/{id}', [EndtimeController::class, 'show'])->name('api.endtime.show');
    Route::get('/endtime/{id}/edit-data', [EndtimeController::class, 'getEditData'])->middleware(['permission:endtime:update'])->name('endtime.edit.data');
    Route::get('/endtime/equipment-data', [EndtimeController::class, 'getEquipmentData'])->middleware(['permission:endtime'])->name('endtime.equipment.data');
    
    // Lot lookup route for endtime modal
    Route::get('/endtime/lookup/{lotId}', [EndtimeController::class, 'lookupLot'])->middleware(['permission:endtime'])->name('endtime.lookup.lot');
    
    // API route for getting ongoing lots for submitted lot modal
    Route::get('/endtime/ongoing-lots', [EndtimeController::class, 'getOngoingLots'])->middleware(['permission:endtime'])->name('endtime.ongoing.lots');
    
    // Machine Allocation Dashboard API routes
    Route::get('/api/equipment/dashboard-data', [DashboardController::class, 'getDashboardData'])->middleware(['permission:mc-alloc-rpt'])->name('api.equipment.dashboard.data');
    Route::get('/api/equipment/export', [EquipmentController::class, 'exportEquipment'])->middleware(['permission:equipment'])->name('api.equipment.export');
    
    // Route for submitting lots (update from Ongoing to Submitted)
    Route::post('/endtime/submit-lot', [EndtimeController::class, 'submitLot'])->middleware(['permission:endtime:update'])->name('endtime.submit.lot');
    
    // API route for getting lot details (for modal view)
    Route::get('/api/endtime/lot/{id}', [EndtimeController::class, 'getLot'])->middleware(['permission:endtime'])->name('api.endtime.lot.details');

    // API route for checking if lot exists with Ongoing status
    Route::get('/api/endtime/check-ongoing/{lotId}', [EndtimeController::class, 'checkOngoingLot'])->middleware(['permission:endtime'])->name('api.endtime.check.ongoing');
    
    
    
    
    // Lot Request routes - accessible to all authenticated users
    Route::get('lot-requests', [LotRequestController::class, 'index'])->name('lot-requests.index');
    Route::get('lot-requests/create', [LotRequestController::class, 'create'])->name('lot-requests.create');
    Route::post('lot-requests', [LotRequestController::class, 'store'])->name('lot-requests.store');
    // Modal details endpoint (replaces page-based show)
    Route::get('lot-requests/{lotRequest}/details', [LotRequestController::class, 'details'])->name('lot-requests.details');
    
    // Update routes - accessible to all authenticated users (they can edit their own requests)
    Route::get('lot-requests/{lotRequest}/edit', [LotRequestController::class, 'edit'])->name('lot-requests.edit');
    Route::put('lot-requests/{lotRequest}', [LotRequestController::class, 'update'])->name('lot-requests.update');
    Route::patch('lot-requests/{lotRequest}', [LotRequestController::class, 'update']);
    
    // Delete route - restricted to admins and managers only
    Route::middleware(['permission:lot-requests:delete'])->group(function () {
        Route::delete('lot-requests/{lotRequest}', [LotRequestController::class, 'destroy'])->name('lot-requests.destroy');
    });
    
    // API routes for lot requests
    Route::get('/api/search-employees', [LotRequestController::class, 'searchEmployees'])->name('api.search.employees');
    Route::get('/api/equipment-code', [LotRequestController::class, 'getEquipmentCode'])->name('api.equipment.code');
    
    // Lot assignment route (legacy)
    Route::post('/lot-requests/{lotRequest}/assign-lot', [LotRequestController::class, 'assignLot'])->name('lot-requests.assign-lot');
    
    // New Management Workflow Routes
    Route::middleware(['permission:lot-requests:manage'])->group(function () {
        // Accept lot request
        Route::patch('lot-requests/{lotRequest}/accept', [LotRequestController::class, 'accept'])->name('lot-requests.accept');
        
        // Lot assignment workflow
        Route::get('lot-requests/{lotRequest}/assign-lots', [LotRequestController::class, 'assignLots'])->name('lot-requests.assign-lots');
        Route::post('lot-requests/{lotRequest}/process-assignment', [LotRequestController::class, 'processAssignment'])->name('lot-requests.process-assignment');
        
        // Get available lots (AJAX)
        Route::get('lot-requests/{lotRequest}/available-lots', [LotRequestController::class, 'getAvailableLots'])->name('lot-requests.available-lots');
        
        // Refresh available lots cache
        Route::post('/lot-requests/refresh-cache', [LotRequestController::class, 'refreshAvailableLotsCache'])->name('lot-requests.refresh-cache');
        
        // Mark as delivered
        Route::patch('lot-requests/{lotRequest}/deliver', [LotRequestController::class, 'deliver'])->name('lot-requests.deliver');
        
        // Set priority
        Route::patch('lot-requests/{lotRequest}/priority', [LotRequestController::class, 'setPriority'])->name('lot-requests.set-priority');
        
        // Auto-assign best lots
        Route::post('lot-requests/{lotRequest}/auto-assign', [LotRequestController::class, 'autoAssign'])->name('lot-requests.auto-assign');
        
        // Update lots cache
        Route::post('/lots-cache/update', [LotRequestController::class, 'updateLotsCache'])->name('lots-cache.update');
    });
    
    // Management routes (Admin only)
    Route::prefix('management')->name('management.')->group(function () {
        // User Management
        Route::prefix('users')->name('users.')->middleware('permission:management/users')->group(function () {
            Route::get('/', [UserManagementController::class, 'index'])->name('index');
            Route::get('/create', [UserManagementController::class, 'create'])->name('create');
            Route::post('/', [UserManagementController::class, 'store'])->name('store');
            Route::get('/{user}', [UserManagementController::class, 'show'])->name('show');
            Route::get('/{user}/edit', [UserManagementController::class, 'edit'])->name('edit');
            Route::put('/{user}', [UserManagementController::class, 'update'])->name('update');
            Route::delete('/{user}', [UserManagementController::class, 'destroy'])->name('destroy');
            Route::patch('/{user}/toggle-status', [UserManagementController::class, 'toggleStatus'])->name('toggle-status');
            Route::patch('/{user}/reset-password', [UserManagementController::class, 'resetPassword'])->name('reset-password');
        });
        
        // Data Management
        Route::prefix('data')->name('data.')->middleware('permission:management/data')->group(function () {
            Route::get('/', [DataManagementController::class, 'index'])->name('index');
            Route::get('/export/users', [DataManagementController::class, 'exportUsers'])->name('export.users');
            Route::post('/backup/create', [DataManagementController::class, 'createBackup'])->name('backup.create');
            Route::get('/backups', [DataManagementController::class, 'listBackups'])->name('backups');
            Route::get('/backup/download/{filename}', [DataManagementController::class, 'downloadBackup'])->name('backup.download');
            Route::delete('/backup/{filename}', [DataManagementController::class, 'deleteBackup'])->name('backup.delete');
        });
        
        
        // Settings
        Route::prefix('settings')->name('settings.')->middleware('permission:management/settings')->group(function () {
            Route::get('/', [SettingsController::class, 'index'])->name('index');
            Route::get('/general', [SettingsController::class, 'general'])->name('general');
            Route::match(['POST', 'PUT'], '/general', [SettingsController::class, 'updateGeneral'])->name('update.general');
            Route::get('/email', [SettingsController::class, 'email'])->name('email');
            Route::match(['POST', 'PUT'], '/email', [SettingsController::class, 'updateEmail'])->name('update.email');
            Route::get('/system', [SettingsController::class, 'system'])->name('system');
            Route::match(['POST', 'PUT'], '/system', [SettingsController::class, 'updateSystem'])->name('update.system');
            Route::get('/security', [SettingsController::class, 'security'])->name('security');
            Route::match(['POST', 'PUT'], '/security', [SettingsController::class, 'updateSecurity'])->name('update.security');
            Route::post('/clear-cache', [SettingsController::class, 'clearCache'])->name('clear-cache');
            Route::post('/optimize', [SettingsController::class, 'optimize'])->name('optimize');
            Route::get('/download-logs', [SettingsController::class, 'downloadLogs'])->name('download-logs');
            Route::post('/clear-logs', [SettingsController::class, 'clearLogs'])->name('clear-logs');
            
            // Security-specific routes
            Route::post('/security/logout-all-users', [SettingsController::class, 'logoutAllUsers'])->name('security.logout-all-users');
            Route::get('/security/active-sessions', [SettingsController::class, 'showActiveSessions'])->name('security.active-sessions');
            Route::get('/security/security-log', [SettingsController::class, 'showSecurityLog'])->name('security.security-log');
        });
    });
});

require __DIR__.'/auth.php';
