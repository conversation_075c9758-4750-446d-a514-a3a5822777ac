# Endtime Table - Clean Migration Setup

## Overview
This is a clean, consolidated migration setup for the endtime table functionality. All previous fragmented migrations have been merged into a single, comprehensive migration.

## Current Migration Structure

### `2025_09_06_125047_create_endtime_table_consolidated.php`
This single migration creates the complete endtime table with all required fields:

#### Core Lot Information
- `lot_id` - Lot identifier
- `model_15` - Model designation (15 chars max)
- `lot_size` - Size classification
- `lot_qty` - Quantity of pieces
- `work_type` - Work type (NOR, OI, ADV, etc.)
- `lot_type` - Lot classification (MAIN, RL/LY)
- `lipas_yn` - LIPAS flag (Y/N)

#### Equipment Management (Up to 10 Equipment Units)
- `eqp_1` through `eqp_10` - Equipment assignments
- `start_time_1` through `start_time_10` - Equipment start times
- `ng_percent_1` through `ng_percent_10` - NG percentages per equipment

#### Aggregate Equipment Info
- `eqp_line` - Equipment line
- `eqp_area` - Equipment area

#### Status & Timing
- `status` - Current status (default: 'Ongoing')
- `est_endtime` - Calculated estimated end time
- `actual_submitted_at` - User-specified actual submission time

#### Submission Results
- `remarks` - Auto-calculated result (Early, OK, Delayed)
- `submission_notes` - Additional notes (required for delayed lots)

#### Audit Fields
- `modified_by` - Who last modified the record
- `created_at` / `updated_at` - Laravel timestamps

#### Indexes
Optimized indexes for:
- `lot_id`, `est_endtime`, `status`
- `eqp_line`, `eqp_area`, `remarks`
- `actual_submitted_at`
- Composite index on `[lot_size, work_type]`

## Seeders

### EndtimeSeeder.php
- Production seeder that reads from `endtime.csv`
- Automatically calculates `remarks` based on submission timing
- Backwards compatible with legacy CSV formats

### SampleEndtimeSeeder.php
- Development seeder for generating realistic test data
- Creates various lot statuses and submission results
- Uses EndtimeFactory for consistent data generation

### EndtimeFactory.php
- Comprehensive factory with state methods:
  - `ongoing()` - Lots awaiting submission
  - `early()` - Early submitted lots
  - `onTime()` - On-time submitted lots  
  - `delayed()` - Delayed lots with notes

## Usage

### Production Setup
```bash
php artisan migrate --env=production
php artisan db:seed --env=production
```

### Development Setup
```bash
php artisan migrate:fresh --seed
# Optional: Add sample data
php artisan db:seed --class=SampleEndtimeSeeder
```

## Removed Components
- ❌ `submitted` table (redundant - functionality moved to endtime status)
- ❌ `Submitted` model
- ❌ `SubmittedSeeder` 
- ❌ `SubmittedController` reference
- ❌ All incremental endtime migrations (consolidated)

## Benefits of Clean Migration
1. **Single Source of Truth**: One migration file defines the complete table
2. **No Migration Dependencies**: No fragmented migration chain to manage
3. **Clear Documentation**: Complete field definitions with comments
4. **Optimized Indexes**: Performance-focused indexing strategy
5. **Clean History**: No legacy migration artifacts

This setup provides a clean, maintainable foundation for the endtime/submitted lot functionality.
