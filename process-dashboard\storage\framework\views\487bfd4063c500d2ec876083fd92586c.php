<style>
    .detail-card { background: white; border: 1px solid #e1e8ff; box-shadow: 0 4px 12px rgba(0,0,0,.05); border-radius: 12px; overflow: hidden; }
    .detail-card .card-header { background: linear-gradient(45deg,#667eea,#764ba2); color: #fff; border: none; padding: 0.75rem 1rem; }
    .status-badge { font-size: .95rem; padding: 0.35rem .75rem; border-radius: 20px; }
    .table-responsive { border-radius: 8px; overflow: hidden; }
</style>

<div class="container-fluid">
    <div class="row g-3">
        <div class="col-lg-8">
            <div class="detail-card mb-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-microchip me-2"></i>Equipment Items</h6>
                </div>
                <div class="card-body">
                    <?php if($lotRequest->lotRequestItems->count()): ?>
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead class="table-light">
                                    <tr>
                                        <th>Equipment Number</th>
                                        <th>Equipment Code</th>
                                        <th>Quantity (lots)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <?php $__currentLoopData = $lotRequest->lotRequestItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $displayCode = $item->equipment_code;
                                        if (empty($displayCode)) {
                                            $parts = [];
                                            if (!empty($item->cam_class)) $parts[] = $item->cam_class;
                                            if (!empty($item->insp_type)) $parts[] = $item->insp_type;
                                            if (!empty($item->alloc_type)) $parts[] = $item->alloc_type;
                                            $displayCode = count($parts) ? implode('-', $parts) : 'N/A';
                                        }
                                    ?>
                                    <tr>
                                        <td>
                                            <div class="fw-medium"><?php echo e($item->equipment_number); ?></div>
                                            <?php if($item->equipment): ?>
                                                <small class="text-muted">Line: <?php echo e($item->equipment->eqp_line ?? 'N/A'); ?> | Area: <?php echo e($item->equipment->eqp_area ?? 'N/A'); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td><span class="badge bg-info"><?php echo e($displayCode); ?></span></td>
                                        <td>
                                            <?php $quantityDisplay = $lotRequest->getQuantityDisplayForItem($item); ?>
                                            <?php if($quantityDisplay === 'shared'): ?>
                                                <span class="badge bg-secondary">shared</span>
                                                <small class="text-muted d-block"><?php echo e($item->quantity); ?> lots</small>
                                            <?php else: ?>
                                                <span class="fw-bold text-primary"><?php echo e($item->quantity); ?></span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <td colspan="2" class="fw-bold">Sub-total</td>
                                        <td class="fw-bold text-primary"><?php echo e($lotRequest->total_quantity); ?> lots</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center text-muted py-3">No equipment items found.</div>
                    <?php endif; ?>
                </div>
            </div>

            <?php if($lotRequest->lotAssignments->count() > 0): ?>
                <div class="detail-card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-check-circle me-2"></i>Assigned Lots</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead class="table-light">
                                <tr>
                                    <th>Lot Number</th>
                                    <th>Model</th>
                                    <th>Equipment</th>
                                    <th>Quantity</th>
                                    <th>WIP Status</th>
                                    <th>Assigned Date</th>
                                    <th>Assigned By</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php $__currentLoopData = $lotRequest->lotAssignments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $assignment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php $wipDetail = $wipDetails[$assignment->lot_id] ?? null; ?>
                                    <tr>
                                        <td>
                                            <div class="fw-medium" style="font-family: 'Courier New', monospace;"><?php echo e($assignment->lot_id); ?></div>
                                            <?php if($wipDetail && $wipDetail->lot_location): ?>
                                                <small class="text-muted d-block"><i class="fas fa-map-marker-alt me-1"></i><?php echo e($wipDetail->lot_location); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($wipDetail && $wipDetail->model_15): ?>
                                                <span class="badge bg-info"><?php echo e($wipDetail->model_15); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="fw-medium"><?php echo e($assignment->equipment_number); ?></div>
                                            <small class="text-muted"><?php echo e($assignment->equipment_code); ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-success"><?php echo e(number_format($assignment->lot_quantity)); ?></span>
                                            <?php if($wipDetail && $wipDetail->stagnant_tat): ?>
                                                <small class="text-muted d-block"><i class="fas fa-clock me-1"></i><?php echo e(number_format($wipDetail->stagnant_tat, 1)); ?>d TAT</small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($wipDetail && $wipDetail->wip_status): ?>
                                                <span class="badge <?php echo e($wipDetail->wip_status === 'Newlot Standby' ? 'bg-primary' : 'bg-info'); ?>"><?php echo e($wipDetail->wip_status); ?></span>
                                                <?php if($wipDetail->auto_yn === 'Y' || $wipDetail->lipas_yn === 'Y'): ?>
                                                    <div class="mt-1">
                                                        <?php if($wipDetail->auto_yn === 'Y'): ?><span class="badge bg-success" style="font-size: .65rem;">Auto</span><?php endif; ?>
                                                        <?php if($wipDetail->lipas_yn === 'Y'): ?><span class="badge bg-warning text-dark" style="font-size: .65rem;">Lipas</span><?php endif; ?>
                                                    </div>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="fw-medium"><?php echo e($assignment->assigned_date->format('M d, Y')); ?></div>
                                            <small class="text-muted"><?php echo e($assignment->assigned_date->format('H:i')); ?></small>
                                        </td>
                                        <td>
                                            <div class="fw-medium"><?php echo e($assignment->assignedBy->emp_name ?? 'Unknown'); ?></div>
                                            <small class="text-muted"><?php echo e($assignment->assignedBy->emp_no ?? 'N/A'); ?></small>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                                <tfoot class="table-light">
                                <tr>
                                    <td colspan="3" class="fw-bold">Total Assigned Lots:</td>
                                    <td class="fw-bold text-success"><?php echo e($lotRequest->lotAssignments->sum('lot_quantity') ? number_format($lotRequest->lotAssignments->sum('lot_quantity')) : $lotRequest->lotAssignments->count()); ?></td>
                                    <td colspan="3" class="fw-bold text-muted"><?php echo e($lotRequest->lotAssignments->count()); ?> assignment(s)</td>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if($lotRequest->notes): ?>
                <div class="detail-card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-sticky-note me-2"></i>Notes</h6>
                    </div>
                    <div class="card-body"><p class="mb-0"><?php echo e($lotRequest->notes); ?></p></div>
                </div>
            <?php endif; ?>
        </div>
        <div class="col-lg-4">
            <div class="detail-card mb-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Lot Request Summary</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2"><span>Request Number:</span><span class="fw-medium text-primary"><?php echo e($lotRequest->request_number); ?></span></div>
                    <div class="d-flex justify-content-between mb-2"><span>Total Equipment Items:</span><span class="badge bg-secondary"><?php echo e($lotRequest->lotRequestItems->count()); ?></span></div>
                    <div class="d-flex justify-content-between mb-2"><span>Total Lots:</span><span class="fw-bold text-primary fs-5"><?php echo e($lotRequest->total_quantity); ?></span></div>
                    <div class="d-flex justify-content-between mb-2"><span>Status:</span><span class="badge <?php echo e($lotRequest->getStatusBadgeClass()); ?> status-badge"><?php echo e($lotRequest->formatted_status); ?></span></div>
                    <div class="d-flex justify-content-between"><span>Request Date:</span><span class="fw-medium"><?php echo e($lotRequest->request_date->format('M d, Y')); ?></span></div>
                </div>
            </div>
            <div class="detail-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-user me-2"></i>Requestor Information</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2"><span>Name:</span><span class="fw-medium"><?php echo e($lotRequest->user->emp_name); ?></span></div>
                    <div class="d-flex justify-content-between mb-2"><span>Employee ID:</span><span class="fw-medium"><?php echo e($lotRequest->user->emp_no); ?></span></div>
                    <div class="d-flex justify-content-between"><span>Area Station:</span><span class="text-break"><?php echo e($lotRequest->area_stations); ?></span></div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\inetpub\wwwroot\process-dashboard\resources\views/lot-requests/partials/details.blade.php ENDPATH**/ ?>