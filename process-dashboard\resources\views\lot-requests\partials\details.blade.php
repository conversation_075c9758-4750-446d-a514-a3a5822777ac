<style>
    .detail-card { background: white; border: 1px solid #e1e8ff; box-shadow: 0 4px 12px rgba(0,0,0,.05); border-radius: 12px; overflow: hidden; }
    .detail-card .card-header { background: linear-gradient(45deg,#667eea,#764ba2); color: #fff; border: none; padding: 0.75rem 1rem; }
    .status-badge { font-size: .95rem; padding: 0.35rem .75rem; border-radius: 20px; }
    .table-responsive { border-radius: 8px; overflow: hidden; }
</style>

<div class="container-fluid">
    <div class="row g-3">
        <div class="col-lg-8">
            <div class="detail-card mb-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-microchip me-2"></i>Equipment Items</h6>
                </div>
                <div class="card-body">
                    @if($lotRequest->lotRequestItems->count())
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead class="table-light">
                                    <tr>
                                        <th>Equipment Number</th>
                                        <th>Equipment Code</th>
                                        <th>Quantity (lots)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                @foreach($lotRequest->lotRequestItems as $item)
                                    @php
                                        $displayCode = $item->equipment_code;
                                        if (empty($displayCode)) {
                                            $parts = [];
                                            if (!empty($item->cam_class)) $parts[] = $item->cam_class;
                                            if (!empty($item->insp_type)) $parts[] = $item->insp_type;
                                            if (!empty($item->alloc_type)) $parts[] = $item->alloc_type;
                                            $displayCode = count($parts) ? implode('-', $parts) : 'N/A';
                                        }
                                    @endphp
                                    <tr>
                                        <td>
                                            <div class="fw-medium">{{ $item->equipment_number }}</div>
                                            @if($item->equipment)
                                                <small class="text-muted">Line: {{ $item->equipment->eqp_line ?? 'N/A' }} | Area: {{ $item->equipment->eqp_area ?? 'N/A' }}</small>
                                            @endif
                                        </td>
                                        <td><span class="badge bg-info">{{ $displayCode }}</span></td>
                                        <td>
                                            @php $quantityDisplay = $lotRequest->getQuantityDisplayForItem($item); @endphp
                                            @if($quantityDisplay === 'shared')
                                                <span class="badge bg-secondary">shared</span>
                                                <small class="text-muted d-block">{{ $item->quantity }} lots</small>
                                            @else
                                                <span class="fw-bold text-primary">{{ $item->quantity }}</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <td colspan="2" class="fw-bold">Sub-total</td>
                                        <td class="fw-bold text-primary">{{ $lotRequest->total_quantity }} lots</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    @else
                        <div class="text-center text-muted py-3">No equipment items found.</div>
                    @endif
                </div>
            </div>

            @if($lotRequest->lotAssignments->count() > 0)
                <div class="detail-card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-check-circle me-2"></i>Assigned Lots</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead class="table-light">
                                <tr>
                                    <th>Lot Number</th>
                                    <th>Model</th>
                                    <th>Equipment</th>
                                    <th>Quantity</th>
                                    <th>WIP Status</th>
                                    <th>Assigned Date</th>
                                    <th>Assigned By</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($lotRequest->lotAssignments as $assignment)
                                    @php $wipDetail = $wipDetails[$assignment->lot_id] ?? null; @endphp
                                    <tr>
                                        <td>
                                            <div class="fw-medium" style="font-family: 'Courier New', monospace;">{{ $assignment->lot_id }}</div>
                                            @if($wipDetail && $wipDetail->lot_location)
                                                <small class="text-muted d-block"><i class="fas fa-map-marker-alt me-1"></i>{{ $wipDetail->lot_location }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            @if($wipDetail && $wipDetail->model_15)
                                                <span class="badge bg-info">{{ $wipDetail->model_15 }}</span>
                                            @else
                                                <span class="text-muted">N/A</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="fw-medium">{{ $assignment->equipment_number }}</div>
                                            <small class="text-muted">{{ $assignment->equipment_code }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">{{ number_format($assignment->lot_quantity) }}</span>
                                            @if($wipDetail && $wipDetail->stagnant_tat)
                                                <small class="text-muted d-block"><i class="fas fa-clock me-1"></i>{{ number_format($wipDetail->stagnant_tat, 1) }}d TAT</small>
                                            @endif
                                        </td>
                                        <td>
                                            @if($wipDetail && $wipDetail->wip_status)
                                                <span class="badge {{ $wipDetail->wip_status === 'Newlot Standby' ? 'bg-primary' : 'bg-info' }}">{{ $wipDetail->wip_status }}</span>
                                                @if($wipDetail->auto_yn === 'Y' || $wipDetail->lipas_yn === 'Y')
                                                    <div class="mt-1">
                                                        @if($wipDetail->auto_yn === 'Y')<span class="badge bg-success" style="font-size: .65rem;">Auto</span>@endif
                                                        @if($wipDetail->lipas_yn === 'Y')<span class="badge bg-warning text-dark" style="font-size: .65rem;">Lipas</span>@endif
                                                    </div>
                                                @endif
                                            @else
                                                <span class="text-muted">N/A</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="fw-medium">{{ $assignment->assigned_date->format('M d, Y') }}</div>
                                            <small class="text-muted">{{ $assignment->assigned_date->format('H:i') }}</small>
                                        </td>
                                        <td>
                                            <div class="fw-medium">{{ $assignment->assignedBy->emp_name ?? 'Unknown' }}</div>
                                            <small class="text-muted">{{ $assignment->assignedBy->emp_no ?? 'N/A' }}</small>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                                <tfoot class="table-light">
                                <tr>
                                    <td colspan="3" class="fw-bold">Total Assigned Lots:</td>
                                    <td class="fw-bold text-success">{{ $lotRequest->lotAssignments->sum('lot_quantity') ? number_format($lotRequest->lotAssignments->sum('lot_quantity')) : $lotRequest->lotAssignments->count() }}</td>
                                    <td colspan="3" class="fw-bold text-muted">{{ $lotRequest->lotAssignments->count() }} assignment(s)</td>
                                </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            @endif

            @if($lotRequest->notes)
                <div class="detail-card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-sticky-note me-2"></i>Notes</h6>
                    </div>
                    <div class="card-body"><p class="mb-0">{{ $lotRequest->notes }}</p></div>
                </div>
            @endif
        </div>
        <div class="col-lg-4">
            <div class="detail-card mb-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Lot Request Summary</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2"><span>Request Number:</span><span class="fw-medium text-primary">{{ $lotRequest->request_number }}</span></div>
                    <div class="d-flex justify-content-between mb-2"><span>Total Equipment Items:</span><span class="badge bg-secondary">{{ $lotRequest->lotRequestItems->count() }}</span></div>
                    <div class="d-flex justify-content-between mb-2"><span>Total Lots:</span><span class="fw-bold text-primary fs-5">{{ $lotRequest->total_quantity }}</span></div>
                    <div class="d-flex justify-content-between mb-2"><span>Status:</span><span class="badge {{ $lotRequest->getStatusBadgeClass() }} status-badge">{{ $lotRequest->formatted_status }}</span></div>
                    <div class="d-flex justify-content-between"><span>Request Date:</span><span class="fw-medium">{{ $lotRequest->request_date->format('M d, Y') }}</span></div>
                </div>
            </div>
            <div class="detail-card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-user me-2"></i>Requestor Information</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2"><span>Name:</span><span class="fw-medium">{{ $lotRequest->user->emp_name }}</span></div>
                    <div class="d-flex justify-content-between mb-2"><span>Employee ID:</span><span class="fw-medium">{{ $lotRequest->user->emp_no }}</span></div>
                    <div class="d-flex justify-content-between"><span>Area Station:</span><span class="text-break">{{ $lotRequest->area_stations }}</span></div>
                </div>
            </div>
        </div>
    </div>
</div>
