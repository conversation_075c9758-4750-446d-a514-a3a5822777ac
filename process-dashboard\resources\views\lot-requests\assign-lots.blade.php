<x-app-layout>
    <x-slot name="header">
        Assign Lots to Request
    </x-slot>

    <style>
        /* Professional lot assignment interface styling */
        .lot-assignment-page {
            background: #f8f9ff;
            min-height: 100vh;
        }
        
        .assignment-card {
            background: white;
            border: 1px solid #e1e8ff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            transition: all 0.2s ease;
        }
        
        .assignment-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }
        
        .assignment-card .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2) !important;
            color: white !important;
            border: none !important;
            padding: 1rem 1.5rem;
            border-radius: 12px 12px 0 0;
        }

        /* Make header switches larger and clearer */
        .assignment-card .card-header .form-check-input {
            width: 2.8rem;
            height: 1.4rem;
            cursor: pointer;
        }
        .assignment-card .card-header .form-check-input:focus {
            box-shadow: 0 0 0 0.2rem rgba(102,126,234,0.4);
        }
        .assignment-card .card-header .form-check-label {
            font-size: 0.95rem;
            font-weight: 700;
            margin-left: 0.25rem;
        }
        
        /* Available lots styling */
        .available-lot {
            border: 2px solid #e1e8ff;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            background: white;
            transition: all 0.2s ease;
            cursor: pointer;
            position: relative;
        }
        
        .available-lot:hover {
            border-color: #667eea;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }
        
        .available-lot.selected {
            border-color: #28a745;
            background: #f8fff9;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
        }
        /* Larger header text for lot code and model */
        .lot-header .lot-code { font-size: 1.15rem; font-weight: 800; }
        .lot-header .lot-model { font-size: 1.05rem; font-weight: 700; color: #3b5bdb; }
        
        .available-lot.priority-lipas {
            border: 3px solid #6f42c1;
            background: linear-gradient(135deg, #f8f5ff 0%, #ede3ff 100%);
            box-shadow: 0 0 10px rgba(111, 66, 193, 0.2);
        }
        
        .available-lot.priority-critical {
            border: 3px solid #dc3545;
            background: linear-gradient(135deg, #fff5f5 0%, #ffebee 100%);
            box-shadow: 0 0 10px rgba(220, 53, 69, 0.2);
        }
        
        .available-lot.priority-high {
            border: 3px solid #ffc107;
            background: linear-gradient(135deg, #fffbf0 0%, #fff8e1 100%);
            box-shadow: 0 0 10px rgba(255, 193, 7, 0.2);
        }
        
        .available-lot.priority-medium {
            border: 2px solid #17a2b8;
            background: linear-gradient(135deg, #f0feff 0%, #e6faff 100%);
        }
        
        .lot-classification {
            display: inline-block;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            margin: 0.125rem;
        }
        
        .lot-classification.match {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .lot-classification.mismatch {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        /* Less-priority/alternative match */
        .lot-classification.alt {
            background: #fff3cd;
            border-color: #ffeeba;
            color: #856404;
        }
        
        /* Assignment summary */
        .assignment-summary {
            position: sticky;
            top: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
        }
        
        .btn-assign-selected {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-assign-selected:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
            color: white;
        }
        
        .btn-assign-selected:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        /* Enhanced Refresh button styling */
        .btn-refresh-lots {
            background: #ffffff;
            color: #667eea;
            border: 2px solid #ffffff;
            padding: 0.5rem 1rem;
            font-size: 0.95rem;
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        
        .btn-refresh-lots:hover {
            background: #f0f4ff;
            color: #5a67d8;
            border-color: #f0f4ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .btn-refresh-lots:active {
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        }
        
        .btn-refresh-lots:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.4);
        }
        
        .btn-refresh-lots:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }
        
        .btn-refresh-lots i {
            font-size: 1rem;
        }
        
        /* Pulse animation to draw attention */
        @keyframes pulse {
            0% {
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
            50% {
                box-shadow: 0 2px 15px rgba(102, 126, 234, 0.4);
            }
            100% {
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            }
        }
        
        .btn-refresh-lots {
            animation: pulse 2s ease-in-out infinite;
        }
        
        .btn-refresh-lots:hover {
            animation: none;
        }
        
        /* Alternative button style with gradient */
        .btn-refresh-lots.gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            border: none;
        }
        
        .btn-refresh-lots.gradient:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b4193 100%);
            color: #ffffff;
            border: none;
        }

        /* Dark mode adaptations - only when app explicitly sets Bootstrap 5 theme */
        [data-bs-theme="dark"] .assignment-card { background: #111827; border-color: #374151; }
        [data-bs-theme="dark"] .assignment-card .card-header { background: linear-gradient(45deg, #4f46e5, #7c3aed) !important; }
        [data-bs-theme="dark"] .available-lot { background: #1f2937; border-color: #374151; color: #e5e7eb; }
        [data-bs-theme="dark"] .available-lot.selected { background: #0f172a; border-color: #22c55e; }
        [data-bs-theme="dark"] .lot-classification { background: #0b1220; border-color: #334155; color: #e2e8f0; }
        [data-bs-theme="dark"] .lot-classification.match { background: #065f46; border-color: #10b981; color: #d1fae5; }
        [data-bs-theme="dark"] .lot-classification.mismatch { background: #7f1d1d; border-color: #f87171; color: #fee2e2; }
        [data-bs-theme="dark"] .btn-refresh-lots { background: #1f2937; color: #c7d2fe; border-color: #374151; }
        [data-bs-theme="dark"] .btn-refresh-lots:hover { background: #111827; color: #e0e7ff; border-color: #4b5563; }

        /* Rotating icon animation */
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .fa-sync-alt.spinning {
            animation: spin 1s linear infinite;
        }
    </style>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Assign Available Lots</h4>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('lot-requests.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-2"></i>Back to List
                    </a>
                    <a href="{{ route('lot-requests.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Requests
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Available Lots -->
        <div class="col-lg-8">
            <div class="assignment-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="prioritizeLipas" checked>
                            <label class="form-check-label text-white" for="prioritizeLipas">
                                LIPAS Priority
                            </label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="prioritizeTat" checked>
                            <label class="form-check-label text-white" for="prioritizeTat">
                                TAT Priority
                            </label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="prioritizeSameModel">
                            <label class="form-check-label text-white" for="prioritizeSameModel">
                               Same Model Priority
                            </label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="prioritizeSmallQty">
                            <label class="form-check-label text-white" for="prioritizeSmallQty">
                                Small Qty
                            </label>
                        </div>
                        <button type="button" class="btn btn-refresh-lots" onclick="refreshLotList()" title="Refresh available lots">
                            <i class="fas fa-sync-alt me-2"></i>Refresh Lot List
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Flash messages / validation errors -->
                    @if(session('success'))
                        <div class="alert alert-success">{{ session('success') }}</div>
                    @endif
                    @if(session('error'))
                        <div class="alert alert-danger">{{ session('error') }}</div>
                    @endif
                    @if($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    <form id="lotAssignmentForm" action="{{ route('lot-requests.process-assignment', $lotRequest) }}" method="POST">
                        @csrf
                        
                        @php
                            $lotsCount = is_countable($availableLots) ? 
                                (is_array($availableLots) ? count($availableLots) : $availableLots->count()) : 0;
                        @endphp
                        @if($lotsCount > 0)
                            @foreach($availableLots as $lot)
                                @php
                                    $priorityClass = '';
                                    
                                    if ($lot->lipas_yn) {
                                        $priorityClass = 'priority-lipas';
                                    } elseif (($lot->stagnant_tat ?? 0) >= 3) {
                                        $priorityClass = 'priority-critical';
                                    } elseif (($lot->stagnant_tat ?? 0) >= 2) {
                                        $priorityClass = 'priority-high';
                                    } elseif (($lot->stagnant_tat ?? 0) >= 1) {
                                        $priorityClass = 'priority-medium';
                                    }
                                    
                                    // Compatibility and scoring based on assignment matrix
                                    // Rules per table:
                                    // - Size: exact match only
                                    // - Type (6S/4S): 6S can take 4S (less priority); 4S requires 4S
                                    // - Class (COLOR/MONO): COLOR can take MONO (less priority); MONO requires MONO
                                    // - Work: NORMAL can take WH REWORK (less priority);
                                    //          PROCESS RW, OI REWORK, WH REWORK can take NORMAL (less priority)
                                    
                                    $compatibilityDetails = [];
                                    $bestDetail = null;
                                    $bestScore = -1;
                                    
                                    $workAlt = [
                                        'NORMAL' => ['WH REWORK'],
                                        'PROCESS RW' => ['NORMAL'],
                                        'OI REWORK' => ['NORMAL'],
                                        'WH REWORK' => ['NORMAL'],
                                    ];
                                    
                                    foreach ($lotRequest->lotRequestItems as $requestItem) {
                                        $equipment = $requestItem->equipment;
                                        if (!$equipment) { continue; }
                                        
                                        // Size
                                        $sizeStatus = ($equipment->size && $lot->lot_size && $equipment->size === $lot->lot_size) ? 'exact' : 'none';
                                        
                                        // Type: equipment->insp_type vs lot->eqp_class
                                        $typeStatus = 'none';
                                        if ($equipment->insp_type && $lot->eqp_class) {
                                            if ($equipment->insp_type === $lot->eqp_class) {
                                                $typeStatus = 'exact';
                                            } elseif ($equipment->insp_type === '6S' && $lot->eqp_class === '4S') {
                                                $typeStatus = 'alt';
                                            }
                                        }
                                        
                                        // Class: equipment->cam_class vs lot->eqp_type
                                        $classStatus = 'none';
                                        if ($equipment->cam_class && $lot->eqp_type) {
                                            if ($equipment->cam_class === $lot->eqp_type) {
                                                $classStatus = 'exact';
                                            } elseif ($equipment->cam_class === 'COLOR' && $lot->eqp_type === 'MONO') {
                                                $classStatus = 'alt';
                                            }
                                        }
                                        
                                        // Work: equipment->alloc_type vs lot->work_type
                                        $workStatus = 'none';
                                        if ($equipment->alloc_type && $lot->work_type) {
                                            if ($equipment->alloc_type === $lot->work_type) {
                                                $workStatus = 'exact';
                                            } else {
                                                $alts = $workAlt[$equipment->alloc_type] ?? [];
                                                if (in_array($lot->work_type, $alts)) {
                                                    $workStatus = 'alt';
                                                }
                                            }
                                        }
                                        
                                        $overallAssignable = $sizeStatus !== 'none' && $typeStatus !== 'none' && $classStatus !== 'none' && $workStatus !== 'none';
                                        
                                        // Scoring: size exact=4; type exact=3 alt=1; class exact=3 alt=1; work exact=2 alt=1
                                        $score = 0;
                                        if ($sizeStatus === 'exact') $score += 4; // required
                                        $score += ($typeStatus === 'exact') ? 3 : (($typeStatus === 'alt') ? 1 : 0);
                                        $score += ($classStatus === 'exact') ? 3 : (($classStatus === 'alt') ? 1 : 0);
                                        $score += ($workStatus === 'exact') ? 2 : (($workStatus === 'alt') ? 1 : 0);
                                        
                                        $detail = [
                                            'equipment' => $equipment,
                                            'size_status' => $sizeStatus,
                                            'type_status' => $typeStatus,
                                            'class_status' => $classStatus,
                                            'work_status' => $workStatus,
                                            'assignable' => $overallAssignable,
                                            'score' => $score,
                                        ];
                                        $compatibilityDetails[] = $detail;
                                        
                                        if ($overallAssignable && $score > $bestScore) {
                                            $bestScore = $score;
                                            $bestDetail = $detail;
                                        }
                                    }
                                    
                                    // Fallback to first detail if none assignable
                                    if (!$bestDetail && !empty($compatibilityDetails)) {
                                        $bestDetail = $compatibilityDetails[0];
                                        $bestScore = $bestDetail['score'];
                                    }
                                @endphp
                                
                                <div class="available-lot {{ $priorityClass }}" 
                                     data-lot-id="{{ $lot->lot_code }}" 
                                     data-priority="{{ $lot->priority_score }}"
                                     data-model="{{ $lot->model ?? '' }}"
                                     data-tat="{{ $lot->stagnant_tat ?? 0 }}"
                                     data-lipas="{{ $lot->lipas_yn ? '1' : '0' }}"
                                     data-qty-class="{{ strtoupper($lot->qty_class ?? '') }}"
                                     data-assignable="{{ ($bestDetail['assignable'] ?? false) ? '1' : '0' }}"
                                     data-assign-score="{{ (int) ($bestDetail['score'] ?? 0) }}">
                                    
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="d-flex align-items-center flex-wrap gap-2 lot-header">
                                                <div class="form-check m-0">
                                                    <input class="form-check-input lot-checkbox" 
                                                           type="checkbox" 
                                                           name="selected_lots[]" 
                                                           value="{{ $lot->lot_code }}"
                                                           id="lot_{{ $lot->lot_code }}">
                                                    <label class="form-check-label fw-bold lot-code" for="lot_{{ $lot->lot_code }}">
                                                        <span class="text-info">
                                                        {{ $lot->lot_code }}</span>
                                                    </label>
                                                </div>
                                                <span>|</span>
                                                <span class="text-primary lot-model">{{ $lot->model_15 ?? 'N/A' }}</span>
                                                <span>|</span>
                                                <span class="text-info">{{ number_format($lot->available_quantity ?? 0) }} {{ $lot->qty_class ?? 'PCS' }}</span>
                                                <span>|</span>
                                                <span class="text-secondary">{{ $lot->lot_location ?? 'N/A' }}</span>
                                                <span>|</span>
                                                <span class="text-warning">{{ number_format($lot->stagnant_tat ?? 0, 2) }} Days</span>
                                                <span>|</span>
                                                <span class="text-info">Lipas: {{ $lot->lipas_yn ? 'Y' : 'N' }}</span>
                                            </div>
                                        </div>

                                        <div class="col-12 mt-2">
                                            <div class="lot-classifications">
                                                @php
                                                    $totalEquipment = count($compatibilityDetails);
                                                    $assignableCount = collect($compatibilityDetails)->where('assignable', true)->count();
                                                    $sizeCls = ($bestDetail['size_status'] ?? 'none') === 'exact' ? 'match' : 'mismatch';
                                                    $typeStatus = $bestDetail['type_status'] ?? 'none';
                                                    $classStatus = $bestDetail['class_status'] ?? 'none';
                                                    $workStatus = $bestDetail['work_status'] ?? 'none';
                                                @endphp
                                                <div class="d-flex align-items-center flex-wrap gap-2">
                                                    <span class="lot-classification {{ $sizeCls }}">
                                                        Size: {{ $lot->lot_size ?? 'N/A' }}
                                                    </span>
                                                    <span class="lot-classification {{ $typeStatus === 'exact' ? 'match' : ($typeStatus === 'alt' ? 'alt' : 'mismatch') }}">
                                                        Type: {{ $lot->eqp_class ?? 'N/A' }}
                                                    </span>
                                                    <span class="lot-classification {{ $classStatus === 'exact' ? 'match' : ($classStatus === 'alt' ? 'alt' : 'mismatch') }}">
                                                        Class: {{ $lot->eqp_type ?? 'N/A' }}
                                                    </span>
                                                    <span class="lot-classification {{ $workStatus === 'exact' ? 'match' : ($workStatus === 'alt' ? 'alt' : 'mismatch') }}">
                                                        Work: {{ $lot->work_type ?? 'N/A' }}
                                                    </span>
                                                    <span class="small ms-2">
                                                        <i class="fas {{ ($bestDetail['assignable'] ?? false) ? 'fa-check-circle text-success' : 'fa-exclamation-triangle text-warning' }}"></i>
                                                        {{ $assignableCount }}/{{ $totalEquipment }} equipment assignable
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Quantity Assignment -->
                                </div>
                            @endforeach
                        @else
                            <div class="text-center py-5">
                                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Compatible Lots Available</h5>
                                <p class="text-muted mb-0">
                                    No lots in the system match the equipment requirements for this request.
                                </p>
                            </div>
                        @endif
                    </form>
                </div>
            </div>
        </div>

        <!-- Assignment Summary -->
        <div class="col-lg-4">
            <div class="assignment-summary">
                <h6 class="mb-3">
                    <i class="fas fa-clipboard-check me-2"></i>Assignment Summary
                </h6>
                
                <!-- Requested Equipment Numbers -->
                <div class="mb-3">
                    <div class="d-flex flex-wrap gap-2">
                        @foreach($lotRequest->lotRequestItems as $item)
                            <span class="badge bg-white text-primary border border-2 rounded-3 py-2 px-3 fs-6">
                                <i class="fas fa-hashtag me-1"></i>{{ $item->equipment_number }}
                            </span>
                        @endforeach
                    </div>
                </div>
                
                <!-- Request Details -->
                <div class="mb-4">
                    <h6 class="text-light">Request Details</h6>
                    <div class="small">
                        <strong>Requestor:</strong> {{ $lotRequest->user->emp_name }}<br>
                        <strong>Total Lots Needed:</strong> {{ $lotRequest->total_quantity }}
                    </div>
                </div>
                
                <!-- Equipment Requirements -->
                <div class="mb-4">
                    <h6 class="text-light">Equipment Requirements</h6>
                    @php
                        $reqSizes = $lotRequest->lotRequestItems->pluck('size')->filter()->unique()->implode(', ');
                        $reqTypes = $lotRequest->lotRequestItems->pluck('insp_type')->filter()->unique()->implode(', ');
                        $reqClasses = $lotRequest->lotRequestItems->pluck('cam_class')->filter()->unique()->implode(', ');
                        $reqWork = $lotRequest->lotRequestItems->pluck('alloc_type')->filter()->unique()->implode(', ');
                    @endphp
                    <div class="d-flex flex-wrap gap-2">
                        <span class="badge bg-white text-primary border border-2 rounded-pill px-3 py-2">
                            <i class="fas fa-ruler-combined me-1"></i>Size: {{ $reqSizes ?: 'N/A' }}
                        </span>
                        <span class="badge bg-white text-primary border border-2 rounded-pill px-3 py-2">
                            <i class="fas fa-cubes me-1"></i>Type: {{ $reqTypes ?: 'N/A' }}
                        </span>
                        <span class="badge bg-white text-primary border border-2 rounded-pill px-3 py-2">
                            <i class="fas fa-layer-group me-1"></i>Class: {{ $reqClasses ?: 'N/A' }}
                        </span>
                        <span class="badge bg-white text-primary border border-2 rounded-pill px-3 py-2">
                            <i class="fas fa-briefcase me-1"></i>Work: {{ $reqWork ?: 'N/A' }}
                        </span>
                    </div>
                </div>
                
                <!-- Selection Summary -->
                <div class="mb-4">
                    <h6 class="text-light">Current Selection</h6>
                    <div id="selectionSummary" class="small">
                        <em>No lots selected</em>
                    </div>
                </div>
                
                <!-- Assignment Actions -->
                <div class="d-grid gap-2">
                    <button type="submit" 
                            form="lotAssignmentForm" 
                            class="btn btn-assign-selected" 
                            id="assignButton" 
                            disabled>
                        <i class="fas fa-check me-2"></i>Assign Selected Lots
                    </button>
                    
                    <button type="button" class="btn btn-outline-light" onclick="autoSelectBest()">
                        <i class="fas fa-magic me-2"></i>Auto-Select Best Matches
                    </button>
                    
                    <button type="button" class="btn btn-outline-light" onclick="clearSelection()">
                        <i class="fas fa-times me-2"></i>Clear Selection
                    </button>
                </div>
                
                <!-- Help -->
                <div class="mt-4 p-3 bg-rgba-255-255-255-0-1 rounded">
                    <h6 class="text-light small">💡 Tips:</h6>
                    <ul class="small mb-0 text-light">
                        <li>LIPAS lots have highest priority</li>
                        <li>High stagnant TAT indicates urgent lots</li>
                        <li>Select lots carefully to optimize machine compatibility</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const checkboxes = document.querySelectorAll('.lot-checkbox');
            const assignButton = document.getElementById('assignButton');
            const selectionSummary = document.getElementById('selectionSummary');
            
            // Ensure clicking Assign submits the form even if form attribute is not respected
            assignButton?.addEventListener('click', function(e) {
                // If button is enabled, submit the form explicitly
                if (!this.disabled) {
                    const form = document.getElementById('lotAssignmentForm');
                    if (form) form.submit();
                }
            });
            
            // Priority toggle switches
            const prioritizeLipas = document.getElementById('prioritizeLipas');
            const prioritizeTat = document.getElementById('prioritizeTat');
            const prioritizeSameModel = document.getElementById('prioritizeSameModel');
            const prioritizeSmallQty = document.getElementById('prioritizeSmallQty');
            
            // Filter lots by qty_class depending on Small Qty toggle
            // ON  => include both L and S
            // OFF => show only L
            function filterLotsByQty() {
                const includeSmall = !!(prioritizeSmallQty && prioritizeSmallQty.checked);
                const lots = document.querySelectorAll('.available-lot');
                lots.forEach(lot => {
                    const qty = (lot.dataset.qtyClass || '').toUpperCase();
                    const isSmall = qty === 'S';
                    if (!includeSmall && isSmall) {
                        // Hide small-qty lots when toggle is OFF
                        lot.style.display = 'none';
                        lot.setAttribute('data-hidden', 'true');
                        const cb = lot.querySelector('.lot-checkbox');
                        if (cb && cb.checked) {
                            cb.checked = false;
                            cb.dispatchEvent(new Event('change'));
                        }
                    } else {
                        lot.style.display = '';
                        lot.removeAttribute('data-hidden');
                    }
                });
            }
            
            // Function to sort lots based on priority settings
            function sortLotsByPriority() {
                const formElement = document.getElementById('lotAssignmentForm');
                const availableLots = Array.from(document.querySelectorAll('.available-lot'));
                const csrfToken = formElement.querySelector('[name="_csrf"]');
                
                // Get the previous model from last assigned lot (if any)
                const previousModel = '{{ $lotRequest->lotAssignments->last()->model ?? "" }}';
                
                // Show loading indicator
                const firstLot = availableLots[0];
                if (firstLot) {
                    const loadingDiv = document.createElement('div');
                    loadingDiv.className = 'text-center py-3';
                    loadingDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sorting lots...';
                    firstLot.parentNode.insertBefore(loadingDiv, firstLot);
                }
                
                setTimeout(() => {
                    availableLots.sort((a, b) => {
                        let scoreA = 0, scoreB = 0;
                        
                        // LIPAS Priority
                        if (prioritizeLipas.checked) {
                            const lipasA = a.dataset.lipas === '1';
                            const lipasB = b.dataset.lipas === '1';
                            if (lipasA) scoreA += 10000;
                            if (lipasB) scoreB += 10000;
                        }
                        
                        // TAT Priority (stagnant time)
                        if (prioritizeTat.checked) {
                            const tatA = parseInt(a.dataset.tat) || 0;
                            const tatB = parseInt(b.dataset.tat) || 0;
                            scoreA += tatA * 10; // Multiply by 10 to give more weight
                            scoreB += tatB * 10;
                        }
                        
                        // Same Model Priority
                        if (prioritizeSameModel.checked && previousModel) {
                            const modelA = a.dataset.model || '';
                            const modelB = b.dataset.model || '';
                            if (modelA === previousModel) scoreA += 5000;
                            if (modelB === previousModel) scoreB += 5000;
                        }
                        
                        // If all priorities are disabled, sort by lot ID
                        if (!prioritizeLipas.checked && !prioritizeTat.checked && !prioritizeSameModel.checked) {
                            return a.dataset.lotId.localeCompare(b.dataset.lotId);
                        }
                        
                        return scoreB - scoreA; // Sort descending
                    });
                    
                    // Remove loading indicator
                    const loadingDiv = formElement.querySelector('.text-center.py-3');
                    if (loadingDiv) loadingDiv.remove();
                    
                    // Clear and re-append sorted lots
                    availableLots.forEach(lot => {
                        formElement.appendChild(lot);
                    });
                }, 100);
            }
            
            // Add event listeners to toggle switches
            prioritizeLipas.addEventListener('change', sortLotsByPriority);
            prioritizeTat.addEventListener('change', sortLotsByPriority);
            prioritizeSameModel.addEventListener('change', sortLotsByPriority);
            if (prioritizeSmallQty) {
                prioritizeSmallQty.addEventListener('change', filterLotsByQty);
            }
            
            // Apply initial qty filter on load
            filterLotsByQty();
            
            // Handle lot selection (checkbox only toggles visual state)
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const lotItem = this.closest('.available-lot');
                    if (this.checked) {
                        lotItem.classList.add('selected');
                    } else {
                        lotItem.classList.remove('selected');
                    }
                    updateSelectionSummary();
                });
            });

            // Make entire lot card clickable to toggle selection
            document.addEventListener('click', function(e) {
                const lotItem = e.target.closest('.available-lot');
                if (!lotItem) return;
                const tag = e.target.tagName.toLowerCase();
                if (['input','select','a','button','label'].includes(tag)) return;
                const checkbox = lotItem.querySelector('.lot-checkbox');
                checkbox.checked = !checkbox.checked;
                checkbox.dispatchEvent(new Event('change'));
            });
            
            function updateSelectionSummary() {
                const selectedCheckboxes = document.querySelectorAll('.lot-checkbox:checked');
                const count = selectedCheckboxes.length;
                
                if (count === 0) {
                    selectionSummary.innerHTML = '<em>No lots selected</em>';
                    assignButton.disabled = true;
                } else {
                    selectionSummary.innerHTML = `
                        <strong>${count} lots selected</strong>
                    `;
                    assignButton.disabled = false;
                }
            }
            
            // No quantity inputs anymore
            
            // Auto-select best matches
            window.autoSelectBest = function() {
                // Clear current selection
                clearSelection();
                
                // Select top priority lots that are assignable according to the matrix
                const availableLots = document.querySelectorAll('.available-lot:not([data-hidden="true"])');
                const lotsNeeded = {{ $lotRequest->total_quantity }};
                let selected = 0;
                
                Array.from(availableLots)
                    .filter(lot => lot.dataset.assignable === '1')
                    .sort((a, b) => {
                        // First by assignment score, then by priority score
                        const ascoreDiff = (parseInt(b.dataset.assignScore || '0') - parseInt(a.dataset.assignScore || '0'));
                        if (ascoreDiff !== 0) return ascoreDiff;
                        return parseInt(b.dataset.priority || '0') - parseInt(a.dataset.priority || '0');
                    })
                    .forEach(lot => {
                        if (selected < lotsNeeded) {
                            const checkbox = lot.querySelector('.lot-checkbox');
                            checkbox.checked = true;
                            checkbox.dispatchEvent(new Event('change'));
                            selected++;
                        }
                    });
            };
            
            // Clear selection
            window.clearSelection = function() {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = false;
                    checkbox.dispatchEvent(new Event('change'));
                });
            };
            
            // Refresh lot list from UpdateWip database
            window.refreshLotList = function() {
                const button = event.target.closest('button');
                const icon = button.querySelector('.fa-sync-alt');
                const originalHtml = button.innerHTML;
                
                // Stop pulse animation and add spinning animation
                button.style.animation = 'none';
                icon.classList.add('spinning');
                button.disabled = true;
                
                // Show loading message with better styling
                button.innerHTML = '<i class="fas fa-sync-alt spinning me-2"></i>REFRESHING DATABASE...';
                button.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                button.style.color = '#ffffff';
                button.style.border = 'none';
                
                // Create AbortController for timeout
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout
                
                // Make AJAX call to refresh the available_lots_cache from updatewip
                fetch('{{ route('lot-requests.refresh-cache') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    signal: controller.signal
                })
                .then(response => {
                    clearTimeout(timeoutId);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // Show success message
                        button.innerHTML = '<i class="fas fa-check me-2"></i>' + data.count + ' LOTS IMPORTED!';
                        button.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
                        
                        // Show simple alert if no toast system
                        alert(data.message);
                        
                        // Reload page after 1 second to show the new lots
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        throw new Error(data.message || 'Failed to refresh lots');
                    }
                })
                .catch(error => {
                    clearTimeout(timeoutId);
                    console.error('Error refreshing lots:', error);
                    
                    let errorMessage = 'Failed to refresh lots';
                    if (error.name === 'AbortError') {
                        errorMessage = 'Request timeout - please try again';
                    } else if (error.message) {
                        errorMessage = error.message;
                    }
                    
                    // Show error state
                    button.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>REFRESH FAILED';
                    button.style.background = 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)';
                    
                    alert('Error: ' + errorMessage);
                    
                    // Reset button after 2 seconds
                    setTimeout(() => {
                        button.innerHTML = originalHtml;
                        button.style = '';
                        button.disabled = false;
                        button.style.animation = 'pulse 2s ease-in-out infinite';
                    }, 2000);
                });
            };
        });
    </script>
</x-app-layout>