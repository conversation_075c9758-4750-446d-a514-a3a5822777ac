<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class LotRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'request_number',
        'user_id',
        'notes',
        'status',
        'priority_score',
        'is_urgent',
        'assigned_to_manager',
        'request_date',
        'responded_at',
        'completed_at'
    ];

    protected $casts = [
        'request_date' => 'datetime',
        'responded_at' => 'datetime',
        'completed_at' => 'datetime',
        'priority_score' => 'integer',
        'is_urgent' => 'boolean',
    ];

    /**
     * Get the user who created this lot request
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the lot request items for this request
     */
    public function lotRequestItems()
    {
        return $this->hasMany(LotRequestItem::class);
    }
    
    /**
     * Get the lot assignments for this request
     */
    public function lotAssignments()
    {
        return $this->hasMany(LotAssignment::class);
    }
    
    /**
     * Get the priority record for this request
     */
    public function priority()
    {
        return $this->hasOne(LotRequestPriority::class);
    }
    
    /**
     * Get the manager assigned to this request
     */
    public function assignedManager()
    {
        return $this->belongsTo(User::class, 'assigned_to_manager', 'emp_no');
    }

    /**
     * Generate request number based on equipment number and sequence
     * Example: VI280-0001
     */
    public static function generateRequestNumber($equipmentNumber = null)
    {
        if (empty($equipmentNumber)) {
            // Fallback to old format if no equipment number provided
            return 'LTR-' . strtoupper(Str::random(8));
        }
        
        // Use a database transaction with locking to handle race conditions
        return DB::transaction(function () use ($equipmentNumber) {
            $maxAttempts = 15;
            $attempt = 0;
            
            while ($attempt < $maxAttempts) {
                try {
                    // Get the next sequence number for this equipment with locking
                    $lastSequence = DB::table('lot_requests')
                        ->where('request_number', 'like', $equipmentNumber . '-%')
                        ->orderBy('id', 'desc')
                        ->lockForUpdate()
                        ->first();
                    
                    $nextSequence = 1;
                    if ($lastSequence) {
                        // Extract sequence number from request number (e.g., VI280-0001 -> 0001)
                        $parts = explode('-', $lastSequence->request_number);
                        if (count($parts) >= 2) {
                            $lastNum = (int) end($parts);
                            $nextSequence = $lastNum + 1;
                        }
                    }
                    
                    // Format as 4-digit sequence
                    $proposedNumber = $equipmentNumber . '-' . str_pad($nextSequence, 4, '0', STR_PAD_LEFT);
                    
                    // Double-check uniqueness within the transaction
                    if (!self::where('request_number', $proposedNumber)->lockForUpdate()->exists()) {
                        return $proposedNumber;
                    }
                    
                    $attempt++;
                    // Progressive backoff: increase delay with each attempt
                    usleep(50000 + ($attempt * 25000)); // 50ms to 425ms
                    
                } catch (\Exception $e) {
                    $attempt++;
                    if ($attempt >= $maxAttempts) {
                        throw $e;
                    }
                    usleep(100000); // 100ms
                }
            }
            
            // If we still can't generate a unique sequence after maximum attempts,
            // use a more unique fallback combining timestamp and random string
            return $equipmentNumber . '-' . date('His') . strtoupper(Str::random(2));
        });
        
        // Fallback: if we can't generate a unique sequence, use timestamp
        return $equipmentNumber . '-' . str_pad(time() % 10000, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get status badge class
     */
    public function getStatusBadgeClass()
    {
        return match($this->status) {
            'pending' => 'bg-warning text-dark',
            'accepted' => 'bg-info text-white',
            'assigned' => 'bg-primary text-white',
            'delivered' => 'bg-success text-white',
            'completed' => 'bg-success text-white',
            'cancelled' => 'bg-danger text-white',
            default => 'bg-secondary text-white'
        };
    }

    /**
     * Get formatted status for display
     */
    public function getFormattedStatusAttribute()
    {
        return match($this->status) {
            'pending' => 'Pending Assignment',
            'accepted' => 'Accepted by Manager',
            'assigned' => 'Lots Assigned',
            'delivered' => 'Delivered to Operator',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
            default => ucfirst(str_replace('_', ' ', $this->status))
        };
    }

    /**
     * Get total quantity (count of unique lot quantities)
     * Equipment with the same quantity are grouped together and count as one lot
     */
    public function getTotalQuantityAttribute()
    {
        return $this->lotRequestItems->pluck('quantity')->unique()->sum();
    }

    /**
     * Get quantity display for individual items
     * If quantity appears multiple times, show 'shared' instead of number
     */
    public function getQuantityDisplayForItem($item)
    {
        $allQuantities = $this->lotRequestItems->pluck('quantity')->toArray();
        $countOfThisQuantity = array_count_values($allQuantities)[$item->quantity] ?? 0;
        
        if ($countOfThisQuantity > 1) {
            return 'shared';
        }
        
        return $item->quantity;
    }

    /**
     * Get items grouped by quantity for display
     */
    public function getGroupedItemsAttribute()
    {
        return $this->lotRequestItems->groupBy('quantity')->map(function ($items, $quantity) {
            return [
                'quantity' => $quantity,
                'items' => $items,
                'is_shared' => $items->count() > 1,
                'item_count' => $items->count()
            ];
        });
    }

    /**
     * Get unique area stations from equipment items
     */
    public function getAreaStationsAttribute()
    {
        return $this->lotRequestItems
            ->map(function ($item) {
                return $item->equipment ? $item->equipment->eqp_area : null;
            })
            ->filter() // Remove null values
            ->unique()
            ->values()
            ->implode(', ');
    }

    /**
     * Get unique equipment types (eqp_type) from requested equipment
     */
    public function getEquipmentTypesAttribute()
    {
        return $this->lotRequestItems
            ->map(function ($item) {
                return $item->equipment ? $item->equipment->eqp_type : null;
            })
            ->filter()
            ->unique()
            ->values()
            ->implode(', ');
    }

    /**
     * Get elapsed time in seconds from request_date until assigned or delivered/completed.
     * If not yet assigned or delivered, counts until now.
     */
    public function getElapsedSecondsAttribute(): int
    {
        $start = $this->request_date ?? now();

        // Determine end time based on status
        $status = $this->status;
        $end = now();

        if (in_array($status, ['delivered', 'completed'])) {
            // Prefer completed_at if available, else earliest delivered_at on assignments
            $end = $this->completed_at ?? ($this->lotAssignments()->min('delivered_at') ?? now());
        } elseif ($status === 'assigned') {
            // Stop at earliest assigned_date
            $end = $this->lotAssignments()->min('assigned_date') ?? now();
        } else {
            // pending/accepted -> still running, use now
            $end = now();
        }

        return max(0, $start->diffInSeconds($end));
    }

    /**
     * Human readable elapsed time, e.g. "2h 15m", "1d 3h".
     */
    public function getElapsedHumanAttribute(): string
    {
        $seconds = $this->elapsed_seconds;
        $minutes = intdiv($seconds, 60);
        $hours = intdiv($minutes, 60);
        $days = intdiv($hours, 24);

        if ($minutes < 60) {
            return $minutes . 'm';
        }
        if ($hours < 24) {
            return $hours . 'h ' . ($minutes % 60) . 'm';
        }
        $remainingHours = $hours % 24;
        return $days . 'd' . ($remainingHours > 0 ? ' ' . $remainingHours . 'h' : '');
    }
    
    /**
     * Scope for pending requests (management queue)
     */
    public function scopePendingManagement(Builder $query)
    {
        return $query->where('status', 'pending')
                    ->orderBy('is_urgent', 'desc')
                    ->orderBy('priority_score', 'desc')
                    ->orderBy('request_date', 'asc'); // FIFO within same priority
    }
    
    /**
     * Scope for requests assigned to a manager
     */
    public function scopeAssignedToManager(Builder $query, $managerEmpNo)
    {
        return $query->where('assigned_to_manager', $managerEmpNo)
                    ->whereIn('status', ['accepted', 'assigned']);
    }
    
    /**
     * Scope for active requests (not completed/cancelled)
     */
    public function scopeActive(Builder $query)
    {
        return $query->whereNotIn('status', ['completed', 'cancelled']);
    }
    
    /**
     * Accept the lot request for processing
     */
    public function acceptRequest($managerEmpNo, $notes = null)
    {
        $this->update([
            'status' => 'accepted',
            'assigned_to_manager' => $managerEmpNo,
            'responded_at' => now()
        ]);
        
        // Update or create priority record
        $this->priority()->updateOrCreate(
            ['lot_request_id' => $this->id],
            [
                'fifo_order' => $this->id, // Use ID as FIFO order
                'priority_set_by' => $managerEmpNo,
                'priority_updated_at' => now(),
                'priority_notes' => $notes
            ]
        );
        
        return $this;
    }
    
    /**
     * Mark lots as assigned to this request
     */
    public function markLotsAssigned()
    {
        $this->update(['status' => 'assigned']);
        return $this;
    }
    
    /**
     * Mark request as delivered
     */
    public function markDelivered($deliveredBy = null)
    {
        $this->update([
            'status' => 'delivered',
            'completed_at' => now()
        ]);
        
        // Update all associated lot assignments
        $this->lotAssignments()->update([
            'assignment_status' => 'delivered',
            'delivered_at' => now(),
            'delivered_by' => $deliveredBy
        ]);
        
        return $this;
    }
    
    /**
     * Check if request can be accepted by manager
     */
    public function canBeAccepted()
    {
        return $this->status === 'pending';
    }
    
    /**
     * Check if request can have lots assigned
     */
    public function canAssignLots()
    {
        return $this->status === 'accepted';
    }
    
    /**
     * Check if request can be delivered
     */
    public function canBeDelivered()
    {
        return $this->status === 'assigned' && $this->lotAssignments()->count() > 0;
    }
    
    /**
     * Get next FIFO order number
     */
    public static function getNextFifoOrder()
    {
        $lastOrder = static::join('lot_request_priorities', 'lot_requests.id', '=', 'lot_request_priorities.lot_request_id')
                          ->max('lot_request_priorities.fifo_order');
        
        return ($lastOrder ?? 0) + 1;
    }
    
    /**
     * Calculate priority score based on business rules
     */
    public function calculatePriorityScore()
    {
        $score = 0;
        
        // Base FIFO score (age of request)
        $hoursOld = $this->request_date->diffInHours(now());
        $score += min($hoursOld, 1000); // Cap at 1000
        
        // Urgent requests get priority boost
        if ($this->is_urgent) {
            $score += 500;
        }
        
        // Equipment count factor (more equipment = higher priority)
        $equipmentCount = $this->lotRequestItems()->count();
        $score += ($equipmentCount * 10);
        
        return $score;
    }
}
