<x-app-layout>
    <x-slot name="header">
        User Management
    </x-slot>

    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-lg col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary bg-opacity-10 text-primary me-3">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ number_format($stats['total_users']) }}</h3>
                            <p class="text-muted mb-0">Total Users</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-danger bg-opacity-10 text-danger me-3">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ number_format($stats['admin_users']) }}</h3>
                            <p class="text-muted mb-0">Administrators</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-info bg-opacity-10 text-info me-3">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ number_format($stats['manager_users']) }}</h3>
                            <p class="text-muted mb-0">Managers</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary bg-opacity-10 text-primary me-3">
                            <i class="fas fa-user"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ number_format($stats['regular_users']) }}</h3>
                            <p class="text-muted mb-0">Regular Users</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success bg-opacity-10 text-success me-3">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ number_format($stats['active_users']) }}</h3>
                            <p class="text-muted mb-0">Active Users</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-secondary bg-opacity-10 text-secondary me-3">
                            <i class="fas fa-user-times"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ number_format($stats['inactive_users']) }}</h3>
                            <p class="text-muted mb-0">Inactive Users</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0"><i class="fas fa-search me-2"></i>Search & Filter Users</h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('management.users.index') }}" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Search Users</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="{{ request('search') }}" placeholder="Name or Employee Number">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label for="role" class="form-label">Role</label>
                            <select class="form-select" id="role" name="role">
                                <option value="all" {{ request('role', 'all') === 'all' ? 'selected' : '' }}>All Roles</option>
                                <option value="ADMIN" {{ request('role') === 'ADMIN' ? 'selected' : '' }}>Admin</option>
                                <option value="MANAGER" {{ request('role') === 'MANAGER' ? 'selected' : '' }}>Manager</option>
                                <option value="USER" {{ request('role') === 'USER' ? 'selected' : '' }}>User</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="" {{ request('status') === '' ? 'selected' : '' }}>All Status</option>
                                <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <div class="btn-group w-100" role="group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i>Filter
                                </button>
                                <a href="{{ route('management.users.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Clear
                                </a>
                            </div>
                        </div>
                        <!-- Preserve existing sort parameters -->
                        @if(request('sort'))
                            <input type="hidden" name="sort" value="{{ request('sort') }}">
                        @endif
                        @if(request('direction'))
                            <input type="hidden" name="direction" value="{{ request('direction') }}">
                        @endif
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- User List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0">User List</h5>
                            @if(request()->hasAny(['search', 'role', 'status']))
                                <small class="text-muted">
                                    Showing filtered results
                                    @if(request('search'))
                                        for "{{ request('search') }}"
                                    @endif
                                    @if(request('role') && request('role') !== 'all')
                                        | Role: {{ request('role') }}
                                    @endif
                                    @if(request('status'))
                                        | Status: {{ ucfirst(request('status')) }}
                                    @endif
                                </small>
                            @endif
                        </div>
                        <a href="{{ route('management.users.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add New User
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>
                                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'emp_name', 'direction' => (request('sort') === 'emp_name' && request('direction') === 'asc') ? 'desc' : 'asc']) }}" 
                                           class="text-decoration-none text-dark d-flex align-items-center">
                                            User
                                            @if(request('sort') === 'emp_name')
                                                <i class="fas fa-sort-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1 text-primary"></i>
                                            @else
                                                <i class="fas fa-sort ms-1 text-muted"></i>
                                            @endif
                                        </a>
                                    </th>
                                    <th>
                                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'role', 'direction' => (request('sort') === 'role' && request('direction') === 'asc') ? 'desc' : 'asc']) }}" 
                                           class="text-decoration-none text-dark d-flex align-items-center">
                                            Role
                                            @if(request('sort') === 'role')
                                                <i class="fas fa-sort-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1 text-primary"></i>
                                            @else
                                                <i class="fas fa-sort ms-1 text-muted"></i>
                                            @endif
                                        </a>
                                    </th>
                                    <th>
                                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'emp_verified_at', 'direction' => (request('sort') === 'emp_verified_at' && request('direction') === 'asc') ? 'desc' : 'asc']) }}" 
                                           class="text-decoration-none text-dark d-flex align-items-center">
                                            Status
                                            @if(request('sort') === 'emp_verified_at')
                                                <i class="fas fa-sort-{{ request('direction') === 'asc' ? 'up' : 'down' }} ms-1 text-primary"></i>
                                            @else
                                                <i class="fas fa-sort ms-1 text-muted"></i>
                                            @endif
                                        </a>
                                    </th>
                                    <th>Activity</th>
                                    <th>
                                        <a href="{{ request()->fullUrlWithQuery(['sort' => 'created_at', 'direction' => (request('sort') === 'created_at' && request('direction') === 'asc') ? 'desc' : 'asc']) }}" 
                                           class="text-decoration-none text-dark d-flex align-items-center">
                                            Joined
                                            @if(request('sort') === 'created_at' || !request('sort'))
                                                <i class="fas fa-sort-{{ request('direction', 'desc') === 'asc' ? 'up' : 'down' }} ms-1 text-primary"></i>
                                            @else
                                                <i class="fas fa-sort ms-1 text-muted"></i>
                                            @endif
                                        </a>
                                    </th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($users as $user)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-container me-3">
                                                @if($user->avatar)
                                                    <img src="{{ asset('storage/' . $user->avatar) }}" 
                                                         alt="{{ $user->emp_name }}" 
                                                         class="avatar-circle" 
                                                         width="40" 
                                                         height="40"
                                                         style="object-fit: cover;">
                                                @else
                                                    <div class="avatar-circle bg-primary text-white" 
                                                         style="width: 40px; height: 40px; font-size: 14px;">
                                                        {{ strtoupper(substr($user->emp_name, 0, 1)) }}
                                                    </div>
                                                @endif
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $user->emp_name }}</h6>
                                                <small class="text-muted">{{ $user->emp_no }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        @php
                                            $badgeClass = match($user->role) {
                                                'ADMIN' => 'bg-danger',
                                                'MANAGER' => 'bg-info',
                                                'USER' => 'bg-primary',
                                                default => 'bg-secondary'
                                            };
                                        @endphp
                                        <span class="badge {{ $badgeClass }}">
                                            {{ $user->role }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge {{ $user->emp_verified_at ? 'bg-success' : 'bg-warning' }}">
                                            {{ $user->emp_verified_at ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div>
                                            <small class="text-muted">{{ $user->orders_count }} orders</small><br>
                                            <small class="text-muted">{{ $user->products_count }} products</small>
                                        </div>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ $user->created_at->format('M d, Y') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ route('management.users.show', $user) }}" 
                                               class="btn btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('management.users.edit', $user) }}" 
                                               class="btn btn-outline-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @if($user->id !== auth()->id() && $user->role !== 'ADMIN')
                                                <form action="{{ route('management.users.toggle-status', $user) }}" 
                                                      method="POST" class="d-inline">
                                                    @csrf
                                                    @method('PATCH')
                                                    <button type="submit" 
                                                            class="btn btn-outline-{{ $user->emp_verified_at ? 'secondary' : 'success' }}" 
                                                            title="{{ $user->emp_verified_at ? 'Deactivate' : 'Activate' }}">
                                                        <i class="fas fa-{{ $user->emp_verified_at ? 'pause' : 'play' }}"></i>
                                                    </button>
                                                </form>
                                                <form action="{{ route('management.users.destroy', $user) }}" 
                                                      method="POST" 
                                                      class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this user?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-outline-danger" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            @elseif($user->role === 'ADMIN')
                                                <span class="badge bg-warning text-dark" title="Admin accounts are protected from deactivation">
                                                    <i class="fas fa-shield-alt me-1"></i>Protected
                                                </span>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-users fa-3x mb-3"></i>
                                            <p>No users found</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                @if($users->hasPages())
                <div class="card-footer bg-white">
                    {{ $users->links() }}
                </div>
                @endif
            </div>
        </div>
    </div>

    <style>
        .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            object-fit: cover;
            border: 2px solid #e9ecef;
        }
        
        .avatar-container {
            position: relative;
            display: inline-block;
        }
        
        .stats-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }
        
        /* Sortable header styles */
        th a {
            white-space: nowrap;
            transition: all 0.2s ease;
        }
        
        th a:hover {
            background-color: rgba(0, 123, 255, 0.1);
            border-radius: 4px;
            padding: 4px 8px;
            margin: -4px -8px;
        }
        
        th a:hover .fa-sort {
            color: var(--bs-primary) !important;
        }
        
        /* Search form enhancements */
        .card-header h6 {
            font-weight: 600;
        }
        
        .input-group-text {
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }
        
        /* Filter results indicator */
        .text-muted small {
            font-size: 0.85em;
        }
        
        /* Responsive table improvements */
        @media (max-width: 768px) {
            .btn-group-sm .btn {
                padding: 0.25rem 0.4rem;
            }
            
            .table-responsive {
                font-size: 0.9rem;
            }
        }
    </style>
</x-app-layout>