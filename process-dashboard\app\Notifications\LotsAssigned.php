<?php

namespace App\Notifications;

use App\Models\LotRequest;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class LotsAssigned extends Notification
{
    use Queueable;

    public function __construct(
        public LotRequest $lotRequest,
        public User $manager,
        public int $assignedCount
    ) {}

    public function via(object $notifiable): array
    {
        return ['database'];
    }

    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'lots_assigned',
            'lot_request_id' => $this->lotRequest->id,
            'request_number' => $this->lotRequest->request_number,
            'new_status' => 'assigned',
            'assigned_count' => $this->assignedCount,
            'performed_by' => [
                'emp_no' => $this->manager->emp_no,
                'name' => $this->manager->getDisplayName(),
            ],
            'message' => sprintf(
                '%d lot(s) were assigned to your request %s by %s (%s).',
                $this->assignedCount,
                $this->lotRequest->request_number,
                $this->manager->getDisplayName(),
                $this->manager->emp_no
            ),
            'url' => '/lot-requests/' . $this->lotRequest->id,
        ];
    }
}
