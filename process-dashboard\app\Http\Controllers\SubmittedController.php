<?php

namespace App\Http\Controllers;

use App\Models\Submitted;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SubmittedController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of submitted/finished lots from equipment.
     */
    public function index(Request $request)
    {
        // Get filter parameters
        $filters = [
            'eqp_line' => $request->get('eqp_line', 'all'),
            'eqp_area' => $request->get('eqp_area', 'all'),
            'work_type' => $request->get('work_type', 'all'),
            'lot_type' => $request->get('lot_type', 'all'),
            'submitted_by' => $request->get('submitted_by', 'all'),
            'date_range' => $request->get('date_range', 'today'), // today, this_week, this_month
            'search' => $request->get('search', ''),
        ];
        
        // Build query for submitted data
        $submittedQuery = Submitted::select('*');
        
        // Apply date range filter
        switch ($filters['date_range']) {
            case 'today':
                $submittedQuery->whereDate('submitted_at', today());
                break;
            case 'yesterday':
                $submittedQuery->whereDate('submitted_at', Carbon::yesterday());
                break;
            case 'this_week':
                $submittedQuery->whereBetween('submitted_at', [
                    Carbon::now()->startOfWeek(),
                    Carbon::now()->endOfWeek()
                ]);
                break;
            case 'this_month':
                $submittedQuery->whereBetween('submitted_at', [
                    Carbon::now()->startOfMonth(),
                    Carbon::now()->endOfMonth()
                ]);
                break;
        }
        
        // Apply equipment line filter
        if ($filters['eqp_line'] !== 'all') {
            $submittedQuery->where('eqp_line', $filters['eqp_line']);
        }
        
        // Apply equipment area filter
        if ($filters['eqp_area'] !== 'all') {
            $submittedQuery->where('eqp_area', $filters['eqp_area']);
        }
        
        // Apply work type filter
        if ($filters['work_type'] !== 'all') {
            $submittedQuery->where('work_type', $filters['work_type']);
        }
        
        // Apply lot type filter
        if ($filters['lot_type'] !== 'all') {
            $submittedQuery->where('lot_type', $filters['lot_type']);
        }
        
        // Apply submitted by filter
        if ($filters['submitted_by'] !== 'all') {
            $submittedQuery->where('submitted_by', $filters['submitted_by']);
        }
        
        // Apply search filter
        if (!empty($filters['search'])) {
            $searchTerm = $filters['search'];
            $submittedQuery->where(function($query) use ($searchTerm) {
                $query->where('lot_id', 'LIKE', '%' . $searchTerm . '%')
                      ->orWhere('model_15', 'LIKE', '%' . $searchTerm . '%')
                      ->orWhere('eqp_1', 'LIKE', '%' . $searchTerm . '%')
                      ->orWhere('eqp_line', 'LIKE', '%' . $searchTerm . '%')
                      ->orWhere('eqp_area', 'LIKE', '%' . $searchTerm . '%')
                      ->orWhere('submitted_by', 'LIKE', '%' . $searchTerm . '%');
            });
        }
        
        $submittedLots = $submittedQuery
            ->orderBy('submitted_at', 'desc')
            ->paginate(15)
            ->withQueryString();
        
        // Get filter options
        $filterOptions = [
            'equipment_lines' => Submitted::distinct()->pluck('eqp_line')->filter()->sort()->values(),
            'equipment_areas' => Submitted::distinct()->pluck('eqp_area')->filter()->sort()->values(),
            'work_types' => Submitted::distinct()->pluck('work_type')->filter()->sort()->values(),
            'lot_types' => Submitted::distinct()->pluck('lot_type')->filter()->sort()->values(),
            'submitters' => Submitted::distinct()->pluck('submitted_by')->filter()->sort()->values(),
        ];
        
        // Get summary statistics
        $stats = [
            'total_submitted' => $submittedQuery->count(),
            'submitted_today' => Submitted::submittedToday()->count(),
            'line_breakdown' => Submitted::select('eqp_line', DB::raw('COUNT(*) as count'))
                ->groupBy('eqp_line')
                ->pluck('count', 'eqp_line')
                ->toArray(),
            'area_breakdown' => Submitted::select('eqp_area', DB::raw('COUNT(*) as count'))
                ->groupBy('eqp_area')
                ->pluck('count', 'eqp_area')
                ->toArray(),
        ];
        
        return view('submitted.index', compact('submittedLots', 'filters', 'filterOptions', 'stats'));
    }
}
