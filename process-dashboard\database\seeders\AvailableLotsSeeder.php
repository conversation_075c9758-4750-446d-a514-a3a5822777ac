<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AvailableLotsCache;

class AvailableLotsSeeder extends Seeder
{
    public function run()
    {
        // Clear existing data
        AvailableLotsCache::truncate();

        $sampleLots = [
            [
                'lot_code' => 'LOT001-2024-001',
                'lot_size' => '03',
                'eqp_type' => 'COLOR',
                'eqp_class' => '6S',
                'work_type' => 'NORMAL',
                'lipas_yn' => true,  // High priority LIPAS lot
                'stagnant_tat' => 48,
                'lot_location' => 'RACK-A-001',
                'available_quantity' => 1,
                'is_available' => true,
                'last_updated' => now()
            ],
            [
                'lot_code' => 'LOT002-2024-002',
                'lot_size' => '05',
                'eqp_type' => 'MONO',
                'eqp_class' => '4S',
                'work_type' => 'PROCESS RW',
                'lipas_yn' => false,
                'stagnant_tat' => 72, // Critical stagnant time
                'lot_location' => 'RACK-A-002',
                'available_quantity' => 1,
                'is_available' => true,
                'last_updated' => now()
            ],
            [
                'lot_code' => 'LOT003-2024-003',
                'lot_size' => '10',
                'eqp_type' => 'COLOR',
                'eqp_class' => '6S',
                'work_type' => 'OI REWORK',
                'lipas_yn' => false,
                'stagnant_tat' => 24, // Medium priority
                'lot_location' => 'RACK-B-001',
                'available_quantity' => 2,
                'is_available' => true,
                'last_updated' => now()
            ],
            [
                'lot_code' => 'LOT004-2024-004',
                'lot_size' => '21',
                'eqp_type' => 'MONO',
                'eqp_class' => '4S',
                'work_type' => 'WH REWORK',
                'lipas_yn' => false,
                'stagnant_tat' => 12, // Normal priority
                'lot_location' => 'RACK-B-002',
                'available_quantity' => 1,
                'is_available' => true,
                'last_updated' => now()
            ],
            [
                'lot_code' => 'LOT005-2024-005',
                'lot_size' => '31',
                'eqp_type' => 'COLOR',
                'eqp_class' => '6S',
                'work_type' => 'NORMAL',
                'lipas_yn' => true, // Another LIPAS lot
                'stagnant_tat' => 96, // Very high stagnant time
                'lot_location' => 'RACK-C-001',
                'available_quantity' => 1,
                'is_available' => true,
                'last_updated' => now()
            ],
            [
                'lot_code' => 'LOT006-2024-006',
                'lot_size' => '32',
                'eqp_type' => 'MONO',
                'eqp_class' => '4S',
                'work_type' => 'NORMAL',
                'lipas_yn' => false,
                'stagnant_tat' => 6, // Very fresh lot
                'lot_location' => 'RACK-C-002',
                'available_quantity' => 3,
                'is_available' => true,
                'last_updated' => now()
            ]
        ];

        foreach ($sampleLots as $lotData) {
            AvailableLotsCache::create($lotData);
        }

        $this->command->info('Created ' . count($sampleLots) . ' sample available lots in cache.');
    }
}