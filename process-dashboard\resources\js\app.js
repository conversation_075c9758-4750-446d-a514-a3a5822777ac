// Import Bootstrap
import * as bootstrap from "bootstrap";
import "bootstrap/dist/js/bootstrap.bundle.min.js";

// Import SweetAlert2
import <PERSON>wal from "sweetalert2";

// Make Bootstrap and SweetAlert2 globally available
window.bootstrap = bootstrap;
window.Swal = Swal;

// Dashboard JavaScript Functions
class Dashboard {
    constructor() {
        this.init();
    }

    init() {
        this.initSidebar();
        this.initDataTables();
        this.initNotifications();
    }

    // Enhanced Sidebar functionality (merged from emergency-restore.js)
    initSidebar() {
        // Add CSS fixes for collapsed sidebar dropdowns
        this.addSidebarCSS();

        // Initialize user dropdown functionality
        this.initUserDropdown();

        // Initialize sidebar toggle functionality
        this.initSidebarToggle();

        // Initialize sidebar dropdowns
        this.initSidebarDropdowns();

        // Initialize theme toggle
        this.initThemeToggle();

        // Initialize keyboard shortcuts
        this.initKeyboardShortcuts();

        // Active menu highlighting
        this.initActiveMenuHighlighting();
    }

    // Add CSS fixes for collapsed sidebar dropdowns
    addSidebarCSS() {
        const style = document.createElement("style");
        style.textContent = `
            /* Fix collapsed sidebar dropdown positioning */
            .sidebar.collapsed .nav-item.has-dropdown .dropdown-menu {
                position: absolute !important;
                left: 70px !important;
                top: 0 !important;
                background: #2c3e50 !important;
                border-radius: 8px !important;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
                min-width: 220px !important;
                z-index: 1050 !important;
                border: 1px solid rgba(255, 255, 255, 0.1) !important;
                display: none !important;
                margin: 0 !important;
                padding: 6px 0 !important;
            }
            
            .sidebar.collapsed .nav-item.has-dropdown:hover .dropdown-menu {
                display: block !important;
            }
            
            .sidebar.collapsed .dropdown-item {
                padding: 12px 20px !important;
                color: rgba(255, 255, 255, 0.9) !important;
                display: flex !important;
                align-items: center !important;
                text-align: left !important;
                justify-content: flex-start !important;
                white-space: nowrap !important;
            }
            
            .sidebar.collapsed .dropdown-item:hover {
                background: rgba(255, 255, 255, 0.15) !important;
                color: #ffffff !important;
                padding-left: 25px !important;
            }
            
            .sidebar.collapsed .dropdown-item i {
                margin-right: 12px !important;
                width: 18px !important;
                text-align: center !important;
                color: rgba(255, 255, 255, 0.9) !important;
            }
            
            .sidebar.collapsed .dropdown-item:hover i {
                color: #ffffff !important;
            }
            
            /* Ensure tooltips work for collapsed sidebar */
            .sidebar.collapsed .nav-link {
                position: relative;
            }
            
            .sidebar.collapsed .nav-link:hover::after {
                content: attr(data-title);
                position: absolute;
                left: calc(100% + 10px);
                top: 50%;
                transform: translateY(-50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 0.875rem;
                white-space: nowrap;
                z-index: 1001;
                pointer-events: none;
                opacity: 0;
                animation: fadeInTooltip 0.3s ease 0.5s forwards;
            }
            
            @keyframes fadeInTooltip {
                from {
                    opacity: 0;
                    transform: translateY(-50%) translateX(-5px);
                }
                to {
                    opacity: 1;
                    transform: translateY(-50%) translateX(0);
                }
            }
            
            /* Hide tooltip when dropdown is showing */
            .sidebar.collapsed .nav-item.has-dropdown:hover .nav-link::after {
                display: none;
            }
        `;
        document.head.appendChild(style);
    }

    // Initialize user dropdown functionality
    initUserDropdown() {
        const userDropdown = document.getElementById("userDropdown");
        const headerDropdownMenu = document.querySelector(
            ".header-actions .dropdown-menu"
        );

        if (userDropdown && headerDropdownMenu) {
            userDropdown.onclick = function (e) {
                e.preventDefault();
                const isShown = headerDropdownMenu.classList.contains("show");

                if (isShown) {
                    headerDropdownMenu.classList.remove("show");
                    userDropdown.setAttribute("aria-expanded", "false");
                } else {
                    headerDropdownMenu.classList.add("show");
                    userDropdown.setAttribute("aria-expanded", "true");
                }
            };

            // Close on outside click
            document.onclick = function (e) {
                if (
                    !userDropdown.contains(e.target) &&
                    !headerDropdownMenu.contains(e.target)
                ) {
                    headerDropdownMenu.classList.remove("show");
                    userDropdown.setAttribute("aria-expanded", "false");
                }
            };
        }
    }

    // Initialize sidebar toggle functionality
    initSidebarToggle() {
        const sidebarToggleDesktop = document.getElementById(
            "sidebarToggleDesktop"
        );
        const sidebarToggleMobile = document.getElementById(
            "sidebarToggleMobile"
        );
        const sidebar = document.getElementById("sidebar");
        const mainContent = document.querySelector(".main-content");

        // Load saved sidebar state and transition from immediate state
        const savedSidebarState = localStorage.getItem("sidebarCollapsed");

        // Only apply collapsed state on desktop (window width > 768px)
        if (
            window.innerWidth > 768 &&
            savedSidebarState === "true" &&
            sidebar &&
            mainContent
        ) {
            // Remove immediate class and apply normal collapsed classes
            document.documentElement.classList.remove(
                "sidebar-collapsed-immediate"
            );
            sidebar.classList.add("collapsed");
            mainContent.classList.add("sidebar-collapsed");
        } else {
            // Ensure immediate class is removed if not collapsed or on mobile
            document.documentElement.classList.remove(
                "sidebar-collapsed-immediate"
            );
        }

        // Desktop sidebar collapse/expand
        if (sidebarToggleDesktop && sidebar && mainContent) {
            sidebarToggleDesktop.onclick = function () {
                const isCollapsed = sidebar.classList.contains("collapsed");

                if (isCollapsed) {
                    sidebar.classList.remove("collapsed");
                    mainContent.classList.remove("sidebar-collapsed");
                    localStorage.setItem("sidebarCollapsed", "false");
                } else {
                    sidebar.classList.add("collapsed");
                    mainContent.classList.add("sidebar-collapsed");
                    localStorage.setItem("sidebarCollapsed", "true");
                }

                // Update dropdown behavior after state change
                setTimeout(() => this.updateDropdownBehavior(), 100);
            }.bind(this);
        }

        // Mobile sidebar toggle
        if (sidebarToggleMobile && sidebar) {
            sidebarToggleMobile.onclick = function () {
                sidebar.classList.toggle("show");

                if (sidebar.classList.contains("show")) {
                    // Add overlay for mobile
                    const overlay = document.createElement("div");
                    overlay.className = "sidebar-overlay";
                    overlay.style.cssText = `
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, 0.5);
                        z-index: 999;
                        opacity: 0;
                        transition: opacity 0.3s ease;
                    `;
                    document.body.appendChild(overlay);

                    setTimeout(() => (overlay.style.opacity = "1"), 10);

                    overlay.onclick = function () {
                        sidebar.classList.remove("show");
                        overlay.style.opacity = "0";
                        setTimeout(() => {
                            if (overlay.parentNode) {
                                overlay.parentNode.removeChild(overlay);
                            }
                        }, 300);
                    };
                } else {
                    const overlay = document.querySelector(".sidebar-overlay");
                    if (overlay) {
                        overlay.style.opacity = "0";
                        setTimeout(() => {
                            if (overlay.parentNode) {
                                overlay.parentNode.removeChild(overlay);
                            }
                        }, 300);
                    }
                }
            };
        }

        // Sidebar header click toggle (breadcrumb area)
        const sidebarHeader = document.getElementById("sidebarHeader");
        if (
            sidebarHeader &&
            sidebar &&
            mainContent &&
            window.innerWidth > 768
        ) {
            sidebarHeader.onclick = function () {
                const isCollapsed = sidebar.classList.contains("collapsed");

                if (isCollapsed) {
                    sidebar.classList.remove("collapsed");
                    mainContent.classList.remove("sidebar-collapsed");
                    localStorage.setItem("sidebarCollapsed", "false");
                } else {
                    sidebar.classList.add("collapsed");
                    mainContent.classList.add("sidebar-collapsed");
                    localStorage.setItem("sidebarCollapsed", "true");
                }

                // Update dropdown behavior after state change
                setTimeout(() => this.updateDropdownBehavior(), 100);
            }.bind(this);
        }
    }

    // Initialize sidebar dropdowns
    initSidebarDropdowns() {
        const dropdownToggles = document.querySelectorAll(
            ".sidebar-menu .dropdown-toggle"
        );

        dropdownToggles.forEach(function (toggle) {
            toggle.onclick = function (e) {
                e.preventDefault();
                const parentItem = this.closest(".nav-item.has-dropdown");

                if (parentItem) {
                    const isOpen = parentItem.classList.contains("open");

                    // Close all others
                    document
                        .querySelectorAll(".nav-item.has-dropdown.open")
                        .forEach(function (item) {
                            if (item !== parentItem) {
                                item.classList.remove("open");
                            }
                        });

                    // Toggle current
                    if (isOpen) {
                        parentItem.classList.remove("open");
                    } else {
                        parentItem.classList.add("open");
                    }
                }
            };
        });

        // Enhanced collapsed sidebar behavior
        const navItems = document.querySelectorAll(".nav-item.has-dropdown");

        navItems.forEach(function (navItem) {
            const dropdownMenu = navItem.querySelector(".dropdown-menu");
            const mainNavLink = navItem.querySelector(
                ".nav-link.dropdown-toggle"
            );

            if (!dropdownMenu || !mainNavLink) return;

            // In collapsed mode, clicking nav link should navigate to first item instead of toggling
            mainNavLink.addEventListener("click", function (e) {
                const sidebar = document.getElementById("sidebar");
                if (sidebar && sidebar.classList.contains("collapsed")) {
                    e.preventDefault();
                    e.stopPropagation();
                    const firstDropdownItem =
                        dropdownMenu.querySelector(".dropdown-item");
                    if (firstDropdownItem && firstDropdownItem.href) {
                        window.location.href = firstDropdownItem.href;
                    }
                }
                // In expanded mode, the regular dropdown toggle functionality will handle the click
            });
        });
    }

    // Update dropdown behavior when sidebar state changes
    updateDropdownBehavior() {
        const sidebar = document.getElementById("sidebar");
        const navItems = document.querySelectorAll(".nav-item.has-dropdown");

        navItems.forEach(function (navItem) {
            if (sidebar && sidebar.classList.contains("collapsed")) {
                // Reset any open dropdowns when switching to collapsed mode
                navItem.classList.remove("open");
            }
        });
    }

    // Initialize theme toggle functionality
    initThemeToggle() {
        const themeToggle = document.getElementById("themeToggle");
        const themeIcon = document.getElementById("themeIcon");
        const html = document.documentElement;

        if (themeToggle && themeIcon) {
            // Load saved theme
            const currentTheme = localStorage.getItem("theme") || "light";
            html.setAttribute("data-theme-mode", currentTheme);
            this.updateThemeIcon(currentTheme);

            themeToggle.onclick = () => {
                const currentTheme = html.getAttribute("data-theme-mode");
                const newTheme = currentTheme === "dark" ? "light" : "dark";

                html.setAttribute("data-theme-mode", newTheme);
                localStorage.setItem("theme", newTheme);
                this.updateThemeIcon(newTheme);
            };
        }
    }

    // Update theme icon
    updateThemeIcon(theme) {
        const themeIcon = document.getElementById("themeIcon");
        const themeToggle = document.getElementById("themeToggle");

        if (themeIcon && themeToggle) {
            if (theme === "dark") {
                themeIcon.className = "fas fa-sun";
                themeToggle.title = "Switch to light mode";
            } else {
                themeIcon.className = "fas fa-moon";
                themeToggle.title = "Switch to dark mode";
            }
        }
    }

    // Initialize keyboard shortcuts
    initKeyboardShortcuts() {
        document.addEventListener("keydown", (e) => {
            const sidebar = document.getElementById("sidebar");
            const mainContent = document.querySelector(".main-content");

            // Ctrl/Cmd + B to toggle sidebar
            if (
                (e.ctrlKey || e.metaKey) &&
                e.key === "b" &&
                window.innerWidth > 768
            ) {
                e.preventDefault();
                if (sidebar && mainContent) {
                    const isCollapsed = sidebar.classList.contains("collapsed");

                    if (isCollapsed) {
                        sidebar.classList.remove("collapsed");
                        mainContent.classList.remove("sidebar-collapsed");
                        localStorage.setItem("sidebarCollapsed", "false");
                    } else {
                        sidebar.classList.add("collapsed");
                        mainContent.classList.add("sidebar-collapsed");
                        localStorage.setItem("sidebarCollapsed", "true");
                    }

                    // Update dropdown behavior after state change
                    setTimeout(() => this.updateDropdownBehavior(), 100);
                }
            }

            // Escape to close mobile sidebar
            if (
                e.key === "Escape" &&
                sidebar &&
                sidebar.classList.contains("show")
            ) {
                sidebar.classList.remove("show");
                const overlay = document.querySelector(".sidebar-overlay");
                if (overlay) {
                    overlay.style.opacity = "0";
                    setTimeout(() => {
                        if (overlay.parentNode) {
                            overlay.parentNode.removeChild(overlay);
                        }
                    }, 300);
                }
            }
        });
    }

    // Active menu highlighting
    initActiveMenuHighlighting() {
        const currentPath = window.location.pathname;
        const menuLinks = document.querySelectorAll(".sidebar-menu .nav-link");

        menuLinks.forEach((link) => {
            if (link.getAttribute("href") === currentPath) {
                link.classList.add("active");
            }
        });
    }


    // Initialize DataTables
    initDataTables() {
        // Note: DataTables will be initialized when we add the DataTables library
        const tables = document.querySelectorAll(".data-table");
        tables.forEach((table) => {
            if (table && typeof $.fn.DataTable !== "undefined") {
                $(table).DataTable({
                    responsive: true,
                    pageLength: 10,
                    order: [[0, "desc"]],
                    language: {
                        search: "Search:",
                        lengthMenu: "Show _MENU_ entries",
                        info: "Showing _START_ to _END_ of _TOTAL_ entries",
                        paginate: {
                            first: "First",
                            last: "Last",
                            next: "Next",
                            previous: "Previous",
                        },
                    },
                });
            }
        });
    }

    // Notifications
    initNotifications() {
        // Success notification
        window.showSuccess = (message) => {
            Swal.fire({
                icon: "success",
                title: "Success!",
                text: message,
                timer: 3000,
                showConfirmButton: false,
                toast: true,
                position: "top-end",
            });
        };

        // Error notification
        window.showError = (message) => {
            Swal.fire({
                icon: "error",
                title: "Error!",
                text: message,
                timer: 3000,
                showConfirmButton: false,
                toast: true,
                position: "top-end",
            });
        };

        // Confirmation dialog
        window.showConfirm = (message, callback) => {
            Swal.fire({
                title: "Are you sure?",
                text: message,
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#6366f1",
                cancelButtonColor: "#ef4444",
                confirmButtonText: "Yes, do it!",
                cancelButtonText: "Cancel",
            }).then((result) => {
                if (result.isConfirmed && callback) {
                    callback();
                }
            });
        };
    }

    // Utility functions
    static formatCurrency(amount) {
        return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "USD",
        }).format(amount);
    }

    static formatDate(date) {
        return new Intl.DateTimeFormat("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
        }).format(new Date(date));
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
    new Dashboard();
});

// Make Dashboard class globally available
window.Dashboard = Dashboard;

