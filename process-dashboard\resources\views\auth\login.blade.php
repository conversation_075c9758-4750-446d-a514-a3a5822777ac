<x-guest-layout>
    <div class="text-center mb-4">
        <h4 class="fw-bold mb-2">Hi, Welcome back!</h4>
        <p class="text-muted">Please enter your credentials</p>
    </div>

    <!-- Session Status -->
    <x-auth-session-status class="mb-4" :status="session('status')" />

    <!-- Error Messages -->
    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <form method="POST" action="{{ route('login') }}">
        @csrf

        <!-- Employee Number -->
        <div class="mb-3">
            <label for="emp_no" class="form-label fw-medium">{{ __('Employee Number') }}</label>
            <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-id-card text-muted"></i>
                </span>
                <input id="emp_no" 
                       type="text" 
                       class="form-control border-start-0 @error('emp_no') is-invalid @enderror" 
                       name="emp_no" 
                       value="{{ old('emp_no') }}" 
                       required 
                       autofocus 
                       autocomplete="username"
                       placeholder="Enter your employee number">
            </div>
            @error('emp_no')
                <div class="invalid-feedback d-block">
                    {{ $message }}
                </div>
            @enderror
        </div>

        <!-- Password -->
        <div class="mb-3">
            <label for="password" class="form-label fw-medium">{{ __('Password') }}</label>
            <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-lock text-muted"></i>
                </span>
                <input id="password" 
                       type="password" 
                       class="form-control border-start-0 @error('password') is-invalid @enderror" 
                       name="password" 
                       required 
                       autocomplete="current-password"
                       placeholder="Enter your password">
            </div>
            @error('password')
                <div class="invalid-feedback d-block">
                    {{ $message }}
                </div>
            @enderror
        </div>

        <!-- Remember Me & Forgot Password -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div class="form-check">
                <input id="remember_me" type="checkbox" class="form-check-input" name="remember">
                <label class="form-check-label text-sm" for="remember_me">
                    {{ __('Remember me') }}
                </label>
            </div>
            @if (Route::has('password.request'))
                <a class="text-decoration-none text-primary" href="{{ route('password.request') }}">
                    {{ __('Forgot password?') }}
                </a>
            @endif
        </div>

        <!-- Login Button -->
        <div class="d-grid mb-4">
            <button type="submit" class="btn btn-primary btn-lg py-2">
                <i class="fas fa-sign-in-alt me-2"></i>
                {{ __('Sign In') }}
            </button>
        </div>
    </form>

    <!-- Register Link -->
    @if (Route::has('register'))
        <div class="text-center mb-4">
            <p class="mb-0 text-muted">Don't have an account? 
                <a href="{{ route('register') }}" class="text-decoration-none text-primary fw-medium">
                    Register Here
                </a>
            </p>
        </div>
    @endif
</x-guest-layout>
