<x-app-layout>
    <x-slot name="header">
        User Details
    </x-slot>

    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">{{ $user->emp_name }}</h4>
                <div class="d-flex gap-2">
                    <a href="{{ route('management.users.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Users
                    </a>
                    <a href="{{ route('management.users.edit', $user) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Edit User
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- User Info Cards -->
    <div class="row g-4 mb-4">
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">User Information</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="position-relative d-inline-block mb-3">
                            <img src="{{ $user->getAvatarUrl() }}" 
                                 alt="{{ $user->emp_name }}" 
                                 class="avatar-circle" 
                                 style="width: 80px; height: 80px; object-fit: cover;"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'">
                            <div class="avatar-circle bg-primary text-white mx-auto" 
                                 style="display: none; width: 80px; height: 80px; font-size: 2rem;">
                                {{ strtoupper(substr($user->emp_name, 0, 2)) }}
                            </div>
                        </div>
                        <h5 class="mb-1">{{ $user->emp_name }}</h5>
                        <p class="text-muted mb-2">{{ $user->emp_no }}</p>
                        @php
                            $badgeClass = match($user->role) {
                                'ADMIN' => 'bg-danger',
                                'MANAGER' => 'bg-info',
                                'USER' => 'bg-primary',
                                default => 'bg-secondary'
                            };
                            $roleLabel = match($user->role) {
                                'ADMIN' => 'Administrator',
                                'MANAGER' => 'Manager',
                                'USER' => 'User',
                                default => $user->role
                            };
                        @endphp
                        <span class="badge {{ $badgeClass }} px-3 py-2">
                            {{ $roleLabel }}
                        </span>
                    </div>
                    
                    <hr>
                    
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-primary mb-0">{{ $userStats['total_orders'] }}</h4>
                            <small class="text-muted">Total Orders</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-success mb-0">${{ number_format($userStats['total_spent'], 2) }}</h4>
                            <small class="text-muted">Total Spent</small>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">Status</label>
                        <p class="mb-0">
                            <span class="badge {{ $user->emp_verified_at ? 'bg-success' : 'bg-warning' }}">
                                {{ $user->emp_verified_at ? 'Active' : 'Inactive' }}
                            </span>
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">Member Since</label>
                        <p class="mb-0">{{ $user->created_at->format('M d, Y \a\t g:i A') }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label text-muted">Last Updated</label>
                        <p class="mb-0">{{ $user->updated_at->format('M d, Y \a\t g:i A') }}</p>
                    </div>
                    
                    @if($user->position || $user->title_class || $user->rank || $user->hr_job_name || $user->job_assigned)
                    <hr>
                    <h6 class="text-muted mb-3">Employee Information</h6>
                    
                    @if($user->position)
                    <div class="mb-2">
                        <label class="form-label text-muted small">Position</label>
                        <p class="mb-0">{{ $user->position }}</p>
                    </div>
                    @endif
                    
                    @if($user->title_class)
                    <div class="mb-2">
                        <label class="form-label text-muted small">Title Class</label>
                        <p class="mb-0">{{ $user->title_class }}</p>
                    </div>
                    @endif
                    
                    @if($user->rank)
                    <div class="mb-2">
                        <label class="form-label text-muted small">Rank</label>
                        <p class="mb-0">{{ $user->rank }}</p>
                    </div>
                    @endif
                    
                    @if($user->hr_job_name)
                    <div class="mb-2">
                        <label class="form-label text-muted small">HR Job Name</label>
                        <p class="mb-0">{{ $user->hr_job_name }}</p>
                    </div>
                    @endif
                    
                    @if($user->job_assigned)
                    <div class="mb-2">
                        <label class="form-label text-muted small">Job Assigned</label>
                        <p class="mb-0">{{ $user->job_assigned }}</p>
                    </div>
                    @endif
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Activity & Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row g-4 mb-4">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="stats-icon bg-primary bg-opacity-10 text-primary mx-auto mb-2">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <h4 class="mb-0">{{ $userStats['total_orders'] }}</h4>
                                <small class="text-muted">Orders Placed</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="stats-icon bg-success bg-opacity-10 text-success mx-auto mb-2">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <h4 class="mb-0">${{ number_format($userStats['total_spent'], 0) }}</h4>
                                <small class="text-muted">Total Spent</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="stats-icon bg-info bg-opacity-10 text-info mx-auto mb-2">
                                    <i class="fas fa-box"></i>
                                </div>
                                <h4 class="mb-0">{{ $userStats['total_products'] }}</h4>
                                <small class="text-muted">Products Created</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="stats-icon bg-warning bg-opacity-10 text-warning mx-auto mb-2">
                                    <i class="fas fa-star"></i>
                                </div>
                                <h4 class="mb-0">{{ $user->created_at->diffInDays() }}</h4>
                                <small class="text-muted">Days Active</small>
                            </div>
                        </div>
                    </div>
                    
                    @if($userStats['recent_activity']->count() > 0)
                    <h6 class="mb-3">Recent Activity</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Order</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($userStats['recent_activity'] as $order)
                                <tr>
                                    <td>
                                        <strong>{{ $order->order_number }}</strong>
                                        <br><small class="text-muted">{{ $order->total_items }} items</small>
                                    </td>
                                    <td>${{ number_format($order->total_amount, 2) }}</td>
                                    <td>
                                        <span class="badge {{ $order->getStatusBadgeClass() }}">
                                            {{ ucfirst($order->status) }}
                                        </span>
                                    </td>
                                    <td>{{ $order->created_at->format('M d, Y') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @else
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No recent activity</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Admin Actions -->
    @if($user->id !== auth()->id())
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Admin Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="{{ route('management.users.edit', $user) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>Edit User
                        </a>
                        
                        <form action="{{ route('management.users.toggle-status', $user) }}" method="POST" class="d-inline">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="btn btn-{{ $user->emp_verified_at ? 'secondary' : 'success' }}">
                                <i class="fas fa-{{ $user->emp_verified_at ? 'pause' : 'play' }} me-2"></i>
                                {{ $user->emp_verified_at ? 'Deactivate' : 'Activate' }} User
                            </button>
                        </form>
                        
                        <form action="{{ route('management.users.reset-password', $user) }}" 
                              method="POST" 
                              class="d-inline"
                              onsubmit="return confirm('Are you sure you want to reset this user\'s password?')">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-key me-2"></i>Reset Password
                            </button>
                        </form>
                        
                        <form action="{{ route('management.users.destroy', $user) }}" 
                              method="POST" 
                              class="d-inline"
                              onsubmit="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>Delete User
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <style>
        .avatar-circle {
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .stats-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }
    </style>
</x-app-layout>