<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class LotRequestItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'lot_request_id',
        'equipment_number',
        'equipment_code',
        'size',
        'cam_class',
        'insp_type',
        'alloc_type',
        'quantity'
    ];

    protected $casts = [
        'quantity' => 'integer',
    ];

    /**
     * Get the lot request that owns this item
     */
    public function lotRequest()
    {
        return $this->belongsTo(LotRequest::class);
    }

    /**
     * Get the equipment for this item
     */
    public function equipment()
    {
        return $this->belongsTo(Equipment::class, 'equipment_number', 'eqp_no');
    }
}
