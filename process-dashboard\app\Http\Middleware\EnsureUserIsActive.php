<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureUserIsActive
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();
        
        // If user is authenticated but inactive, log them out
        if ($user && is_null($user->emp_verified_at)) {
            Auth::logout();
            
            // If it's an API request, return JSON response
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Your account has been deactivated. Please contact an administrator.'
                ], 401);
            }
            
            // For web requests, redirect to login with message
            return redirect()->route('login')
                           ->with('error', 'Your account has been deactivated. Please contact an administrator.');
        }
        
        return $next($request);
    }
}
