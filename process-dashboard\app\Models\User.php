<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Storage;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The name of the "username" column.
     *
     * @var string
     */
    public function username()
    {
        return 'emp_no';
    }

    /**
     * Get the name of the unique identifier for the user.
     *
     * @return string
     */
    public function getAuthIdentifierName()
    {
        return 'emp_no';
    }

    /**
     * Get the unique identifier for the user.
     *
     * @return mixed
     */
    public function getAuthIdentifier()
    {
        return $this->attributes[$this->getAuthIdentifierName()];
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'emp_no',
        'emp_name',
        'role',
        'position',
        'title_class',
        'rank',
        'hr_job_name',
        'job_assigned',
        'emp_verified_at',
        'password',
        'remember_token',
        'avatar',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'emp_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Check if user is an admin
     *
     * @return bool
     */
    public function isAdmin(): bool
    {
        return $this->role === 'ADMIN';
    }

    /**
     * Check if user is a regular user
     *
     * @return bool
     */
    public function isUser(): bool
    {
        return $this->role === 'USER';
    }

    /**
     * Check if user is a manager
     *
     * @return bool
     */
    public function isManager(): bool
    {
        return $this->role === 'MANAGER';
    }


    /**
     * Check if user has specific role
     *
     * @param string $role
     * @return bool
     */
    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    /**
     * Check if user can create records
     *
     * @return bool
     */
    public function canCreate(): bool
    {
        return $this->isAdmin() || $this->isManager();
    }

    /**
     * Check if user can delete records
     *
     * @return bool
     */
    public function canDelete(): bool
    {
        return $this->isAdmin() || $this->isManager();
    }

    /**
     * Check if user can access management features
     *
     * @return bool
     */
    public function canAccessManagement(): bool
    {
        return $this->isAdmin();
    }

    /**
     * Check if user can access MC Allocation Dashboard
     *
     * @return bool
     */
    public function canAccessMCAllocation(): bool
    {
        return $this->isAdmin() || $this->isManager();
    }

    /**
     * Check if user is active (has been verified by administrator)
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return !is_null($this->emp_verified_at);
    }

    /**
     * Check if user is inactive (not yet verified by administrator)
     *
     * @return bool
     */
    public function isInactive(): bool
    {
        return is_null($this->emp_verified_at);
    }


    /**
     * Get the user's display name.
     */
    public function getDisplayName(): string
    {
        return $this->emp_name ?? $this->emp_no;
    }

    /**
     * Get the user's avatar URL.
     */
    public function getAvatarUrl(): string
    {
        if ($this->avatar && Storage::disk('public')->exists($this->avatar)) {
            return asset('storage/' . $this->avatar);
        }
        
        // Fallback to generated avatar based on emp_name
        return 'https://ui-avatars.com/api/?name=' . urlencode($this->getDisplayName()) . 
               '&background=6366f1&color=fff&size=200&font-size=0.5';
    }

    /**
     * Get the user's small avatar URL (for lists).
     */
    public function getSmallAvatarUrl(): string
    {
        if ($this->avatar && Storage::disk('public')->exists($this->avatar)) {
            return asset('storage/' . $this->avatar);
        }
        
        // Fallback to generated avatar based on emp_name
        return 'https://ui-avatars.com/api/?name=' . urlencode($this->getDisplayName()) . 
               '&background=6366f1&color=fff&size=80&font-size=0.5';
    }
}
