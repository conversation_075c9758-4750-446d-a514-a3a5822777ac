<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;

class SettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:ADMIN');
    }

    /**
     * Display settings dashboard.
     */
    public function index()
    {
        $settings = $this->getApplicationSettings();
        
        return view('management.settings.index', compact('settings'));
    }

    /**
     * Show general settings form.
     */
    public function general()
    {
        $settings = $this->getApplicationSettings();
        
        return view('management.settings.general', compact('settings'));
    }

    /**
     * Update general settings.
     */
    public function updateGeneral(Request $request)
    {
        $request->validate([
            'app_name' => 'required|string|max:255',
            'app_description' => 'nullable|string|max:500',
            'contact_email' => 'required|email',
            'timezone' => 'required|string',
            'unit' => 'required|string|in:PCS,K PCS,M PCS',
        ]);

        $this->updateSettings([
            'app_name' => $request->app_name,
            'app_description' => $request->app_description,
            'contact_email' => $request->contact_email,
            'timezone' => $request->timezone,
            'unit' => $request->unit,
        ]);
        
        // Also update the Laravel config for app name and timezone
        config(['app.name' => $request->app_name]);
        config(['app.timezone' => $request->timezone]);

        return redirect()->route('management.settings.general')
                        ->with('success', 'General settings updated successfully!');
    }

    /**
     * Show email settings form.
     */
    public function email()
    {
        $settings = $this->getApplicationSettings();
        
        return view('management.settings.email', compact('settings'));
    }

    /**
     * Update email settings.
     */
    public function updateEmail(Request $request)
    {
        $request->validate([
            'mail_from_address' => 'required|email',
            'mail_from_name' => 'required|string|max:255',
            'smtp_host' => 'nullable|string',
            'smtp_port' => 'nullable|integer',
            'smtp_username' => 'nullable|string',
            'smtp_password' => 'nullable|string',
            'smtp_encryption' => 'nullable|string',
        ]);

        $this->updateSettings([
            'mail_from_address' => $request->mail_from_address,
            'mail_from_name' => $request->mail_from_name,
            'smtp_host' => $request->smtp_host,
            'smtp_port' => $request->smtp_port,
            'smtp_username' => $request->smtp_username,
            'smtp_password' => $request->smtp_password,
            'smtp_encryption' => $request->smtp_encryption,
        ]);

        return redirect()->route('management.settings.email')
                        ->with('success', 'Email settings updated successfully!');
    }

    /**
     * Show system settings form.
     */
    public function system()
    {
        $settings = $this->getApplicationSettings();
        $systemInfo = $this->getSystemInfo();
        
        return view('management.settings.system', compact('settings', 'systemInfo'));
    }

    /**
     * Update system settings.
     */
    public function updateSystem(Request $request)
    {
        $request->validate([
            'maintenance_mode' => 'boolean',
            'debug_mode' => 'boolean',
            'cache_enabled' => 'boolean',
            'log_level' => 'required|in:emergency,alert,critical,error,warning,notice,info,debug',
            'session_lifetime' => 'required|integer|min:5|max:1440',
            'max_upload_size' => 'required|integer|min:1|max:100',
            'memory_limit' => 'nullable|string|in:128M,256M,512M,1G,2G',
            'max_execution_time' => 'nullable|integer|in:30,60,120,300,600',
            
            // Auto Clear Logs settings
            'auto_clear_logs' => 'boolean',
            'log_clear_trigger_type' => 'nullable|string|in:size,time,both',
            'max_log_size' => 'nullable|integer|min:1|max:1000',
            'log_clear_schedule' => 'nullable|string|in:daily,weekly,monthly',
        ]);

        // Store the settings
        $this->updateSettings([
            'maintenance_mode' => $request->boolean('maintenance_mode'),
            'debug_mode' => $request->boolean('debug_mode'),
            'cache_enabled' => $request->boolean('cache_enabled'),
            'log_level' => $request->log_level,
            'session_lifetime' => $request->session_lifetime,
            'max_upload_size' => $request->max_upload_size,
            'memory_limit' => $request->memory_limit,
            'max_execution_time' => $request->max_execution_time,
            
            // Auto Clear Logs settings
            'auto_clear_logs' => $request->boolean('auto_clear_logs'),
            'log_clear_trigger_type' => $request->log_clear_trigger_type,
            'max_log_size' => $request->max_log_size,
            'log_clear_schedule' => $request->log_clear_schedule,
        ]);
        
        // Apply system changes immediately
        $this->applySystemSettings([
            'maintenance_mode' => $request->boolean('maintenance_mode'),
            'debug_mode' => $request->boolean('debug_mode'),
            'session_lifetime' => $request->session_lifetime,
            'log_level' => $request->log_level,
            'max_upload_size' => $request->max_upload_size,
        ]);

        return redirect()->route('management.settings.system')
                        ->with('success', 'System settings updated successfully!');
    }

    /**
     * Show security settings form.
     */
    public function security()
    {
        $settings = $this->getApplicationSettings();
        
        return view('management.settings.security', compact('settings'));
    }

    /**
     * Update security settings.
     */
    public function updateSecurity(Request $request)
    {
        $request->validate([
            'password_min_length' => 'required|integer|min:6|max:32',
            'password_require_uppercase' => 'boolean',
            'password_require_lowercase' => 'boolean',
            'password_require_numbers' => 'boolean',
            'password_require_symbols' => 'boolean',
            'login_attempts_limit' => 'required|integer|min:3|max:10',
            'lockout_duration' => 'required|integer|min:1|max:60',
            'two_factor_enabled' => 'boolean',
            'two_factor_method_select' => 'nullable|string|in:app,sms,email',
            
            // Session & Access Control
            'session_timeout' => 'nullable|integer|in:15,30,60,120,240,480,1440',
            'max_concurrent_sessions' => 'nullable|string',
            'force_logout_on_password_change' => 'boolean',
            'remember_me_enabled' => 'boolean',
            
            // IP & Access Restrictions
            'ip_whitelist_enabled' => 'boolean',
            'allowed_ips' => 'nullable|string',
            'security_notifications' => 'boolean',
        ]);

        $this->updateSettings([
            'password_min_length' => $request->password_min_length,
            'password_require_uppercase' => $request->boolean('password_require_uppercase'),
            'password_require_lowercase' => $request->boolean('password_require_lowercase'),
            'password_require_numbers' => $request->boolean('password_require_numbers'),
            'password_require_symbols' => $request->boolean('password_require_symbols'),
            'login_attempts_limit' => $request->login_attempts_limit,
            'lockout_duration' => $request->lockout_duration,
            'two_factor_enabled' => $request->boolean('two_factor_enabled'),
            'two_factor_method' => $request->two_factor_method_select,
            
            // Session & Access Control
            'session_timeout' => $request->session_timeout,
            'max_concurrent_sessions' => $request->max_concurrent_sessions,
            'force_logout_on_password_change' => $request->boolean('force_logout_on_password_change'),
            'remember_me_enabled' => $request->boolean('remember_me_enabled'),
            
            // IP & Access Restrictions
            'ip_whitelist_enabled' => $request->boolean('ip_whitelist_enabled'),
            'allowed_ips' => $request->allowed_ips,
            'security_notifications' => $request->boolean('security_notifications'),
        ]);

        return redirect()->route('management.settings.security')
                        ->with('success', 'Security settings updated successfully!');
    }

    /**
     * Clear application cache.
     */
    public function clearCache()
    {
        try {
            Artisan::call('cache:clear');
            Artisan::call('config:clear');
            Artisan::call('view:clear');
            Artisan::call('route:clear');

            return redirect()->back()
                           ->with('success', 'Application cache cleared successfully!');
        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'Failed to clear cache: ' . $e->getMessage());
        }
    }

    /**
     * Optimize application.
     */
    public function optimize()
    {
        try {
            Artisan::call('config:cache');
            Artisan::call('route:cache');
            Artisan::call('view:cache');

            return redirect()->back()
                           ->with('success', 'Application optimized successfully!');
        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'Failed to optimize application: ' . $e->getMessage());
        }
    }

    /**
     * Download application logs.
     */
    public function downloadLogs()
    {
        $logPath = storage_path('logs/laravel.log');
        
        if (!file_exists($logPath)) {
            return redirect()->back()
                           ->with('error', 'Log file not found.');
        }

        return response()->download($logPath, 'application_logs_' . date('Y-m-d') . '.log');
    }

    /**
     * Clear application logs.
     */
    public function clearLogs()
    {
        $logPath = storage_path('logs/laravel.log');
        
        if (file_exists($logPath)) {
            file_put_contents($logPath, '');
            return redirect()->back()
                           ->with('success', 'Application logs cleared successfully!');
        }

        return redirect()->back()
                       ->with('error', 'Log file not found.');
    }

    /**
     * Get application settings.
     */
    private function getApplicationSettings()
    {
        return [
            // General Settings - Only the 5 simplified fields
            'app_name' => config('app.name', 'Process Dashboard'),
            'app_description' => $this->getSetting('app_description', 'Business Process Management Dashboard'),
            'contact_email' => $this->getSetting('contact_email', '<EMAIL>'),
            'timezone' => config('app.timezone', 'Asia/Tokyo'),
            'unit' => $this->getSetting('unit', 'PCS'),

            // Email Settings
            'mail_from_address' => config('mail.from.address', '<EMAIL>'),
            'mail_from_name' => config('mail.from.name', 'Process Dashboard'),
            'smtp_host' => config('mail.mailers.smtp.host', ''),
            'smtp_port' => config('mail.mailers.smtp.port', 587),
            'smtp_username' => config('mail.mailers.smtp.username', ''),
            'smtp_password' => config('mail.mailers.smtp.password', ''),
            'smtp_encryption' => config('mail.mailers.smtp.encryption', 'tls'),

            // System Settings
            'maintenance_mode' => $this->getSetting('maintenance_mode', false),
            'debug_mode' => config('app.debug', false),
            'cache_enabled' => $this->getSetting('cache_enabled', true),
            'log_level' => config('logging.channels.single.level', 'info'),
            'session_lifetime' => config('session.lifetime', 120),
            'max_upload_size' => $this->getSetting('max_upload_size', 2),
            'memory_limit' => $this->getSetting('memory_limit', '256M'),
            'max_execution_time' => $this->getSetting('max_execution_time', 60),
            
            // Auto Clear Logs settings
            'auto_clear_logs' => $this->getSetting('auto_clear_logs', false),
            'log_clear_trigger_type' => $this->getSetting('log_clear_trigger_type', 'size'),
            'max_log_size' => $this->getSetting('max_log_size', 10),
            'log_clear_schedule' => $this->getSetting('log_clear_schedule', 'weekly'),

            // Security Settings
            'password_min_length' => $this->getSetting('password_min_length', 8),
            'password_require_uppercase' => $this->getSetting('password_require_uppercase', false),
            'password_require_lowercase' => $this->getSetting('password_require_lowercase', false),
            'password_require_numbers' => $this->getSetting('password_require_numbers', false),
            'password_require_symbols' => $this->getSetting('password_require_symbols', false),
            'login_attempts_limit' => $this->getSetting('login_attempts_limit', 5),
            'lockout_duration' => $this->getSetting('lockout_duration', 5),
            'two_factor_enabled' => $this->getSetting('two_factor_enabled', false),
            'two_factor_method' => $this->getSetting('two_factor_method', 'app'),
            
            // Session & Access Control
            'session_timeout' => $this->getSetting('session_timeout', 120),
            'max_concurrent_sessions' => $this->getSetting('max_concurrent_sessions', 3),
            'force_logout_on_password_change' => $this->getSetting('force_logout_on_password_change', false),
            'remember_me_enabled' => $this->getSetting('remember_me_enabled', true),
            
            // IP & Access Restrictions
            'ip_whitelist_enabled' => $this->getSetting('ip_whitelist_enabled', false),
            'allowed_ips' => $this->getSetting('allowed_ips', ''),
            'security_notifications' => $this->getSetting('security_notifications', true),
        ];
    }

    /**
     * Get system information.
     */
    private function getSystemInfo()
    {
        return [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'disk_space' => $this->getDiskSpace(),
            'log_file_size' => $this->getLogFileSize(),
            'cache_size' => $this->getCacheSize(),
        ];
    }

    /**
     * Get a setting value.
     */
    private function getSetting($key, $default = null)
    {
        $settings = Cache::get('app_settings', []);
        
        // If not in cache, try to load from file
        if (empty($settings) && Storage::disk('local')->exists('settings.json')) {
            try {
                $settingsJson = Storage::disk('local')->get('settings.json');
                $settings = json_decode($settingsJson, true) ?? [];
                // Cache the loaded settings
                Cache::put('app_settings', $settings, now()->addDays(30));
            } catch (\Exception $e) {
                $settings = [];
            }
        }
        
        return $settings[$key] ?? $default;
    }

    /**
     * Update settings.
     */
    private function updateSettings($newSettings)
    {
        $settings = Cache::get('app_settings', []);
        $settings = array_merge($settings, $newSettings);
        
        // Cache the settings
        Cache::put('app_settings', $settings, now()->addDays(30));
        
        // Save to file for persistence
        Storage::disk('local')->put('settings.json', json_encode($settings, JSON_PRETTY_PRINT));
        
        // Update environment file for app name if changed
        if (isset($newSettings['app_name'])) {
            $this->updateEnvFile('APP_NAME', '"' . $newSettings['app_name'] . '"');
        }
        
        // Update environment file for timezone if changed
        if (isset($newSettings['timezone'])) {
            $this->updateEnvFile('APP_TIMEZONE', $newSettings['timezone']);
        }
    }
    
    /**
     * Update environment file with new value.
     */
    private function updateEnvFile($key, $value)
    {
        $envPath = base_path('.env');
        
        if (file_exists($envPath)) {
            $envContent = file_get_contents($envPath);
            
            // Check if key exists
            if (preg_match("/^{$key}=.*/m", $envContent)) {
                // Update existing key
                $envContent = preg_replace("/^{$key}=.*/m", "{$key}={$value}", $envContent);
            } else {
                // Add new key
                $envContent .= "\n{$key}={$value}";
            }
            
            file_put_contents($envPath, $envContent);
            
            // Clear config cache to reload settings
            try {
                Artisan::call('config:clear');
            } catch (\Exception $e) {
                // Silently handle if artisan command fails
            }
        }
    }

    /**
     * Get disk space information.
     */
    private function getDiskSpace()
    {
        $bytes = disk_free_space('/');
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get log file size.
     */
    private function getLogFileSize()
    {
        $logPath = storage_path('logs/laravel.log');
        
        if (!file_exists($logPath)) {
            return '0 B';
        }
        
        $bytes = filesize($logPath);
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get cache size.
     */
    private function getCacheSize()
    {
        try {
            $size = 0;
            $files = Storage::disk('local')->allFiles('framework/cache');
            
            foreach ($files as $file) {
                $size += Storage::disk('local')->size($file);
            }
            
            $units = ['B', 'KB', 'MB', 'GB'];
            
            for ($i = 0; $size > 1024; $i++) {
                $size /= 1024;
            }
            
            return round($size, 2) . ' ' . $units[$i];
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }
    
    /**
     * Apply system settings changes to the application.
     */
    private function applySystemSettings($settings)
    {
        // Handle Maintenance Mode
        if (isset($settings['maintenance_mode'])) {
            if ($settings['maintenance_mode']) {
                $this->enableMaintenanceMode();
            } else {
                $this->disableMaintenanceMode();
            }
        }
        
        // Handle Debug Mode - Update .env file
        if (isset($settings['debug_mode'])) {
            $this->updateEnvFile('APP_DEBUG', $settings['debug_mode'] ? 'true' : 'false');
        }
        
        // Handle Session Lifetime
        if (isset($settings['session_lifetime'])) {
            $this->updateEnvFile('SESSION_LIFETIME', $settings['session_lifetime']);
            config(['session.lifetime' => $settings['session_lifetime']]);
        }
        
        // Handle Log Level
        if (isset($settings['log_level'])) {
            $this->updateEnvFile('LOG_LEVEL', $settings['log_level']);
        }
        
        // Handle Max Upload Size - Update .env and php.ini settings
        if (isset($settings['max_upload_size'])) {
            $uploadSize = $settings['max_upload_size'] . 'M';
            $this->updateEnvFile('MAX_UPLOAD_SIZE', $settings['max_upload_size']);
            
            // Set PHP runtime settings
            ini_set('upload_max_filesize', $uploadSize);
            ini_set('post_max_size', $uploadSize);
        }
        
        // Handle Memory Limit
        if (isset($settings['memory_limit'])) {
            ini_set('memory_limit', $settings['memory_limit']);
            $this->updateEnvFile('MEMORY_LIMIT', $settings['memory_limit']);
        }
        
        // Handle Max Execution Time
        if (isset($settings['max_execution_time'])) {
            ini_set('max_execution_time', $settings['max_execution_time']);
            $this->updateEnvFile('MAX_EXECUTION_TIME', $settings['max_execution_time']);
        }
    }
    
    /**
     * Enable maintenance mode.
     */
    private function enableMaintenanceMode()
    {
        try {
            Artisan::call('down', [
                '--secret' => 'admin-access',
                '--render' => 'errors::503'
            ]);
        } catch (\Exception $e) {
            // Log error but don't break the process
            \Log::warning('Failed to enable maintenance mode: ' . $e->getMessage());
        }
    }
    
    /**
     * Disable maintenance mode.
     */
    private function disableMaintenanceMode()
    {
        try {
            Artisan::call('up');
        } catch (\Exception $e) {
            // Log error but don't break the process
            \Log::warning('Failed to disable maintenance mode: ' . $e->getMessage());
        }
    }
    
    /**
     * Check and perform auto clear logs if needed.
     */
    public function autoCheckClearLogs()
    {
        $settings = $this->getApplicationSettings();
        
        if (!($settings['auto_clear_logs'] ?? false)) {
            return; // Auto clear is disabled
        }
        
        $shouldClear = false;
        $triggerType = $settings['log_clear_trigger_type'] ?? 'size';
        
        // Check size-based trigger
        if (in_array($triggerType, ['size', 'both'])) {
            $maxSize = ($settings['max_log_size'] ?? 10) * 1024 * 1024; // Convert MB to bytes
            $logSize = $this->getLogFileSizeBytes();
            
            if ($logSize > $maxSize) {
                $shouldClear = true;
                \Log::info("Auto clear logs triggered by size: {$logSize} bytes > {$maxSize} bytes");
            }
        }
        
        // Check time-based trigger
        if (in_array($triggerType, ['time', 'both']) && !$shouldClear) {
            $lastClear = Cache::get('last_auto_log_clear', 0);
            $schedule = $settings['log_clear_schedule'] ?? 'weekly';
            
            $intervals = [
                'daily' => 24 * 60 * 60,      // 24 hours
                'weekly' => 7 * 24 * 60 * 60, // 7 days  
                'monthly' => 30 * 24 * 60 * 60 // 30 days
            ];
            
            $interval = $intervals[$schedule] ?? $intervals['weekly'];
            
            if (time() - $lastClear > $interval) {
                $shouldClear = true;
                \Log::info("Auto clear logs triggered by schedule: {$schedule}");
            }
        }
        
        if ($shouldClear) {
            $this->performAutoClearLogs();
        }
    }
    
    /**
     * Perform the actual auto clear logs operation.
     */
    private function performAutoClearLogs()
    {
        try {
            $logPath = storage_path('logs/laravel.log');
            
            if (file_exists($logPath)) {
                // Backup current log before clearing
                $backupPath = storage_path('logs/laravel_backup_' . date('Y_m_d_H_i_s') . '.log');
                copy($logPath, $backupPath);
                
                // Clear the main log file
                file_put_contents($logPath, '');
                
                // Update last clear timestamp
                Cache::put('last_auto_log_clear', time());
                
                \Log::info('Auto clear logs completed successfully. Backup created: ' . basename($backupPath));
                
                // Clean up old backups (keep only last 5)
                $this->cleanupLogBackups();
            }
        } catch (\Exception $e) {
            \Log::error('Auto clear logs failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Get log file size in bytes.
     */
    private function getLogFileSizeBytes()
    {
        $logPath = storage_path('logs/laravel.log');
        return file_exists($logPath) ? filesize($logPath) : 0;
    }
    
    /**
     * Clean up old log backups, keeping only the last 5.
     */
    private function cleanupLogBackups()
    {
        try {
            $logsDir = storage_path('logs');
            $backupFiles = glob($logsDir . '/laravel_backup_*.log');
            
            if (count($backupFiles) > 5) {
                // Sort by modification time (oldest first)
                usort($backupFiles, function($a, $b) {
                    return filemtime($a) - filemtime($b);
                });
                
                // Delete oldest files, keep newest 5
                $filesToDelete = array_slice($backupFiles, 0, -5);
                foreach ($filesToDelete as $file) {
                    unlink($file);
                }
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to cleanup log backups: ' . $e->getMessage());
        }
    }
    
    /**
     * Logout all users except current admin.
     */
    public function logoutAllUsers(Request $request)
    {
        try {
            $currentSessionId = session()->getId();
            
            // Clear all sessions from database except current
            \DB::table('sessions')
                ->where('id', '!=', $currentSessionId)
                ->delete();
                
            // Clear Laravel auth sessions
            \Artisan::call('auth:clear-resets');
            
            // Log security action
            \Log::info('All user sessions terminated by admin', [
                'admin_id' => auth()->id(),
                'admin_ip' => $request->ip(),
                'timestamp' => now()
            ]);
            
            return response()->json([
                'success' => true,
                'message' => 'All user sessions have been terminated successfully.'
            ]);
            
        } catch (\Exception $e) {
            \Log::error('Failed to logout all users: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to logout all users: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Show active sessions page.
     */
    public function showActiveSessions()
    {
        $activeSessions = \DB::table('sessions')
            ->leftJoin('users', 'sessions.user_id', '=', 'users.id')
            ->select(
                'sessions.id',
                'sessions.user_id',
                'sessions.ip_address',
                'sessions.user_agent',
                'sessions.last_activity',
                'users.name',
                'users.email'
            )
            ->where('sessions.last_activity', '>', now()->subMinutes(30)->timestamp)
            ->orderBy('sessions.last_activity', 'desc')
            ->get()
            ->map(function ($session) {
                $session->last_activity_human = \Carbon\Carbon::createFromTimestamp($session->last_activity)->diffForHumans();
                $session->is_current = $session->id === session()->getId();
                return $session;
            });
            
        return view('management.settings.security.active-sessions', compact('activeSessions'));
    }
    
    /**
     * Show security log page.
     */
    public function showSecurityLog()
    {
        // Get security-related log entries from Laravel log
        $logPath = storage_path('logs/laravel.log');
        $securityLogs = [];
        
        if (file_exists($logPath)) {
            $logContent = file_get_contents($logPath);
            $lines = array_reverse(explode("\n", $logContent));
            
            foreach ($lines as $line) {
                if (stripos($line, 'login') !== false || 
                    stripos($line, 'logout') !== false || 
                    stripos($line, 'failed') !== false ||
                    stripos($line, 'security') !== false ||
                    stripos($line, 'auth') !== false) {
                    $securityLogs[] = $line;
                    if (count($securityLogs) >= 100) break; // Limit to last 100 entries
                }
            }
        }
        
        return view('management.settings.security.security-log', compact('securityLogs'));
    }
    
    /**
     * Validate IP address against whitelist.
     */
    public function validateIpAccess($ip)
    {
        $settings = $this->getApplicationSettings();
        
        if (!($settings['ip_whitelist_enabled'] ?? false)) {
            return true; // IP whitelist disabled
        }
        
        $allowedIps = $settings['allowed_ips'] ?? '';
        if (empty($allowedIps)) {
            return true; // No IPs configured
        }
        
        $ipList = array_filter(array_map('trim', explode("\n", $allowedIps)));
        
        foreach ($ipList as $allowedIp) {
            if ($this->ipMatches($ip, $allowedIp)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if IP matches pattern (supports CIDR).
     */
    private function ipMatches($ip, $pattern)
    {
        if ($ip === $pattern) {
            return true;
        }
        
        if (strpos($pattern, '/') !== false) {
            // CIDR notation
            list($subnet, $mask) = explode('/', $pattern);
            $ip_long = ip2long($ip);
            $subnet_long = ip2long($subnet);
            $mask = ~((1 << (32 - $mask)) - 1);
            
            return ($ip_long & $mask) === ($subnet_long & $mask);
        }
        
        return false;
    }
}
