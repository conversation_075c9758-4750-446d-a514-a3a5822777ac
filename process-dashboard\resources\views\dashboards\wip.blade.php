<x-app-layout>
    <x-slot name="header">
        {{ $title ?? 'WIP Analytics Dashboard' }}
    </x-slot>

    <div class="container-fluid px-4 py-3">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="fw-bold text-dark mb-1">
                            <i class="fas fa-chart-area text-primary me-2"></i>
                            WIP Analytics Dashboard
                        </h3>
                        <p class="text-muted mb-0">Comprehensive work-in-progress analytics and insights</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-secondary btn-sm" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="exportData()">
                            <i class="fas fa-download me-1"></i>Export
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="card-body text-white">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="text-white-50 mb-2">Total WIP Lots</h6>
                                <h3 class="fw-bold mb-0">{{ number_format($summary['total_lots'] ?? 1234) }}</h3>
                                <small class="text-white-75">
                                    <i class="fas fa-arrow-up me-1"></i>+5.2% from last week
                                </small>
                            </div>
                            <div class="bg-white bg-opacity-20 rounded-circle p-3">
                                <i class="fas fa-boxes fa-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                    <div class="card-body text-white">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="text-white-50 mb-2">Total Quantity</h6>
                                <h3 class="fw-bold mb-0">{{ number_format($summary['total_quantity'] ?? 2456789) }}</h3>
                                <small class="text-white-75">
                                    <i class="fas fa-arrow-up me-1"></i>+3.1% from last week
                                </small>
                            </div>
                            <div class="bg-white bg-opacity-20 rounded-circle p-3">
                                <i class="fas fa-cubes fa-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);">
                    <div class="card-body text-white">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="text-white-50 mb-2">Average TAT</h6>
                                <h3 class="fw-bold mb-0">{{ number_format($summary['avg_tat'] ?? 15.7, 1) }}<small class="fs-6 ms-1">days</small></h3>
                                <small class="text-white-75">
                                    <i class="fas fa-arrow-down me-1"></i>-2.3% from last week
                                </small>
                            </div>
                            <div class="bg-white bg-opacity-20 rounded-circle p-3">
                                <i class="fas fa-clock fa-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);">
                    <div class="card-body text-dark">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="text-muted mb-2">Critical Lots</h6>
                                <h3 class="fw-bold mb-0 text-danger">{{ number_format($summary['critical_lots'] ?? 45) }}</h3>
                                <small class="text-muted">
                                    <i class="fas fa-exclamation-triangle me-1"></i>>30 days TAT
                                </small>
                            </div>
                            <div class="bg-danger bg-opacity-20 rounded-circle p-3">
                                <i class="fas fa-exclamation-triangle fa-lg text-danger"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Filter and Navigation Row -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center" 
                         style="cursor: pointer; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none;" 
                         onclick="toggleQuickFilters()">
                        <h6 class="mb-0 text-white">
                            <i class="fas fa-filter me-2"></i>
                            <span class="fw-semibold">Quick Filters</span>
                            <small class="ms-2 text-white-75">(Click to toggle)</small>
                        </h6>
                        <i class="fas fa-chevron-up text-white" id="quick-filter-toggle-icon"></i>
                    </div>
                    <div class="card-body" id="quick-filter-section">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label fw-semibold">Work Type</label>
                                <select class="form-select form-select-sm" id="work_type_filter" onchange="applyQuickFilters()">
                                    <option value="">All Types</option>
                                    <option value="NOR">NOR (Normal)</option>
                                    <option value="HOT">HOT (Priority)</option>
                                    <option value="SUP">SUP (Super Hot)</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-semibold">TAT Range</label>
                                <select class="form-select form-select-sm" id="tat_range_filter" onchange="applyQuickFilters()">
                                    <option value="">All Ranges</option>
                                    <option value="0-15">0-15 days</option>
                                    <option value="16-30">16-30 days</option>
                                    <option value="31-60">31-60 days</option>
                                    <option value="60+">60+ days</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-semibold">Lot Size</label>
                                <select class="form-select form-select-sm" id="lot_size_filter" onchange="applyQuickFilters()">
                                    <option value="">All Sizes</option>
                                    <option value="SM">Small (SM)</option>
                                    <option value="MD">Medium (MD)</option>
                                    <option value="LG">Large (LG)</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button class="btn btn-outline-secondary btn-sm me-2" onclick="clearQuickFilters()">
                                    <i class="fas fa-times me-1"></i>Clear
                                </button>
                                <a href="{{ route('updatewip.index') }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-table me-1"></i>Detailed View
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <h6 class="fw-semibold mb-3">
                            <i class="fas fa-link text-primary me-2"></i>Quick Navigation
                        </h6>
                        <div class="d-grid gap-2">
                            <a href="{{ route('updatewip.index') }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-list me-1"></i>WIP Management
                            </a>
                            @if(Auth::user()->isAdmin())
                            <a href="{{ route('updatewip.create') }}" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-sync-alt me-1"></i>Update WIP Data
                            </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header">
                        <h6 class="fw-semibold mb-0">
                            <i class="fas fa-chart-line text-primary me-2"></i>WIP Trend Analysis
                        </h6>
                    </div>
                    <div class="card-body">
                        <canvas id="wipTrendChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header">
                        <h6 class="fw-semibold mb-0">
                            <i class="fas fa-chart-pie text-success me-2"></i>Work Type Distribution
                        </h6>
                    </div>
                    <div class="card-body">
                        <canvas id="workTypeChart" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Secondary Charts Row -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header">
                        <h6 class="fw-semibold mb-0">
                            <i class="fas fa-chart-bar text-warning me-2"></i>TAT Distribution by Range
                        </h6>
                    </div>
                    <div class="card-body">
                        <canvas id="tatDistributionChart" height="280"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header">
                        <h6 class="fw-semibold mb-0">
                            <i class="fas fa-chart-area text-info me-2"></i>Equipment Type Performance
                        </h6>
                    </div>
                    <div class="card-body">
                        <canvas id="equipmentChart" height="280"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities and Top WIP Groups -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="fw-semibold mb-0">
                            <i class="fas fa-clock text-primary me-2"></i>Recent WIP Updates
                        </h6>
                        <span class="badge bg-primary">{{ count($recentUpdates ?? []) }}</span>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            @forelse($recentUpdates ?? [
                                ['time' => '2 hours ago', 'action' => 'WIP data refreshed', 'user' => 'System', 'details' => '1,234 lots processed'],
                                ['time' => '5 hours ago', 'action' => 'Critical lots identified', 'user' => 'Auto-Alert', 'details' => '45 lots >30 days TAT'],
                                ['time' => '8 hours ago', 'action' => 'Manual update', 'user' => 'Admin User', 'details' => 'Excel import completed'],
                                ['time' => '1 day ago', 'action' => 'Bottleneck detected', 'user' => 'Analytics', 'details' => 'EQP01 station delay'],
                                ['time' => '2 days ago', 'action' => 'Performance report', 'user' => 'System', 'details' => 'Weekly summary generated']
                            ] as $update)
                            <div class="list-group-item border-0 py-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="fw-semibold mb-1">{{ $update['action'] }}</h6>
                                        <p class="text-muted mb-1 small">{{ $update['details'] }}</p>
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>{{ $update['user'] }}
                                        </small>
                                    </div>
                                    <small class="text-muted">{{ $update['time'] }}</small>
                                </div>
                            </div>
                            @empty
                            <div class="text-center py-4">
                                <i class="fas fa-clock fa-2x text-muted mb-2"></i>
                                <p class="text-muted">No recent updates</p>
                            </div>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="fw-semibold mb-0">
                            <i class="fas fa-trophy text-warning me-2"></i>Top WIP Groups by Volume
                        </h6>
                        <a href="{{ route('updatewip.index') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>View All
                        </a>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th class="border-0">Rank</th>
                                        <th class="border-0">Group</th>
                                        <th class="border-0">Lots</th>
                                        <th class="border-0">Quantity</th>
                                        <th class="border-0">Avg TAT</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($topGroups ?? [
                                        ['rank' => 1, 'group' => 'LG-A001-TEST-EQP01-NOR', 'lots' => 156, 'quantity' => 234567, 'tat' => 12.5],
                                        ['rank' => 2, 'group' => 'MD-B002-INSP-EQP02-HOT', 'lots' => 134, 'quantity' => 198765, 'tat' => 18.3],
                                        ['rank' => 3, 'group' => 'SM-C003-BURN-EQP03-NOR', 'lots' => 112, 'quantity' => 145632, 'tat' => 9.7],
                                        ['rank' => 4, 'group' => 'LG-D004-TEST-EQP01-SUP', 'lots' => 98, 'quantity' => 178934, 'tat' => 25.1],
                                        ['rank' => 5, 'group' => 'MD-E005-INSP-EQP04-NOR', 'lots' => 87, 'quantity' => 123456, 'tat' => 14.6]
                                    ] as $group)
                                    <tr>
                                        <td>
                                            @if($group['rank'] <= 3)
                                                <span class="badge bg-warning text-dark fw-bold">#{{ $group['rank'] }}</span>
                                            @else
                                                <span class="badge bg-light text-dark">#{{ $group['rank'] }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <code class="text-primary small">{{ Str::limit($group['group'], 20) }}</code>
                                        </td>
                                        <td><span class="badge bg-info">{{ number_format($group['lots']) }}</span></td>
                                        <td><strong>{{ number_format($group['quantity']) }}</strong></td>
                                        <td>
                                            <span class="badge {{ $group['tat'] > 30 ? 'bg-danger' : ($group['tat'] > 15 ? 'bg-warning text-dark' : 'bg-success') }}">
                                                {{ number_format($group['tat'], 1) }}d
                                            </span>
                                        </td>
                                    </tr>
                                    @empty
                                    <tr>
                                        <td colspan="5" class="text-center py-4">
                                            <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                            <p class="text-muted mb-0">No WIP groups available</p>
                                        </td>
                                    </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart.js and Custom Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Dashboard data from backend
        const dashboardData = {
            wipTrend: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
                lots: [1200, 1350, 1180, 1420, 1280, 1380, 1234],
                quantity: [2100000, 2250000, 2180000, 2420000, 2280000, 2380000, 2456789],
                avgTat: [18.5, 17.2, 19.1, 16.8, 17.5, 16.2, 15.7]
            },
            workTypes: {
                labels: ['NOR (Normal)', 'HOT (Priority)', 'SUP (Super Hot)'],
                data: [75, 20, 5],
                colors: ['#28a745', '#ffc107', '#dc3545']
            },
            tatDistribution: {
                labels: ['0-15 days', '16-30 days', '31-60 days', '60+ days'],
                data: [65, 25, 8, 2],
                colors: ['#28a745', '#ffc107', '#fd7e14', '#dc3545']
            },
            equipmentTypes: {
                labels: ['TEST Equipment', 'INSP Equipment', 'BURN Equipment', 'PACK Equipment'],
                avgTat: [14.5, 18.2, 12.8, 16.9],
                throughput: [85, 72, 91, 78]
            }
        };

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeWipTrendChart();
            initializeWorkTypeChart();
            initializeTatDistributionChart();
            initializeEquipmentChart();
        });

        function initializeWipTrendChart() {
            const ctx = document.getElementById('wipTrendChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dashboardData.wipTrend.labels,
                    datasets: [{
                        label: 'Total Lots',
                        data: dashboardData.wipTrend.lots,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y'
                    }, {
                        label: 'Average TAT (days)',
                        data: dashboardData.wipTrend.avgTat,
                        borderColor: '#f093fb',
                        backgroundColor: 'rgba(240, 147, 251, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'Month'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Number of Lots'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Average TAT (days)'
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    }
                }
            });
        }

        function initializeWorkTypeChart() {
            const ctx = document.getElementById('workTypeChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: dashboardData.workTypes.labels,
                    datasets: [{
                        data: dashboardData.workTypes.data,
                        backgroundColor: dashboardData.workTypes.colors,
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function initializeTatDistributionChart() {
            const ctx = document.getElementById('tatDistributionChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: dashboardData.tatDistribution.labels,
                    datasets: [{
                        label: 'Percentage of Lots',
                        data: dashboardData.tatDistribution.data,
                        backgroundColor: dashboardData.tatDistribution.colors,
                        borderWidth: 1,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Percentage (%)'
                            }
                        }
                    }
                }
            });
        }

        function initializeEquipmentChart() {
            const ctx = document.getElementById('equipmentChart').getContext('2d');
            new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: dashboardData.equipmentTypes.labels,
                    datasets: [{
                        label: 'Average TAT (days)',
                        data: dashboardData.equipmentTypes.avgTat,
                        borderColor: '#ff6384',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        pointBackgroundColor: '#ff6384'
                    }, {
                        label: 'Throughput Efficiency (%)',
                        data: dashboardData.equipmentTypes.throughput,
                        borderColor: '#36a2eb',
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        pointBackgroundColor: '#36a2eb'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        r: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Interactive functions
        function toggleQuickFilters() {
            const filterSection = document.getElementById('quick-filter-section');
            const toggleIcon = document.getElementById('quick-filter-toggle-icon');
            
            if (filterSection.style.display === 'none') {
                filterSection.style.display = 'block';
                toggleIcon.className = 'fas fa-chevron-up text-white';
            } else {
                filterSection.style.display = 'none';
                toggleIcon.className = 'fas fa-chevron-down text-white';
            }
        }

        function applyQuickFilters() {
            const workType = document.getElementById('work_type_filter').value;
            const tatRange = document.getElementById('tat_range_filter').value;
            const lotSize = document.getElementById('lot_size_filter').value;
            
            // Build filter parameters
            const params = new URLSearchParams();
            if (workType) params.append('work_type', workType);
            if (tatRange) params.append('tat_range', tatRange);
            if (lotSize) params.append('lot_size', lotSize);
            
            // Apply filters (in a real implementation, this would update charts and data)
            console.log('Applying filters:', { workType, tatRange, lotSize });
            
            // Show loading indicator
            showLoadingIndicator();
            
            // Simulate data refresh
            setTimeout(() => {
                hideLoadingIndicator();
                showSuccessMessage('Filters applied successfully');
            }, 1000);
        }

        function clearQuickFilters() {
            document.getElementById('work_type_filter').value = '';
            document.getElementById('tat_range_filter').value = '';
            document.getElementById('lot_size_filter').value = '';
            applyQuickFilters();
        }

        function refreshDashboard() {
            showLoadingIndicator();
            
            // Simulate refresh
            setTimeout(() => {
                location.reload();
            }, 1000);
        }

        function exportData() {
            showSuccessMessage('Export functionality will be implemented');
        }

        function showLoadingIndicator() {
            // Create or show loading overlay
            let loader = document.getElementById('dashboard-loader');
            if (!loader) {
                loader = document.createElement('div');
                loader.id = 'dashboard-loader';
                loader.innerHTML = `
                    <div class="position-fixed top-0 start-0 w-100 h-100" style="z-index: 9999; background: rgba(255,255,255,0.8);">
                        <div class="d-flex justify-content-center align-items-center h-100">
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-2" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <div class="text-muted">Updating dashboard...</div>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(loader);
            } else {
                loader.style.display = 'block';
            }
        }

        function hideLoadingIndicator() {
            const loader = document.getElementById('dashboard-loader');
            if (loader) {
                loader.style.display = 'none';
            }
        }

        function showSuccessMessage(message) {
            // Create success toast
            const toast = document.createElement('div');
            toast.className = 'position-fixed top-0 end-0 m-3';
            toast.style.zIndex = '10000';
            toast.innerHTML = `
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
            
            document.body.appendChild(toast);
            
            // Auto-remove after 3 seconds
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }
    </script>
</x-app-layout>
