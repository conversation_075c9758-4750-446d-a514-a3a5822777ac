<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EqpSpeedRef extends Model
{
    use HasFactory;

    protected $table = 'eqp_speed_ref';

    protected $fillable = [
        'eqp_type',
        'size',
        'eqp_speed',
    ];

    protected $casts = [
        'eqp_speed' => 'integer',
    ];

    /**
     * Get equipment speed reference by equipment type and size
     */
    public static function getByEqpTypeAndSize($eqpType, $size)
    {
        return self::where('eqp_type', $eqpType)
                   ->where('size', $size)
                   ->first();
    }

    /**
     * Get all unique equipment types
     */
    public static function getUniqueEqpTypes()
    {
        return self::distinct()->pluck('eqp_type')->sort();
    }

    /**
     * Get all unique sizes
     */
    public static function getUniqueSizes()
    {
        return self::distinct()->pluck('size')->sort();
    }

    /**
     * Get all reference combinations
     */
    public static function getAllCombinations()
    {
        return self::select('eqp_type', 'size', 'eqp_speed')
                   ->orderBy('eqp_type')
                   ->orderBy('size')
                   ->get();
    }
}