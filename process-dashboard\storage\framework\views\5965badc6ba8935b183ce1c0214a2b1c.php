<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex justify-content-between align-items-center flex-wrap gap-3 w-100">
            <!-- Title Section -->
            <div class="d-flex align-items-center">
                <h4 class="fw-bold text-dark mb-0 me-3">
                    <i class="fas fa-chart-pie text-primary me-2"></i>
                    Machine Allocation
                </h4>
            </div>
            
            <!-- Main Controls Row -->
            <div class="d-flex align-items-center gap-2 flex-wrap">
                <!-- Quick Filters -->
                <div class="d-flex align-items-center gap-2">
                    <select class="form-select form-select-sm" id="maker_filter_main" onchange="applyQuickFilters()" style="min-width: 130px;">
                        <option value="" selected>ALL Makers</option>
                        <?php if(isset($equipmentData['filter_options']['makers'])): ?>
                            <?php $__currentLoopData = $equipmentData['filter_options']['makers']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $maker): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($maker); ?>"><?php echo e($maker); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </select>
                    
                    <select class="form-select form-select-sm" id="alloc_filter_main" onchange="applyQuickFilters()" style="min-width: 150px;">
                        <option value="" selected>ALL Allocation</option>
                        <?php if(isset($equipmentData['filter_options']['alloc_types'])): ?>
                            <?php $__currentLoopData = $equipmentData['filter_options']['alloc_types']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $allocType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($allocType); ?>"><?php echo e($allocType); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </select>
                    
                    <select class="form-select form-select-sm" id="status_filter_main" onchange="applyQuickFilters()" style="min-width: 150px;">
                        <option value="OPERATIONAL" selected>OPERATIONAL</option>
                        <option value="">ALL Status</option>
                        <?php if(isset($equipmentData['filter_options']['statuses'])): ?>
                            <?php $__currentLoopData = $equipmentData['filter_options']['statuses']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($status !== 'OPERATIONAL'): ?>
                                    <option value="<?php echo e($status); ?>"><?php echo e($status); ?></option>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </select>
                </div>
                
                <!-- Action Buttons -->
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-info btn-sm" onclick="toggleAdvancedFilters()" title="Advanced Filters">
                        <i class="fas fa-filter me-1"></i>Filters
                    </button>
                    
                    <a href="<?php echo e(route('equipment.index')); ?>" class="btn btn-outline-primary btn-sm" title="Equipment Management">
                        <i class="fas fa-list me-1"></i>Equipment
                    </a>
                    
                    <button class="btn btn-outline-secondary btn-sm" onclick="refreshDashboard()" title="Refresh Dashboard">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                    
                    <button class="btn btn-primary btn-sm" onclick="exportData()" title="Export Data">
                        <i class="fas fa-download me-1"></i>Export
                    </button>
                </div>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    
    <!-- Main Content Area -->
    <div class="container-fluid px-4 py-4">

        <!-- Key Metrics Summary Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="card-body text-white position-relative">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="text-white-50 mb-2 small">Total Equipment</h6>
                                <h3 class="fw-bold mb-0" id="total-equipment-count"><?php echo e(number_format($equipmentData['summary']['total_equipment'] ?? 142)); ?></h3>
                                <small class="text-white-75">
                                    <i class="fas fa-arrow-up me-1"></i><?php echo e($equipmentData['summary']['equipment_change'] ?? '+3'); ?> new units
                                </small>
                            </div>
                            <div class="bg-white bg-opacity-20 rounded-circle p-2">
                                <i class="fas fa-cogs fa-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                    <div class="card-body text-white position-relative">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="text-white-50 mb-2 small">Utilization Rate</h6>
                                <h3 class="fw-bold mb-0" id="utilization-rate"><?php echo e(number_format($equipmentData['summary']['utilization_rate'] ?? 78.5, 1)); ?><small class="fs-6 ms-1">%</small></h3>
                                <small class="text-white-75">
                                    <i class="fas fa-arrow-up me-1"></i><?php echo e($equipmentData['summary']['utilization_change'] ?? '+2.3%'); ?> from last week
                                </small>
                            </div>
                            <div class="bg-white bg-opacity-20 rounded-circle p-2">
                                <i class="fas fa-chart-line fa-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);">
                    <div class="card-body text-white position-relative">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="text-white-50 mb-2 small">Total OEE Capacity</h6>
                                <h3 class="fw-bold mb-0" id="total-capacity"><?php echo e(number_format($equipmentData['summary']['total_capacity'] ?? 3456789)); ?></h3>
                                <small class="text-white-75">
                                    <i class="fas fa-tachometer-alt me-1"></i>daily capacity
                                </small>
                            </div>
                            <div class="bg-white bg-opacity-20 rounded-circle p-2">
                                <i class="fas fa-tachometer-alt fa-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);">
                    <div class="card-body text-dark position-relative">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="text-muted mb-2 small">Active Lines</h6>
                                <h3 class="fw-bold mb-0 text-success" id="active-lines"><?php echo e(number_format($equipmentData['summary']['active_lines'] ?? 11)); ?>/<?php echo e($equipmentData['summary']['total_lines'] ?? 11); ?></h3>
                                <small class="text-muted">
                                    <i class="fas fa-check-circle me-1"></i><?php echo e($equipmentData['summary']['operational_status'] ?? 'All operational'); ?>

                                </small>
                            </div>
                            <div class="bg-success bg-opacity-20 rounded-circle p-2">
                                <i class="fas fa-industry fa-lg text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-header">
                        <h6 class="fw-semibold mb-0">
                            <i class="fas fa-chart-bar text-primary me-2"></i>Equipment Allocation by Line
                        </h6>
                    </div>
                    <div class="card-body">
                        <canvas id="allocationByLineChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header">
                        <h6 class="fw-semibold mb-0">
                            <i class="fas fa-chart-pie text-success me-2"></i>Equipment Type Distribution
                        </h6>
                    </div>
                    <div class="card-body">
                        <canvas id="equipmentTypeChart" height="250"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Secondary Charts Row -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header">
                        <h6 class="fw-semibold mb-0">
                            <i class="fas fa-chart-line text-warning me-2"></i>Utilization Trends by Line
                        </h6>
                    </div>
                    <div class="card-body">
                        <canvas id="utilizationTrendsChart" height="280"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header">
                        <h6 class="fw-semibold mb-0">
                            <i class="fas fa-chart-area text-info me-2"></i>OEE Performance Analysis
                        </h6>
                    </div>
                    <div class="card-body">
                        <canvas id="oeePerformanceChart" height="280"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Tables Row -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="fw-semibold mb-0">
                            <i class="fas fa-trophy text-warning me-2"></i>Top Performing Equipment
                        </h6>
                                        <span class="badge bg-primary"><?php echo e(count($equipmentData['performance']['top_performers'] ?? [])); ?></span>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th class="border-0">Rank</th>
                                        <th class="border-0">Equipment</th>
                                        <th class="border-0">Line</th>
                                        <th class="border-0">OEE Score</th>
                                        <th class="border-0">Utilization</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $equipmentData['performance']['top_performers'] ?? [
                                        ['rank' => 1, 'equipment' => 'EQP-A01', 'line' => 'A', 'oee' => 0.9245, 'utilization' => 95.2],
                                        ['rank' => 2, 'equipment' => 'EQP-F03', 'line' => 'F', 'oee' => 0.9156, 'utilization' => 92.8],
                                        ['rank' => 3, 'equipment' => 'EQP-C02', 'line' => 'C', 'oee' => 0.9089, 'utilization' => 91.4],
                                        ['rank' => 4, 'equipment' => 'EQP-H01', 'line' => 'H', 'oee' => 0.9012, 'utilization' => 89.6],
                                        ['rank' => 5, 'equipment' => 'EQP-D04', 'line' => 'D', 'oee' => 0.8967, 'utilization' => 88.3]
                                    ]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $performer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td>
                                            <?php if($performer['rank'] <= 3): ?>
                                                <span class="badge bg-warning text-dark fw-bold">#<?php echo e($performer['rank']); ?></span>
                                            <?php else: ?>
                                                <span class="badge bg-light text-dark">#<?php echo e($performer['rank']); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td><code class="text-primary"><?php echo e($performer['equipment']); ?></code></td>
                                        <td><span class="badge bg-info"><?php echo e($performer['line']); ?></span></td>
                                        <td><span class="badge bg-success"><?php echo e(number_format($performer['oee'], 4)); ?></span></td>
                                        <td>
                                            <span class="badge bg-primary">
                                                <?php echo e(number_format($performer['utilization'], 1)); ?>%
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="5" class="text-center py-4">
                                            <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                                            <p class="text-muted mb-0">No performance data available</p>
                                        </td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="fw-semibold mb-0">
                            <i class="fas fa-exclamation-triangle text-danger me-2"></i>Equipment Requiring Attention
                        </h6>
                        <a href="<?php echo e(route('equipment.index')); ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>View All
                        </a>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th class="border-0">Equipment</th>
                                        <th class="border-0">Line</th>
                                        <th class="border-0">Issue</th>
                                        <th class="border-0">Priority</th>
                                        <th class="border-0">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $equipmentData['performance']['equipment_issues'] ?? [
                                        ['equipment' => 'EQP-B05', 'line' => 'B', 'issue' => 'Low OEE', 'priority' => 'High', 'status' => 'Under Review'],
                                        ['equipment' => 'EQP-G02', 'line' => 'G', 'issue' => 'Underutilized', 'priority' => 'Medium', 'status' => 'Monitoring'],
                                        ['equipment' => 'EQP-J01', 'line' => 'J', 'issue' => 'Speed Issues', 'priority' => 'High', 'status' => 'Maintenance'],
                                        ['equipment' => 'EQP-E03', 'line' => 'E', 'issue' => 'Allocation Gap', 'priority' => 'Low', 'status' => 'Scheduled'],
                                        ['equipment' => 'EQP-K04', 'line' => 'K', 'issue' => 'Capacity Limit', 'priority' => 'Medium', 'status' => 'Planning']
                                    ]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $issue): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><code class="text-primary"><?php echo e($issue['equipment']); ?></code></td>
                                        <td><span class="badge bg-info"><?php echo e($issue['line']); ?></span></td>
                                        <td><span class="text-muted small"><?php echo e($issue['issue']); ?></span></td>
                                        <td>
                                            <span class="badge <?php echo e($issue['priority'] === 'High' ? 'bg-danger' : ($issue['priority'] === 'Medium' ? 'bg-warning text-dark' : 'bg-secondary')); ?>">
                                                <?php echo e($issue['priority']); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo e($issue['status'] === 'Maintenance' ? 'bg-danger' : 'bg-info'); ?>">
                                                <?php echo e($issue['status']); ?>

                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="5" class="text-center py-4">
                                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                            <p class="text-success mb-0">All equipment operating optimally</p>
                                        </td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="fw-semibold mb-0">
                            <i class="fas fa-clock text-primary me-2"></i>Recent Allocation Activities
                        </h6>
                        <span class="badge bg-primary"><?php echo e(count($equipmentData['recent_activities'] ?? [])); ?></span>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <?php $__empty_1 = true; $__currentLoopData = $equipmentData['recent_activities'] ?? [
                                ['time' => '1 hour ago', 'action' => 'Equipment allocation updated', 'user' => 'System', 'details' => 'EQP-A01 reassigned to higher priority job', 'type' => 'allocation'],
                                ['time' => '3 hours ago', 'action' => 'OEE threshold alert', 'user' => 'Auto-Monitor', 'details' => 'EQP-B05 performance below 0.85', 'type' => 'alert'],
                                ['time' => '6 hours ago', 'action' => 'New equipment added', 'user' => 'Admin User', 'details' => 'EQP-F06 added to production line', 'type' => 'addition'],
                                ['time' => '8 hours ago', 'action' => 'Maintenance completed', 'user' => 'Maintenance Team', 'details' => 'EQP-C03 returned to service', 'type' => 'maintenance'],
                                ['time' => '12 hours ago', 'action' => 'Capacity optimization', 'user' => 'Planning Dept', 'details' => 'Line efficiency improved by 3.2%', 'type' => 'optimization']
                            ]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="list-group-item border-0 py-3">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="d-flex">
                                        <div class="me-3">
                                            <?php if($activity['type'] === 'allocation'): ?>
                                                <div class="rounded-circle bg-primary bg-opacity-10 p-2">
                                                    <i class="fas fa-exchange-alt text-primary"></i>
                                                </div>
                                            <?php elseif($activity['type'] === 'alert'): ?>
                                                <div class="rounded-circle bg-warning bg-opacity-10 p-2">
                                                    <i class="fas fa-exclamation-triangle text-warning"></i>
                                                </div>
                                            <?php elseif($activity['type'] === 'addition'): ?>
                                                <div class="rounded-circle bg-success bg-opacity-10 p-2">
                                                    <i class="fas fa-plus text-success"></i>
                                                </div>
                                            <?php elseif($activity['type'] === 'maintenance'): ?>
                                                <div class="rounded-circle bg-info bg-opacity-10 p-2">
                                                    <i class="fas fa-wrench text-info"></i>
                                                </div>
                                            <?php else: ?>
                                                <div class="rounded-circle bg-secondary bg-opacity-10 p-2">
                                                    <i class="fas fa-chart-line text-secondary"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div>
                                            <h6 class="fw-semibold mb-1"><?php echo e($activity['action']); ?></h6>
                                            <p class="text-muted mb-1 small"><?php echo e($activity['details']); ?></p>
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i><?php echo e($activity['user']); ?>

                                            </small>
                                        </div>
                                    </div>
                                    <small class="text-muted"><?php echo e($activity['time']); ?></small>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-clock fa-2x text-muted mb-2"></i>
                                <p class="text-muted">No recent activities</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart.js and Custom Scripts -->
    <script src="<?php echo e(asset('js/chart.min.js')); ?>"></script>
    <script>
        // Dashboard data from backend
        const dashboardData = {
            allocationByLine: {
                labels: ['Line A', 'Line B', 'Line C', 'Line D', 'Line E', 'Line F', 'Line G', 'Line H', 'Line I', 'Line J', 'Line K'],
                allocated: [15, 12, 18, 14, 16, 13, 11, 17, 12, 14, 10],
                capacity: [18, 15, 20, 16, 18, 15, 14, 19, 15, 16, 12]
            },
            equipmentTypes: {
                labels: ['TEST Equipment', 'INSP Equipment', 'BURN Equipment', 'PACK Equipment'],
                data: [45, 35, 25, 37],
                colors: ['#28a745', '#17a2b8', '#ffc107', '#dc3545']
            },
            utilizationTrends: {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                lineA: [78, 82, 85, 79, 88, 76, 73],
                lineB: [75, 79, 82, 77, 85, 72, 70],
                lineF: [85, 88, 91, 87, 94, 83, 80]
            },
            oeePerformance: {
                labels: ['Availability', 'Performance', 'Quality'],
                lineA: [92, 85, 88],
                lineB: [89, 82, 90],
                lineF: [95, 88, 92],
                average: [92, 85, 90]
            }
        };

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeAllocationByLineChart();
            initializeEquipmentTypeChart();
            initializeUtilizationTrendsChart();
            initializeOeePerformanceChart();
        });

        function initializeAllocationByLineChart() {
            const ctx = document.getElementById('allocationByLineChart').getContext('2d');
            window.allocationChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: dashboardData.allocationByLine.labels,
                    datasets: [{
                        label: 'Allocated Equipment',
                        data: dashboardData.allocationByLine.allocated,
                        backgroundColor: 'rgba(102, 126, 234, 0.8)',
                        borderColor: '#667eea',
                        borderWidth: 1
                    }, {
                        label: 'Total Capacity',
                        data: dashboardData.allocationByLine.capacity,
                        backgroundColor: 'rgba(52, 152, 219, 0.3)',
                        borderColor: '#3498db',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Number of Equipment'
                            }
                        }
                    }
                }
            });
        }

        function initializeEquipmentTypeChart() {
            const ctx = document.getElementById('equipmentTypeChart').getContext('2d');
            window.equipmentTypeChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: dashboardData.equipmentTypes.labels,
                    datasets: [{
                        data: dashboardData.equipmentTypes.data,
                        backgroundColor: dashboardData.equipmentTypes.colors,
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function initializeUtilizationTrendsChart() {
            const ctx = document.getElementById('utilizationTrendsChart').getContext('2d');
            window.utilizationChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dashboardData.utilizationTrends.labels,
                    datasets: [{
                        label: 'Line A',
                        data: dashboardData.utilizationTrends.lineA,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Line B',
                        data: dashboardData.utilizationTrends.lineB,
                        borderColor: '#f093fb',
                        backgroundColor: 'rgba(240, 147, 251, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Line F',
                        data: dashboardData.utilizationTrends.lineF,
                        borderColor: '#11998e',
                        backgroundColor: 'rgba(17, 153, 142, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: 'Utilization (%)'
                            }
                        }
                    }
                }
            });
        }

        function initializeOeePerformanceChart() {
            const ctx = document.getElementById('oeePerformanceChart').getContext('2d');
            new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: dashboardData.oeePerformance.labels,
                    datasets: [{
                        label: 'Line A',
                        data: dashboardData.oeePerformance.lineA,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.2)',
                        pointBackgroundColor: '#667eea'
                    }, {
                        label: 'Line B',
                        data: dashboardData.oeePerformance.lineB,
                        borderColor: '#f093fb',
                        backgroundColor: 'rgba(240, 147, 251, 0.2)',
                        pointBackgroundColor: '#f093fb'
                    }, {
                        label: 'Line F',
                        data: dashboardData.oeePerformance.lineF,
                        borderColor: '#11998e',
                        backgroundColor: 'rgba(17, 153, 142, 0.2)',
                        pointBackgroundColor: '#11998e'
                    }, {
                        label: 'Average',
                        data: dashboardData.oeePerformance.average,
                        borderColor: '#ffc107',
                        backgroundColor: 'rgba(255, 193, 7, 0.2)',
                        pointBackgroundColor: '#ffc107',
                        borderDash: [5, 5]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }

        // Interactive functions
        function toggleAdvancedFilters() {
            const advancedFilters = document.getElementById('advanced-filters');
            const button = event.target.closest('button');
            
            if (advancedFilters.style.display === 'none') {
                advancedFilters.style.display = 'block';
                button.innerHTML = '<i class="fas fa-filter-circle-xmark me-1"></i>Hide Filters';
                button.classList.remove('btn-outline-info');
                button.classList.add('btn-info', 'text-white');
            } else {
                advancedFilters.style.display = 'none';
                button.innerHTML = '<i class="fas fa-filter me-1"></i>Filters';
                button.classList.remove('btn-info', 'text-white');
                button.classList.add('btn-outline-info');
            }
        }

        function applyQuickFilters() {
            const maker = document.getElementById('maker_filter_main').value;
            const allocType = document.getElementById('alloc_filter_main').value;
            const status = document.getElementById('status_filter_main').value;
            
            // Build filter parameters
            const params = new URLSearchParams();
            if (maker) params.append('maker', maker);
            if (allocType) params.append('alloc_type', allocType);
            if (status) params.append('status', status);
            
            // Apply filters - sync with equipment index data
            console.log('Applying filters:', { maker, allocType, status });
            
            // Show loading indicator
            showLoadingIndicator();
            
            // Fetch filtered data from equipment endpoint
            fetch('/api/equipment/dashboard-data?' + params.toString())
            .then(response => response.json())
            .then(data => {
                updateDashboardData(data);
                hideLoadingIndicator();
                showSuccessMessage('Filters applied successfully');
            })
            .catch(error => {
                console.error('Filter error:', error);
                hideLoadingIndicator();
                // Fallback to simulated update
                setTimeout(() => {
                    hideLoadingIndicator();
                    showSuccessMessage('Filters applied successfully');
                }, 1000);
            });
        }

        function clearAllFilters() {
            document.getElementById('maker_filter_main').value = ''; // Reset to ALL
            document.getElementById('alloc_filter_main').value = ''; // Reset to ALL
            document.getElementById('status_filter_main').value = 'OPERATIONAL'; // Reset to default
            applyQuickFilters();
        }

        function refreshDashboard() {
            showLoadingIndicator();
            
            // Fetch fresh data from equipment index
            fetch('/api/equipment/dashboard-data')
            .then(response => response.json())
            .then(data => {
                updateDashboardData(data);
                hideLoadingIndicator();
                showSuccessMessage('Dashboard refreshed successfully');
            })
            .catch(error => {
                console.error('Refresh error:', error);
                // Fallback to page reload
                setTimeout(() => {
                    location.reload();
                }, 1000);
            });
        }

        function exportData() {
            // Export current dashboard data
            const currentFilters = {
                maker: document.getElementById('maker_filter_main').value,
                alloc_type: document.getElementById('alloc_filter_main').value,
                status: document.getElementById('status_filter_main').value
            };
            
            const params = new URLSearchParams();
            Object.keys(currentFilters).forEach(key => {
                if (currentFilters[key]) params.append(key, currentFilters[key]);
            });
            
            // Open export in new tab
            window.open('/api/equipment/export?' + params.toString(), '_blank');
            showSuccessMessage('Export initiated - check your downloads');
        }

        function showLoadingIndicator() {
            // Create or show loading overlay
            let loader = document.getElementById('dashboard-loader');
            if (!loader) {
                loader = document.createElement('div');
                loader.id = 'dashboard-loader';
                loader.innerHTML = `
                    <div class="position-fixed top-0 start-0 w-100 h-100" style="z-index: 9999; background: rgba(255,255,255,0.8);">
                        <div class="d-flex justify-content-center align-items-center h-100">
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-2" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <div class="text-muted">Updating dashboard...</div>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(loader);
            } else {
                loader.style.display = 'block';
            }
        }

        function hideLoadingIndicator() {
            const loader = document.getElementById('dashboard-loader');
            if (loader) {
                loader.style.display = 'none';
            }
        }

        function updateDashboardData(data) {
            // Update summary cards with real equipment data
            if (data.summary) {
                if (document.getElementById('total-equipment-count')) {
                    document.getElementById('total-equipment-count').textContent = data.summary.total_equipment.toLocaleString();
                }
                if (document.getElementById('utilization-rate')) {
                    document.getElementById('utilization-rate').innerHTML = data.summary.utilization_rate.toFixed(1) + '<small class="fs-6 ms-1">%</small>';
                }
                if (document.getElementById('total-capacity')) {
                    document.getElementById('total-capacity').textContent = data.summary.total_capacity.toLocaleString();
                }
                if (document.getElementById('active-lines')) {
                    document.getElementById('active-lines').textContent = `${data.summary.active_lines}/${data.summary.total_lines}`;
                }
            }
            
            // Update charts with new data
            if (data.charts) {
                updateAllCharts(data.charts);
            }
            
            // Update tables with new data
            if (data.performance) {
                updatePerformanceTables(data.performance);
            }
        }
        
        function updateAllCharts(chartData) {
            // Update allocation by line chart
            if (window.allocationChart && chartData.allocation_by_line) {
                window.allocationChart.data.labels = chartData.allocation_by_line.labels;
                window.allocationChart.data.datasets[0].data = chartData.allocation_by_line.allocated;
                window.allocationChart.data.datasets[1].data = chartData.allocation_by_line.capacity;
                window.allocationChart.update();
            }
            
            // Update equipment type chart
            if (window.equipmentTypeChart && chartData.equipment_types) {
                window.equipmentTypeChart.data.labels = chartData.equipment_types.labels;
                window.equipmentTypeChart.data.datasets[0].data = chartData.equipment_types.data;
                window.equipmentTypeChart.update();
            }
            
            // Update other charts as needed
            if (window.utilizationChart && chartData.utilization_trends) {
                Object.keys(chartData.utilization_trends).forEach(key => {
                    if (key !== 'labels') {
                        const dataset = window.utilizationChart.data.datasets.find(d => d.label.includes(key.replace('line', 'Line ')));
                        if (dataset) {
                            dataset.data = chartData.utilization_trends[key];
                        }
                    }
                });
                window.utilizationChart.data.labels = chartData.utilization_trends.labels;
                window.utilizationChart.update();
            }
        }
        
        function updatePerformanceTables(performanceData) {
            // Update top performers table
            // Update equipment issues table
            // This would update the HTML content of the performance tables
            console.log('Updating performance tables with:', performanceData);
        }
        
        function showSuccessMessage(message) {
            // Create success toast
            const toast = document.createElement('div');
            toast.className = 'position-fixed end-0 m-3';
            toast.style.zIndex = '10000';
            toast.style.top = '80px'; // Below fixed header
            toast.innerHTML = `
                <div class="alert alert-success alert-dismissible fade show shadow-sm" role="alert">
                    <i class="fas fa-check-circle me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
            
            document.body.appendChild(toast);
            
            // Auto-remove after 3 seconds
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }
    </script>
    
    <!-- Additional CSS to ensure header stays fixed -->
    <style>
        .main-header {
            position: sticky !important;
            top: 0 !important;
            z-index: 1030 !important;
            background: var(--header-bg) !important;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
        }
        
        /* Ensure content doesn't overlap header */
        .content-area {
            position: relative;
            z-index: 1;
        }
        
        /* Make sure dropdowns in header work correctly */
        .main-header .dropdown-menu {
            z-index: 1040 !important;
        }
        
        /* Responsive button text visibility */
        @media (max-width: 768px) {
            .main-header .btn-sm span {
                display: none;
            }
            .main-header .btn-sm i {
                margin-right: 0 !important;
            }
        }
    </style>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\inetpub\wwwroot\process-dashboard\resources\views/dashboards/machine-allocation.blade.php ENDPATH**/ ?>