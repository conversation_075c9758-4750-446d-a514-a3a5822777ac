<?php

namespace App\Http\Controllers;

use App\Models\Equipment;
use App\Models\EqpCapaRef;
use App\Models\EqpSpeedRef;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class EquipmentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the equipment.
     */
    public function index(Request $request)
    {
        // Get filter parameters with defaults (showing all data initially)
        $filters = [
            'eqp_line' => $request->get('eqp_line', 'all'),
            'size' => $request->get('size', 'all'),
            'eqp_maker' => $request->get('eqp_maker', 'all'),
            'eqp_type' => $request->get('eqp_type', 'all'),
            'alloc_type' => $request->get('alloc_type', 'all'),
            'wildcard_search' => $request->get('wildcard_search', ''),
        ];
        
        // Build Equipment summary query with filters (only OPERATIONAL status equipment)
        $equipmentQuery = Equipment::where('eqp_status', 'OPERATIONAL')
            ->select(
                'id',
                'eqp_no',
                'eqp_line',
                'eqp_area',
                'eqp_type',
                'eqp_maker',
                'size',
                'alloc_type',
                'eqp_status',
                'loading_speed',
                'oee_capa'
            );
        
        // Apply filters
        if ($filters['eqp_line'] !== 'all') {
            $equipmentQuery->where('eqp_line', $filters['eqp_line']);
        }
        if ($filters['size'] !== 'all') {
            $equipmentQuery->where('size', $filters['size']);
        }
        if ($filters['eqp_maker'] !== 'all') {
            $equipmentQuery->where('eqp_maker', $filters['eqp_maker']);
        }
        if ($filters['eqp_type'] !== 'all') {
            $equipmentQuery->where('eqp_type', $filters['eqp_type']);
        }
        if ($filters['alloc_type'] !== 'all') {
            $equipmentQuery->where('alloc_type', $filters['alloc_type']);
        }
        
        // Apply wildcard search if provided
        if (!empty($filters['wildcard_search'])) {
            $searchTerm = $filters['wildcard_search'];
            // Convert wildcards: * to % (multiple chars), ? to _ (single char)
            $likeTerm = str_replace(['*', '?'], ['%', '_'], $searchTerm);
            
            $equipmentQuery->where(function($query) use ($likeTerm) {
                $query->where('eqp_no', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('eqp_line', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('eqp_area', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('eqp_type', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('eqp_maker', 'LIKE', '%' . $likeTerm . '%');
            });
        }
        
        // Get all equipment for totals calculation (before pagination)
        $allEquipmentSummary = $equipmentQuery
            ->orderBy('eqp_no')
            ->orderBy('eqp_line')
            ->orderBy('eqp_type')
            ->get();
        
        // Calculate totals for the header
        $totalEquipmentCount = $allEquipmentSummary->count();
        $totalDailyCapa = $allEquipmentSummary->sum('oee_capa');
        
        // Apply pagination to the equipment summary
        $equipmentSummary = $equipmentQuery
            ->orderBy('eqp_no')
            ->orderBy('eqp_line')
            ->orderBy('eqp_type')
            ->paginate(35)
            ->appends($filters);
        
        // Get filter options from database (only from OPERATIONAL status equipment)
        $filterOptions = [
            'eqp_lines' => Equipment::where('eqp_status', 'OPERATIONAL')->distinct()->pluck('eqp_line')->filter()->sort()->values(),
            'sizes' => Equipment::where('eqp_status', 'OPERATIONAL')->distinct()->pluck('size')->filter()->sort()->values(),
            'eqp_makers' => Equipment::where('eqp_status', 'OPERATIONAL')->distinct()->pluck('eqp_maker')->filter()->sort()->values(),
            'eqp_types' => Equipment::where('eqp_status', 'OPERATIONAL')->distinct()->pluck('eqp_type')->filter()->sort()->values(),
            'alloc_types' => Equipment::where('eqp_status', 'OPERATIONAL')->distinct()->pluck('alloc_type')->filter()->sort()->values(),
            'statuses' => Equipment::distinct()->pluck('eqp_status')->filter()->sort()->values(),
        ];
        
        return view('equipment.index', compact('equipmentSummary', 'filters', 'filterOptions', 'totalEquipmentCount', 'totalDailyCapa'));
    }

    /**
     * Get detailed equipment records for a specific group
     */
    public function getEquipmentGroupDetails(Request $request)
    {
        $request->validate([
            'eqp_line' => 'required|string',
            'eqp_code' => 'required|string',
            'size' => 'nullable|string',
            'eqp_maker' => 'nullable|string',
            'eqp_type' => 'nullable|string',
            'work_type' => 'nullable|string',
            'lot_type' => 'nullable|string',
            'wildcard_search' => 'nullable|string',
        ]);

        $equipmentDetailsQuery = Equipment::where('eqp_status', 'OPERATIONAL')
            ->select(
                'eqp_no',
                'eqp_line',
                'eqp_area',
                'eqp_type',
                'eqp_maker',
                'cam_class',
                'insp_type',
                'linear_type',
                'size',
                'alloc_type',
                'eqp_status',
                'loading_speed',
                'eqp_oee',
                'eqp_passing',
                'eqp_yield',
                'operation_time',
                'oee_capa',
                'ongoing_lot'
            )
            ->where('eqp_line', $request->eqp_line)
            ->where('eqp_code', $request->eqp_code);
        
        // Apply additional filters if provided
        if ($request->has('size') && $request->size !== 'all') {
            $equipmentDetailsQuery->where('size', $request->size);
        }
        if ($request->has('eqp_maker') && $request->eqp_maker !== 'all') {
            $equipmentDetailsQuery->where('eqp_maker', $request->eqp_maker);
        }
        if ($request->has('eqp_type') && $request->eqp_type !== 'all') {
            $equipmentDetailsQuery->where('eqp_type', $request->eqp_type);
        }
        if ($request->has('work_type') && $request->work_type !== 'all') {
            $equipmentDetailsQuery->where('work_type', $request->work_type);
        }
        if ($request->has('lot_type') && $request->lot_type !== 'all') {
            $equipmentDetailsQuery->where('lot_type', $request->lot_type);
        }
        
        // Apply wildcard search if provided
        if ($request->has('wildcard_search') && !empty($request->wildcard_search)) {
            $searchTerm = $request->wildcard_search;
            // Convert wildcards: * to % (multiple chars), ? to _ (single char)
            $likeTerm = str_replace(['*', '?'], ['%', '_'], $searchTerm);
            
            $equipmentDetailsQuery->where(function($query) use ($likeTerm) {
                $query->where('eqp_no', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('eqp_line', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('eqp_code', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('eqp_area', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('eqp_maker', 'LIKE', '%' . $likeTerm . '%');
            });
        }
        
        $equipmentDetails = $equipmentDetailsQuery->orderBy('eqp_no')->get();

        return response()->json([
            'success' => true,
            'data' => $equipmentDetails,
            'group_info' => [
                'eqp_line' => $request->eqp_line,
                'eqp_code' => $request->eqp_code,
                'total_equipment' => $equipmentDetails->count(),
                'total_on_equipment' => $equipmentDetails->where('eqp_status', 'ON')->count(),
                'average_oee' => $equipmentDetails->avg(function($item) {
                    return floatval($item->eqp_oee) * floatval($item->eqp_speed) * floatval($item->operation_time);
                })
            ]
        ]);
    }
    
    /**
     * Export equipment data to CSV
     */
    public function exportEquipment(Request $request)
    {
        // Get filters
        $filters = [
            'maker' => $request->get('maker', ''),
            'alloc_type' => $request->get('alloc_type', ''),
            'status' => $request->get('status', 'OPERATIONAL')
        ];
        
        // Build equipment query with status filter
        $equipmentQuery = Equipment::where('eqp_status', $filters['status'] ?: 'OPERATIONAL');
        
        // Apply filters
        if (!empty($filters['maker'])) {
            $equipmentQuery->where('eqp_maker', $filters['maker']);
        }
        if (!empty($filters['alloc_type'])) {
            $equipmentQuery->where('alloc_type', $filters['alloc_type']);
        }
        
        $equipment = $equipmentQuery->orderBy('eqp_line')->orderBy('eqp_no')->get();
        
        // Generate CSV content
        $csvContent = "Equipment No,Line,Area,Type,Maker,Size,Alloc Type,Status,OEE Capacity,Loading Speed\n";
        
        foreach ($equipment as $item) {
            $csvContent .= implode(',', [
                $item->eqp_no,
                $item->eqp_line,
                $item->eqp_area ?? 'N/A',
                $item->eqp_type,
                $item->eqp_maker,
                $item->size,
                $item->alloc_type ?? 'NORMAL',
                $item->eqp_status,
                $item->oee_capa,
                $item->loading_speed
            ]) . "\n";
        }
        
        $filename = 'machine_allocation_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        return response($csvContent)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    /**
     * Show the form for creating a new equipment.
     */
    public function create()
    {
        // Get reference data for dropdowns
        $referenceData = [
            'eqp_types' => EqpSpeedRef::getUniqueEqpTypes(),
            'sizes' => EqpSpeedRef::getUniqueSizes(),
            'alloc_types' => EqpCapaRef::getUniqueWorkTypes(),
            'speed_references' => EqpSpeedRef::getAllCombinations(),
            'capacity_references' => EqpCapaRef::getAllCombinations(),
        ];
        
        return view('equipment.create', compact('referenceData'));
    }

    /**
     * Store a newly created equipment in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'eqp_no' => 'required|string|max:255|unique:equipment',
            'eqp_line' => 'required|string|max:255',
            'eqp_area' => 'nullable|string|max:255',
            'eqp_type' => 'required|string|max:255',
            'eqp_maker' => 'required|string|max:255',
            'cam_class' => 'nullable|string|max:255',
            'insp_type' => 'nullable|string|max:255',
            'linear_type' => 'nullable|string|max:255',
            'size' => 'required|string|max:255',
            'alloc_type' => 'nullable|string|max:255',
            'eqp_status' => 'required|string|in:OPERATIONAL,PLANSTOP,IDDLE,BREAKDOWN,NEW,ADVANCE',
            'loading_speed' => 'required|integer|min:0',
            'eqp_oee' => 'required|numeric|min:0|max:1',
            'eqp_passing' => 'required|numeric|min:0|max:1',
            'eqp_yield' => 'required|numeric|min:0|max:1',
            'operation_time' => 'required|integer|min:0',
            'ideal_capa' => 'nullable|integer|min:0',
            'oee_capa' => 'nullable|integer|min:0',
            'output_capa' => 'nullable|integer|min:0',
            'ongoing_lot' => 'nullable|string|max:255',
        ]);

        $data = $request->all();
        $data['modified_by'] = Auth::id();

        $equipment = Equipment::create($data);
        
        // Auto-populate reference data and calculate capacities
        $equipment->populateFromReference();
        $equipment->save();

        return redirect()->route('equipment.index')
            ->with('success', 'Equipment created successfully.');
    }

    /**
     * Display the specified equipment.
     */
    public function show(Equipment $equipment)
    {
        return view('equipment.show', compact('equipment'));
    }

    /**
     * Show the form for editing the specified equipment.
     */
    public function edit(Request $request, Equipment $equipment)
    {
        // Capture current filter parameters to preserve them
        $currentFilters = $request->only(['eqp_line', 'size', 'eqp_maker', 'eqp_type', 'alloc_type', 'wildcard_search']);
        $filterQuery = http_build_query(array_filter($currentFilters));
        
        // Get reference data for dropdowns
        $referenceData = [
            'eqp_types' => EqpSpeedRef::getUniqueEqpTypes(),
            'sizes' => EqpSpeedRef::getUniqueSizes(),
            'alloc_types' => EqpCapaRef::getUniqueWorkTypes(),
            'speed_references' => EqpSpeedRef::getAllCombinations(),
            'capacity_references' => EqpCapaRef::getAllCombinations(),
        ];
        
        return view('equipment.edit', compact('equipment', 'filterQuery', 'referenceData'));
    }

    /**
     * Update the specified equipment in storage.
     */
    public function update(Request $request, Equipment $equipment)
    {
        $request->validate([
            'eqp_no' => 'required|string|max:255|unique:equipment,eqp_no,' . $equipment->id,
            'eqp_line' => 'required|string|max:255',
            'eqp_area' => 'nullable|string|max:255',
            'eqp_type' => 'required|string|max:255',
            'eqp_maker' => 'required|string|max:255',
            'cam_class' => 'nullable|string|max:255',
            'insp_type' => 'nullable|string|max:255',
            'linear_type' => 'nullable|string|max:255',
            'size' => 'required|string|max:255',
            'alloc_type' => 'nullable|string|max:255',
            'eqp_status' => 'required|string|in:OPERATIONAL,PLANSTOP,IDDLE,BREAKDOWN,NEW,ADVANCE',
            'loading_speed' => 'required|integer|min:0',
            'eqp_oee' => 'required|numeric|min:0|max:1',
            'eqp_passing' => 'required|numeric|min:0|max:1',
            'eqp_yield' => 'required|numeric|min:0|max:1',
            'operation_time' => 'required|integer|min:0',
            'ideal_capa' => 'nullable|integer|min:0',
            'oee_capa' => 'nullable|integer|min:0',
            'output_capa' => 'nullable|integer|min:0',
            'ongoing_lot' => 'nullable|string|max:255',
        ]);

        $data = $request->all();
        $data['modified_by'] = Auth::id();
        
        // Check if size or alloc_type changed and update reference data
        $sizeChanged = $equipment->size !== $request->size;
        $allocTypeChanged = $equipment->alloc_type !== $request->alloc_type;
        
        $equipment->update($data);
        
        // If size or alloc_type changed, update reference data
        if ($sizeChanged || $allocTypeChanged) {
            // Get updated speed reference
            $speedRef = EqpSpeedRef::getByEqpTypeAndSize($equipment->eqp_type, $equipment->size);
            if ($speedRef) {
                $equipment->loading_speed = $speedRef->eqp_speed;
            }
            
            // Get updated capacity reference
            $capaRef = EqpCapaRef::getByWorkTypeAndSize($equipment->alloc_type, $equipment->size);
            if ($capaRef) {
                $equipment->eqp_oee = $capaRef->oee;
                $equipment->eqp_passing = $capaRef->passing;
                $equipment->eqp_yield = $capaRef->yield;
            }
        }
        
        // Always recalculate capacities when updating
        $equipment->calculateCapacities();
        $equipment->save();

        // Preserve filter parameters from the referring page
        $filterParams = [];
        if ($request->has('return_filters')) {
            parse_str($request->return_filters, $filterParams);
        }

        return redirect()->route('equipment.index', $filterParams)
            ->with('success', 'Equipment updated successfully.');
    }

    /**
     * Remove the specified equipment from storage.
     */
    public function destroy(Request $request, Equipment $equipment)
    {
        $equipment->delete();
        
        // Preserve filter parameters from the referring page
        $filterParams = [];
        if ($request->has('return_filters')) {
            parse_str($request->return_filters, $filterParams);
        }

        return redirect()->route('equipment.index', $filterParams)
            ->with('success', 'Equipment deleted successfully.');
    }
    
    /**
     * Get equipment reference data by type and size
     */
    public function getEquipmentReferenceData(Request $request)
    {
        $request->validate([
            'eqp_type' => 'nullable|string',
            'size' => 'nullable|string',
            'alloc_type' => 'nullable|string',
        ]);
        
        $data = [];
        
        // Get speed reference if eqp_type and size are provided
        if ($request->eqp_type && $request->size) {
            $speedRef = EqpSpeedRef::getByEqpTypeAndSize($request->eqp_type, $request->size);
            if ($speedRef) {
                $data['loading_speed'] = $speedRef->eqp_speed;
            }
        }
        
        // Get capacity reference if alloc_type and size are provided
        if ($request->alloc_type && $request->size) {
            $capaRef = EqpCapaRef::getByWorkTypeAndSize($request->alloc_type, $request->size);
            if ($capaRef) {
                $data['eqp_oee'] = $capaRef->oee;
                $data['eqp_passing'] = $capaRef->passing;
                $data['eqp_yield'] = $capaRef->yield;
            }
        }
        
        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }
    
    /**
     * Calculate equipment capacities
     */
    public function calculateCapacities(Request $request)
    {
        $request->validate([
            'loading_speed' => 'required|numeric|min:0',
            'operation_time' => 'required|numeric|min:0',
            'eqp_oee' => 'nullable|numeric|min:0|max:1',
            'eqp_passing' => 'nullable|numeric|min:0|max:1',
            'eqp_yield' => 'nullable|numeric|min:0|max:1',
        ]);
        
        $loadingSpeed = $request->loading_speed;
        $operationTime = $request->operation_time;
        $eqpOee = $request->eqp_oee ?? 0;
        $eqpPassing = $request->eqp_passing ?? 0;
        $eqpYield = $request->eqp_yield ?? 0;
        
        // Calculate capacities using correct formulas
        $idealCapa = $loadingSpeed * $operationTime;
        $oeeCapa = (int)($loadingSpeed * $eqpOee * $operationTime);
        $outputCapa = (int)($loadingSpeed * $eqpOee * $eqpPassing * $eqpYield * $operationTime);
        
        return response()->json([
            'success' => true,
            'data' => [
                'ideal_capa' => number_format($idealCapa),
                'oee_capa' => number_format($oeeCapa),
                'output_capa' => number_format($outputCapa),
            ]
        ]);
    }
}
