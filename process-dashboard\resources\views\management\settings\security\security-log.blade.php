<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Log - Security Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .log-entry {
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            background-color: #f8f9fa;
            border-left: 3px solid #007bff;
            margin-bottom: 0.5rem;
        }
        .log-error { border-left-color: #dc3545; }
        .log-warning { border-left-color: #ffc107; }
        .log-info { border-left-color: #17a2b8; }
        .log-success { border-left-color: #28a745; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4><i class="fas fa-history me-2"></i>Security Log</h4>
            <div>
                <button type="button" class="btn btn-outline-primary me-2" onclick="window.location.reload()">
                    <i class="fas fa-sync me-2"></i>Refresh
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="window.close()">
                    <i class="fas fa-times me-2"></i>Close
                </button>
            </div>
        </div>

        @if(count($securityLogs) > 0)
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                Showing the last <strong>{{ count($securityLogs) }}</strong> security-related log entries
            </div>

            <div class="card">
                <div class="card-body p-0">
                    @foreach($securityLogs as $index => $logEntry)
                        @if(!empty(trim($logEntry)))
                            @php
                                $logClass = 'log-entry';
                                if (stripos($logEntry, 'error') !== false || stripos($logEntry, 'failed') !== false) {
                                    $logClass .= ' log-error';
                                    $icon = 'fas fa-times-circle text-danger';
                                } elseif (stripos($logEntry, 'warning') !== false) {
                                    $logClass .= ' log-warning';
                                    $icon = 'fas fa-exclamation-triangle text-warning';
                                } elseif (stripos($logEntry, 'login') !== false || stripos($logEntry, 'success') !== false) {
                                    $logClass .= ' log-success';
                                    $icon = 'fas fa-check-circle text-success';
                                } else {
                                    $logClass .= ' log-info';
                                    $icon = 'fas fa-info-circle text-info';
                                }
                            @endphp
                            <div class="{{ $logClass }} p-3 {{ $index < count($securityLogs) - 1 ? 'border-bottom' : '' }}">
                                <div class="d-flex align-items-start">
                                    <i class="{{ $icon }} me-2 mt-1 flex-shrink-0"></i>
                                    <div class="flex-grow-1">
                                        <small class="text-muted d-block mb-1">#{{ $index + 1 }}</small>
                                        <code class="text-dark">{{ $logEntry }}</code>
                                    </div>
                                </div>
                            </div>
                        @endif
                    @endforeach
                </div>
            </div>

            <div class="mt-4">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                                <h6 class="card-title">Success Events</h6>
                                <p class="card-text text-success">
                                    {{ collect($securityLogs)->filter(function($log) { 
                                        return stripos($log, 'login') !== false && stripos($log, 'failed') === false; 
                                    })->count() }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-times-circle text-danger fa-2x mb-2"></i>
                                <h6 class="card-title">Failed Attempts</h6>
                                <p class="card-text text-danger">
                                    {{ collect($securityLogs)->filter(function($log) { 
                                        return stripos($log, 'failed') !== false; 
                                    })->count() }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-exclamation-triangle text-warning fa-2x mb-2"></i>
                                <h6 class="card-title">Warnings</h6>
                                <p class="card-text text-warning">
                                    {{ collect($securityLogs)->filter(function($log) { 
                                        return stripos($log, 'warning') !== false; 
                                    })->count() }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-sign-out-alt text-info fa-2x mb-2"></i>
                                <h6 class="card-title">Logout Events</h6>
                                <p class="card-text text-info">
                                    {{ collect($securityLogs)->filter(function($log) { 
                                        return stripos($log, 'logout') !== false; 
                                    })->count() }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                No security-related log entries found.
            </div>
        @endif
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>