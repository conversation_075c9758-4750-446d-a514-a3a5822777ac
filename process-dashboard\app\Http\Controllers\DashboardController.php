<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Endtime;
use App\Models\Equipment;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Http\Response;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        // Get dashboard filter parameters
        $filters = [
            'dashboard_date' => $request->get('dashboard_date', today()->format('Y-m-d')),
            'dashboard_shift' => $request->get('dashboard_shift', 'day'),
            'dashboard_cutoff' => $request->get('dashboard_cutoff', 'all'),
            'dashboard_alloc_type' => $request->get('dashboard_alloc_type', 'all'),
            'dashboard_work_type' => $request->get('dashboard_work_type', 'all') // Keep for endtime filtering
        ];
        
        $dashboardStats = $this->getEndtimeDashboardStats($filters);
        
        // Get Per Line Summary and Per Size Summary data
        $perLineSummary = $this->getPerLineSummary($filters);
        $perSizeSummary = $this->getPerSizeSummary($filters);
        $linePerformanceAnalysis = $this->getLinePerformanceAnalysis($filters);
        
        // Get dynamic panel data
        $previousShiftAchievement = $this->getPreviousShiftAchievement($filters);
        $currentPerformanceMonitor = $this->getCurrentPerformanceMonitor($filters);
        
        return view('dashboard', compact(
            'dashboardStats', 
            'perLineSummary', 
            'perSizeSummary', 
            'linePerformanceAnalysis',
            'previousShiftAchievement',
            'currentPerformanceMonitor'
        ));
    }


    
    /**
     * Show the Machine Allocation Dashboard
     */
    public function machineAllocation(Request $request)
    {
        $equipmentData = $this->getMachineAllocationData($request);
        return view('dashboards.machine-allocation', compact('equipmentData'));
    }
    
    /**
     * API endpoint for dashboard data (JSON response)
     */
    public function getDashboardData(Request $request)
    {
        $data = $this->getMachineAllocationData($request);
        return response()->json($data);
    }
    
    /**
     * Get machine allocation data from equipment index
     */
    private function getMachineAllocationData(Request $request)
    {
        // Get filters with defaults
        $filters = [
            'maker' => $request->get('maker', ''),
            'alloc_type' => $request->get('alloc_type', ''),
            'status' => $request->get('status', 'OPERATIONAL')
        ];
        
        // Build equipment query with default status filter
        $equipmentQuery = Equipment::where('eqp_status', $filters['status']);
        
        // Apply filters
        if (!empty($filters['maker'])) {
            $equipmentQuery->where('eqp_maker', $filters['maker']);
        }
        if (!empty($filters['alloc_type'])) {
            $equipmentQuery->where('alloc_type', $filters['alloc_type']);
        }
        
        $equipmentData = $equipmentQuery->get();
        
        // Calculate summary statistics
        $summary = [
            'total_equipment' => $equipmentData->count(),
            'utilization_rate' => $this->calculateUtilizationRate($equipmentData),
            'total_capacity' => $equipmentData->sum('oee_capa'),
            'active_lines' => $equipmentData->pluck('eqp_line')->unique()->count(),
            'total_lines' => 11, // A-K lines
            'equipment_change' => '+3', // Mock change data
            'utilization_change' => '+2.3%', // Mock change data
            'operational_status' => 'All operational'
        ];
        
        // Generate chart data
        $charts = $this->generateChartData($equipmentData);
        
        // Generate performance data
        $performance = $this->generatePerformanceData($equipmentData);
        
        // Generate recent activities
        $recentActivities = $this->generateRecentActivities($equipmentData);
        
        // Get filter options for dropdowns
        $filterOptions = [
            'makers' => Equipment::distinct()->pluck('eqp_maker')->filter()->sort()->values()->toArray(),
            'alloc_types' => Equipment::distinct()->pluck('alloc_type')->filter()->sort()->values()->toArray(),
            'statuses' => Equipment::distinct()->pluck('eqp_status')->filter()->sort()->values()->toArray()
        ];
        
        return [
            'summary' => $summary,
            'charts' => $charts,
            'performance' => $performance,
            'recent_activities' => $recentActivities,
            'filter_options' => $filterOptions,
            'equipment_data' => $equipmentData
        ];
    }
    
    /**
     * Calculate utilization rate from equipment data
     */
    private function calculateUtilizationRate($equipmentData)
    {
        if ($equipmentData->isEmpty()) {
            return 0;
        }
        
        $totalOee = $equipmentData->sum(function($equipment) {
            return floatval($equipment->eqp_oee ?? 0.8) * 
                   floatval($equipment->loading_speed ?? 100) / 100 * 
                   floatval($equipment->operation_time ?? 24) / 24;
        });
        
        return ($totalOee / $equipmentData->count()) * 100;
    }
    
    /**
     * Generate chart data for dashboard
     */
    private function generateChartData($equipmentData)
    {
        // Allocation by line data
        $lineData = $equipmentData->groupBy('eqp_line');
        $allocationByLine = [
            'labels' => [],
            'allocated' => [],
            'capacity' => []
        ];
        
        foreach (range('A', 'K') as $line) {
            $allocationByLine['labels'][] = "Line $line";
            $lineEquipment = $lineData->get($line, collect());
            $allocationByLine['allocated'][] = $lineEquipment->count();
            $allocationByLine['capacity'][] = $lineEquipment->sum('oee_capa') / 1000; // Scale down for readability
        }
        
        // Equipment type distribution
        $typeData = $equipmentData->groupBy('eqp_type');
        $equipmentTypes = [
            'labels' => [],
            'data' => [],
            'colors' => ['#28a745', '#17a2b8', '#ffc107', '#dc3545']
        ];
        
        foreach ($typeData as $type => $equipment) {
            $equipmentTypes['labels'][] = "$type Equipment";
            $equipmentTypes['data'][] = $equipment->count();
        }
        
        // Utilization trends (mock data based on lines)
        $utilizationTrends = [
            'labels' => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            'lineA' => [78, 82, 85, 79, 88, 76, 73],
            'lineB' => [75, 79, 82, 77, 85, 72, 70],
            'lineF' => [85, 88, 91, 87, 94, 83, 80]
        ];
        
        return [
            'allocation_by_line' => $allocationByLine,
            'equipment_types' => $equipmentTypes,
            'utilization_trends' => $utilizationTrends
        ];
    }
    
    /**
     * Generate performance analysis data
     */
    private function generatePerformanceData($equipmentData)
    {
        // Top performers
        $topPerformers = $equipmentData
            ->sortByDesc(function($equipment) {
                return floatval($equipment->eqp_oee ?? 0.8) * floatval($equipment->loading_speed ?? 100) / 100;
            })
            ->take(5)
            ->values()
            ->map(function($equipment, $index) {
                $oeeScore = floatval($equipment->eqp_oee ?? 0.8) * floatval($equipment->loading_speed ?? 100) / 100;
                return [
                    'rank' => $index + 1,
                    'equipment' => $equipment->eqp_no,
                    'line' => $equipment->eqp_line,
                    'oee' => $oeeScore,
                    'utilization' => $oeeScore * 100
                ];
            });
        
        // Equipment requiring attention (mock data)
        $equipmentIssues = collect([
            ['equipment' => 'EQP-B05', 'line' => 'B', 'issue' => 'Low OEE', 'priority' => 'High', 'status' => 'Under Review'],
            ['equipment' => 'EQP-G02', 'line' => 'G', 'issue' => 'Underutilized', 'priority' => 'Medium', 'status' => 'Monitoring'],
        ]);
        
        return [
            'top_performers' => $topPerformers,
            'equipment_issues' => $equipmentIssues
        ];
    }
    
    /**
     * Generate recent activities data
     */
    private function generateRecentActivities($equipmentData)
    {
        // Mock recent activities data based on equipment data
        return [
            [
                'time' => '1 hour ago',
                'action' => 'Equipment allocation updated',
                'user' => 'System',
                'details' => 'EQP-A01 reassigned to higher priority job',
                'type' => 'allocation'
            ],
            [
                'time' => '3 hours ago',
                'action' => 'OEE threshold alert',
                'user' => 'Auto-Monitor',
                'details' => 'Performance monitoring detected efficiency drop',
                'type' => 'alert'
            ],
            [
                'time' => '6 hours ago',
                'action' => 'New equipment added',
                'user' => 'Admin User',
                'details' => 'Equipment count updated: ' . $equipmentData->count() . ' total',
                'type' => 'addition'
            ],
            [
                'time' => '8 hours ago',
                'action' => 'Maintenance completed',
                'user' => 'Maintenance Team',
                'details' => 'Equipment returned to operational status',
                'type' => 'maintenance'
            ],
            [
                'time' => '12 hours ago',
                'action' => 'Capacity optimization',
                'user' => 'Planning Dept',
                'details' => 'Line efficiency analysis completed',
                'type' => 'optimization'
            ]
        ];
    }
    
    /**
     * Show the Lot Request Dashboard for analytics
     */
    public function lotRequestDashboard()
    {
        // Mock data for the analytics dashboard
        $analytics = [
            'total_requests' => 25,
            'pending_requests' => 8,
            'completed_requests' => 17,
            'total_equipment' => 45,
            'total_lots' => 1250,
            'avg_processing_time' => '2.5h',
            'status_counts' => [
                'pending' => 8,
                'in_process' => 5,
                'completed' => 17,
                'cancelled' => 2
            ],
            'processing_times' => [
                'fastest' => '0.5h',
                'average' => '2.5h',
                'slowest' => '8.2h'
            ],
            'queue_length' => 3
        ];
        
        // Mock recent requests data
        $recentRequests = collect([]);
        
        // Mock top requestors data
        $topRequestors = collect([]);
        
        return view('dashboards.lot-requests', compact('analytics', 'recentRequests', 'topRequestors'));
    }
    
    /**
     * Show the WIP Dashboard for analytics
     */
    public function wipDashboard()
    {
        // Generate comprehensive WIP analytics data
        $summary = $this->getWipSummaryData();
        $recentUpdates = $this->getWipRecentUpdates();
        $topGroups = $this->getTopWipGroups();
        
        return view('dashboards.wip', [
            'title' => 'WIP Analytics Dashboard',
            'summary' => $summary,
            'recentUpdates' => $recentUpdates,
            'topGroups' => $topGroups
        ]);
    }
    
    /**
     * Generate WIP summary statistics
     */
    private function getWipSummaryData()
    {
        // Mock comprehensive WIP summary data
        // In a real implementation, this would query the WIP database tables
        return [
            'total_lots' => 1234,
            'total_quantity' => 2456789,
            'avg_tat' => 15.7,
            'critical_lots' => 45
        ];
    }
    
    /**
     * Get recent WIP updates and activities
     */
    private function getWipRecentUpdates()
    {
        // Mock recent updates data
        // In a real implementation, this would track WIP activity logs
        return [
            [
                'time' => '2 hours ago',
                'action' => 'WIP data refreshed',
                'user' => 'System',
                'details' => '1,234 lots processed'
            ],
            [
                'time' => '5 hours ago',
                'action' => 'Critical lots identified',
                'user' => 'Auto-Alert',
                'details' => '45 lots >30 days TAT'
            ],
            [
                'time' => '8 hours ago',
                'action' => 'Manual update',
                'user' => 'Admin User',
                'details' => 'Excel import completed'
            ],
            [
                'time' => '1 day ago',
                'action' => 'Bottleneck detected',
                'user' => 'Analytics',
                'details' => 'EQP01 station delay'
            ],
            [
                'time' => '2 days ago',
                'action' => 'Performance report',
                'user' => 'System',
                'details' => 'Weekly summary generated'
            ]
        ];
    }
    
    /**
     * Get top WIP groups by volume
     */
    private function getTopWipGroups()
    {
        // Mock top WIP groups data
        // In a real implementation, this would query actual WIP groupings
        return [
            [
                'rank' => 1,
                'group' => 'LG-A001-TEST-EQP01-NOR',
                'lots' => 156,
                'quantity' => 234567,
                'tat' => 12.5
            ],
            [
                'rank' => 2,
                'group' => 'MD-B002-INSP-EQP02-HOT',
                'lots' => 134,
                'quantity' => 198765,
                'tat' => 18.3
            ],
            [
                'rank' => 3,
                'group' => 'SM-C003-BURN-EQP03-NOR',
                'lots' => 112,
                'quantity' => 145632,
                'tat' => 9.7
            ],
            [
                'rank' => 4,
                'group' => 'LG-D004-TEST-EQP01-SUP',
                'lots' => 98,
                'quantity' => 178934,
                'tat' => 25.1
            ],
            [
                'rank' => 5,
                'group' => 'MD-E005-INSP-EQP04-NOR',
                'lots' => 87,
                'quantity' => 123456,
                'tat' => 14.6
            ]
        ];
    }
    
    /**
     * Show the Machine Allocation Dashboard for analytics
     */
    public function machineAllocationDashboard()
    {
        // Generate comprehensive machine allocation analytics data
        $summary = $this->getMachineAllocationSummaryData();
        $topPerformers = $this->getTopPerformingEquipment();
        $equipmentIssues = $this->getEquipmentRequiringAttention();
        $recentActivities = $this->getRecentAllocationActivities();
        
        return view('dashboards.machine-allocation', [
            'title' => 'Machine Allocation Analytics Dashboard',
            'summary' => $summary,
            'topPerformers' => $topPerformers,
            'equipmentIssues' => $equipmentIssues,
            'recentActivities' => $recentActivities
        ]);
    }
    
    /**
     * Generate machine allocation summary statistics
     */
    private function getMachineAllocationSummaryData()
    {
        // Mock comprehensive machine allocation summary data
        // In a real implementation, this would query the Equipment database tables
        return [
            'total_equipment' => 142,
            'utilization_rate' => 78.5,
            'total_capacity' => 3456789,
            'active_lines' => 11
        ];
    }
    
    /**
     * Get top performing equipment by OEE and utilization
     */
    private function getTopPerformingEquipment()
    {
        // Mock top performing equipment data
        // In a real implementation, this would query equipment performance metrics
        return [
            [
                'rank' => 1,
                'equipment' => 'EQP-A01',
                'line' => 'A',
                'oee' => 0.9245,
                'utilization' => 95.2
            ],
            [
                'rank' => 2,
                'equipment' => 'EQP-F03',
                'line' => 'F',
                'oee' => 0.9156,
                'utilization' => 92.8
            ],
            [
                'rank' => 3,
                'equipment' => 'EQP-C02',
                'line' => 'C',
                'oee' => 0.9089,
                'utilization' => 91.4
            ],
            [
                'rank' => 4,
                'equipment' => 'EQP-H01',
                'line' => 'H',
                'oee' => 0.9012,
                'utilization' => 89.6
            ],
            [
                'rank' => 5,
                'equipment' => 'EQP-D04',
                'line' => 'D',
                'oee' => 0.8967,
                'utilization' => 88.3
            ]
        ];
    }
    
    /**
     * Get equipment requiring attention based on performance issues
     */
    private function getEquipmentRequiringAttention()
    {
        // Mock equipment issues data
        // In a real implementation, this would identify equipment with performance problems
        return [
            [
                'equipment' => 'EQP-B05',
                'line' => 'B',
                'issue' => 'Low OEE',
                'priority' => 'High',
                'status' => 'Under Review'
            ],
            [
                'equipment' => 'EQP-G02',
                'line' => 'G',
                'issue' => 'Underutilized',
                'priority' => 'Medium',
                'status' => 'Monitoring'
            ],
            [
                'equipment' => 'EQP-J01',
                'line' => 'J',
                'issue' => 'Speed Issues',
                'priority' => 'High',
                'status' => 'Maintenance'
            ],
            [
                'equipment' => 'EQP-E03',
                'line' => 'E',
                'issue' => 'Allocation Gap',
                'priority' => 'Low',
                'status' => 'Scheduled'
            ],
            [
                'equipment' => 'EQP-K04',
                'line' => 'K',
                'issue' => 'Capacity Limit',
                'priority' => 'Medium',
                'status' => 'Planning'
            ]
        ];
    }
    
    /**
     * Get recent allocation activities and system changes
     */
    private function getRecentAllocationActivities()
    {
        // Mock recent allocation activities data
        // In a real implementation, this would track allocation changes and system events
        return [
            [
                'time' => '1 hour ago',
                'action' => 'Equipment allocation updated',
                'user' => 'System',
                'details' => 'EQP-A01 reassigned to higher priority job',
                'type' => 'allocation'
            ],
            [
                'time' => '3 hours ago',
                'action' => 'OEE threshold alert',
                'user' => 'Auto-Monitor',
                'details' => 'EQP-B05 performance below 0.85',
                'type' => 'alert'
            ],
            [
                'time' => '6 hours ago',
                'action' => 'New equipment added',
                'user' => 'Admin User',
                'details' => 'EQP-F06 added to production line',
                'type' => 'addition'
            ],
            [
                'time' => '8 hours ago',
                'action' => 'Maintenance completed',
                'user' => 'Maintenance Team',
                'details' => 'EQP-C03 returned to service',
                'type' => 'maintenance'
            ],
            [
                'time' => '12 hours ago',
                'action' => 'Capacity optimization',
                'user' => 'Planning Dept',
                'details' => 'Line efficiency improved by 3.2%',
                'type' => 'optimization'
            ]
        ];
    }
    
    /**
     * Show the Endline Report Dashboard for analytics
     */
    public function endlineReportDashboard()
    {
        // This would contain analytics for endline reports
        // For now, return a placeholder view
        return view('dashboards.endline-report', [
            'title' => 'Endline Report Dashboard'
        ]);
    }
    
    /**
     * Show the Endline WIP Entry Form
     */
    public function endlineWipForm()
    {
        // This would show the entry form for endline WIP with multiple entries
        // For now, return a placeholder view
        return view('dashboards.endline-wip-form', [
            'title' => 'Endline WIP Entry Form'
        ]);
    }
    
    /**
     * Calculate endtime dashboard statistics based on filters
     */
    private function getEndtimeDashboardStats($filters)
    {
        // Apply time range filter based on cutoff
        $endtimeQuery = Endtime::query();
        $this->applyDashboardTimeFilter($endtimeQuery, $filters['dashboard_date'], $filters['dashboard_shift'], $filters['dashboard_cutoff']);
        
        // Apply work type filter
        if ($filters['dashboard_work_type'] !== 'all') {
            $endtimeQuery->where('work_type', $filters['dashboard_work_type']);
        }
        
        // Calculate target capacity based on active equipment and filters
        $targetStats = $this->calculateTargetCapacity(
            $filters['dashboard_alloc_type'] ?? 'all',
            $filters['dashboard_shift'],
            $filters['dashboard_cutoff']
        );
        
        // Total lots (ongoing + submitted)
        $totalLots = $endtimeQuery->count();
        $totalQuantity = $endtimeQuery->sum('lot_qty') ?: 0;
        
        // Submitted lots
        $submittedQuery = clone $endtimeQuery;
        $submittedLots = $submittedQuery->where('status', 'Submitted')->count();
        $submittedQuantity = $submittedQuery->sum('lot_qty') ?: 0;
        
        // Ongoing lots
        $ongoingQuery = clone $endtimeQuery;
        $ongoingLots = $ongoingQuery->where('status', 'Ongoing')->count();
        $ongoingQuantity = $ongoingQuery->sum('lot_qty') ?: 0;
        
        // Calculate percentages
        $submittedPercentage = $totalLots > 0 ? ($submittedLots / $totalLots) * 100 : 0;
        $ongoingPercentage = $totalLots > 0 ? ($ongoingLots / $totalLots) * 100 : 0;
        
        // Calculate equipment status
        $equipmentStats = $this->calculateEquipmentStatus($filters);
        
        return [
            'target_capacity' => $targetStats['capacity'],
            'equipment_count' => $targetStats['equipment_count'],
            'total_lots' => $totalLots,
            'total_quantity' => $totalQuantity,
            'submitted_lots' => $submittedLots,
            'submitted_quantity' => $submittedQuantity,
            'submitted_percentage' => $submittedPercentage,
            'ongoing_lots' => $ongoingLots,
            'ongoing_quantity' => $ongoingQuantity,
            'ongoing_percentage' => $ongoingPercentage,
            // Equipment Status data
            'total_equipment' => $equipmentStats['total_equipment'],
            'equipment_with_ongoing' => $equipmentStats['equipment_with_ongoing'],
            'equipment_status_percentage' => $equipmentStats['equipment_percentage'],
        ];
    }
    
    /**
     * Calculate target capacity based on active equipment, alloc type filter, and time period
     */
    private function calculateTargetCapacity($allocType, $shift, $cutoff)
    {
        $equipmentQuery = Equipment::where('eqp_status', 'OPERATIONAL');

        // Apply alloc_type filter if specified
        if ($allocType !== 'all') {
            $equipmentQuery->where('alloc_type', $allocType);
        }

        // Get filtered equipment count (OPERATIONAL status only)
        $equipmentCount = $equipmentQuery->count();
        
        // Calculate total capacity based on equipment specifications and time period
        $equipmentList = $equipmentQuery->get();
        $totalCapacity = 0;
        
        // Calculate time multiplier based on shift and cutoff selection
        $timeMultiplier = $this->getTimeMultiplier($shift, $cutoff);
        
        foreach ($equipmentList as $equipment) {
            $oee = floatval($equipment->eqp_oee) ?: 0;
            $speed = floatval(str_replace(',', '', $equipment->loading_speed)) ?: 0;
            $operationTime = floatval(str_replace(',', '', $equipment->operation_time)) ?: 0;
            
            // Calculate capacity based on time period: OEE × Speed × Operation Time × Time Multiplier
            $periodCapacity = round($oee * $speed * $operationTime * $timeMultiplier);
            $totalCapacity += $periodCapacity;
        }
        
        return [
            'capacity' => $totalCapacity,
            'equipment_count' => $equipmentCount
        ];
    }
    
    /**
     * Get time multiplier based on shift and cutoff selection
     * Based on 6 cutoff periods per day (4 hours each), total 24 hours
     */
    private function getTimeMultiplier($shift, $cutoff)
    {
        if ($shift === 'all') {
            // Full day (24 hours = 6 cutoffs)
            return 1.0;
        }
        
        // Calculate time fraction based on shift and cutoff
        // Each day has 6 cutoff periods of 4 hours each
        if ($shift === 'day') {
            switch ($cutoff) {
                case '1': // 07:00 AM - 11:59 AM (4 hours = 1/6 of day)
                    return 1.0 / 6.0; // ~0.1667
                case '2': // 12:00 PM - 15:59 PM (4 hours = 1/6 of day)
                    return 1.0 / 6.0; // ~0.1667
                case '3': // 16:00 PM - 18:59 PM (3 hours, but treat as 4 for consistency)
                    return 1.0 / 6.0; // ~0.1667
                case 'all': // Entire day shift: 07:00 AM - 18:59 PM (3 cutoffs = 3/6 of day)
                    return 3.0 / 6.0; // 0.5
            }
        } elseif ($shift === 'night') {
            switch ($cutoff) {
                case '1': // 19:00 PM - 23:59 PM (4 hours = 1/6 of day)
                    return 1.0 / 6.0; // ~0.1667
                case '2': // 00:00 AM - 03:59 AM (4 hours = 1/6 of day)
                    return 1.0 / 6.0; // ~0.1667
                case '3': // 04:00 AM - 06:59 AM (3 hours, but treat as 4 for consistency)
                    return 1.0 / 6.0; // ~0.1667
                case 'all': // Entire night shift: 19:00 PM - 06:59 AM (3 cutoffs = 3/6 of day)
                    return 3.0 / 6.0; // 0.5
            }
        }
        
        // Default to full day if no match
        return 1.0;
    }
    
    /**
     * Calculate equipment status based on filters
     * Now uses equipment.ongoing_lot column instead of checking endtime status
     */
    private function calculateEquipmentStatus($filters)
    {
        // Get total equipment count based on alloc type filter (OPERATIONAL status only)
        $totalEquipmentQuery = Equipment::where('eqp_status', 'OPERATIONAL');
        
        // Apply alloc_type filter if specified
        if (isset($filters['dashboard_alloc_type']) && $filters['dashboard_alloc_type'] !== 'all') {
            $totalEquipmentQuery->where('alloc_type', $filters['dashboard_alloc_type']);
        }
        
        $totalEquipment = $totalEquipmentQuery->count();

        // Get equipment with ongoing lots by checking the ongoing_lot column directly (OPERATIONAL status only)
        $equipmentWithOngoingQuery = Equipment::where('eqp_status', 'OPERATIONAL')
            ->whereNotNull('ongoing_lot');
        
        // Apply alloc_type filter if specified
        if (isset($filters['dashboard_alloc_type']) && $filters['dashboard_alloc_type'] !== 'all') {
            $equipmentWithOngoingQuery->where('alloc_type', $filters['dashboard_alloc_type']);
        }
            
        // Note: work_type filter removed as it doesn't exist in equipment table
        // Work type filtering is handled at the endtime level
        
        $equipmentWithOngoing = $equipmentWithOngoingQuery->count();
        
        // Calculate percentage
        $equipmentPercentage = $totalEquipment > 0 ? round(($equipmentWithOngoing / $totalEquipment) * 100, 1) : 0;
        
        return [
            'total_equipment' => $totalEquipment,
            'equipment_with_ongoing' => $equipmentWithOngoing,
            'equipment_percentage' => $equipmentPercentage
        ];
    }
    
    /**
     * Apply dashboard time filter based on cutoff periods
     */
    private function applyDashboardTimeFilter($query, $date, $shift, $cutoff)
    {
        if (!$date) {
            return; // No filtering if date is missing
        }

        $baseDate = Carbon::parse($date);
        $startDateTime = null;
        $endDateTime = null;

        // If shift is 'all', filter by entire date (00:00:00 to 23:59:59)
        if ($shift === 'all') {
            $startDateTime = $baseDate->copy()->startOfDay();
            $endDateTime = $baseDate->copy()->endOfDay();
        }
        // Define cutoff periods for each specific shift
        elseif ($shift === 'day') {
            switch ($cutoff) {
                case '1': // 7:00 AM - 12:00 PM
                    $startDateTime = $baseDate->copy()->setTime(7, 0, 0);
                    $endDateTime = $baseDate->copy()->setTime(11, 59, 59);
                    break;
                case '2': // 12:00 PM - 4:00 PM
                    $startDateTime = $baseDate->copy()->setTime(12, 0, 0);
                    $endDateTime = $baseDate->copy()->setTime(15, 59, 59);
                    break;
                case '3': // 4:00 PM - 7:00 PM
                    $startDateTime = $baseDate->copy()->setTime(16, 0, 0);
                    $endDateTime = $baseDate->copy()->setTime(18, 59, 59);
                    break;
                case 'all': // Entire day shift: 7:00 AM - 7:00 PM
                    $startDateTime = $baseDate->copy()->setTime(7, 0, 0);
                    $endDateTime = $baseDate->copy()->setTime(18, 59, 59);
                    break;
            }
        } elseif ($shift === 'night') {
            // Night shift uses start date convention:
            // Night shift "Sept 6" = Sept 6 19:00 → Sept 7 06:59
            switch ($cutoff) {
                case '1': // 7:00 PM - 11:59 PM (same day as start date)
                    $startDateTime = $baseDate->copy()->setTime(19, 0, 0);
                    $endDateTime = $baseDate->copy()->setTime(23, 59, 59);
                    break;
                case '2': // 12:00 AM - 3:59 AM (next day after start date)
                    $startDateTime = $baseDate->copy()->addDay()->setTime(0, 0, 0);
                    $endDateTime = $baseDate->copy()->addDay()->setTime(3, 59, 59);
                    break;
                case '3': // 4:00 AM - 6:59 AM (next day after start date)
                    $startDateTime = $baseDate->copy()->addDay()->setTime(4, 0, 0);
                    $endDateTime = $baseDate->copy()->addDay()->setTime(6, 59, 59);
                    break;
                case 'all': // Entire night shift: 7:00 PM (start date) - 6:59 AM (next day)
                    $startDateTime = $baseDate->copy()->setTime(19, 0, 0);
                    $endDateTime = $baseDate->copy()->addDay()->setTime(6, 59, 59);
                    break;
            }
        }

        // Apply the time range filter if we have valid start and end times
        if ($startDateTime && $endDateTime) {
            // For submitted lots, use actual_submitted_at; for ongoing lots, use est_endtime
            $query->where(function($q) use ($startDateTime, $endDateTime) {
                $q->where(function($subQ) use ($startDateTime, $endDateTime) {
                    // Submitted lots: filter by actual_submitted_at
                    $subQ->where('status', 'Submitted')
                         ->whereBetween('actual_submitted_at', [$startDateTime, $endDateTime]);
                })->orWhere(function($subQ) use ($startDateTime, $endDateTime) {
                    // Ongoing lots: filter by est_endtime
                    $subQ->where('status', 'Ongoing')
                         ->whereBetween('est_endtime', [$startDateTime, $endDateTime]);
                });
            });
        }
    }
    
    /**
     * API endpoint for per-line equipment utilization data
     */
    public function getLineEquipmentStats(Request $request)
    {
        try {
            $line = $request->get('line', 'A');
            $filters = [
                'dashboard_date' => $request->get('dashboard_date', today()->format('Y-m-d')),
                'dashboard_shift' => $request->get('dashboard_shift', 'all'),
                'dashboard_cutoff' => $request->get('dashboard_cutoff', 'all'),
                'dashboard_work_type' => $request->get('dashboard_work_type', 'all'),
                'dashboard_alloc_type' => $request->get('dashboard_alloc_type', 'all')
            ];
            
            $lineEquipmentStats = $this->calculateLineEquipmentStatus($line, $filters);
            
            return response()->json([
                'success' => true,
                'line' => $line,
                'stats' => $lineEquipmentStats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get line equipment stats: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Calculate equipment status for a specific production line
     */
    private function calculateLineEquipmentStatus($line, $filters)
    {
        // Get total equipment count for this line based on filters (only OPERATIONAL status)
        $totalEquipmentQuery = Equipment::where('eqp_line', $line)
            ->where('eqp_status', 'OPERATIONAL');
            
        // Apply alloc_type filter if specified
        if (isset($filters['dashboard_alloc_type']) && $filters['dashboard_alloc_type'] !== 'all') {
            $totalEquipmentQuery->where('alloc_type', $filters['dashboard_alloc_type']);
        }
        
        $totalEquipment = $totalEquipmentQuery->count();

        // Get equipment with ongoing lots for this line by checking the ongoing_lot column directly (only OPERATIONAL status)
        $equipmentWithOngoingQuery = Equipment::where('eqp_line', $line)
            ->where('eqp_status', 'OPERATIONAL')
            ->whereNotNull('ongoing_lot');
            
        // Apply alloc_type filter if specified
        if (isset($filters['dashboard_alloc_type']) && $filters['dashboard_alloc_type'] !== 'all') {
            $equipmentWithOngoingQuery->where('alloc_type', $filters['dashboard_alloc_type']);
        }
        
        $equipmentWithOngoing = $equipmentWithOngoingQuery->count();
        
        // Calculate idle equipment
        $idleEquipment = $totalEquipment - $equipmentWithOngoing;
        
        // Calculate percentage
        $equipmentPercentage = $totalEquipment > 0 ? round(($equipmentWithOngoing / $totalEquipment) * 100, 1) : 0;
        
        return [
            'total_equipment' => $totalEquipment,
            'equipment_with_ongoing' => $equipmentWithOngoing,
            'idle_equipment' => $idleEquipment,
            'equipment_percentage' => $equipmentPercentage
        ];
    }
    
    /**
     * API endpoint for updating dashboard stats via AJAX
     */
    public function updateDashboardStats(Request $request)
    {
        try {
            $filters = [
                'dashboard_date' => $request->get('dashboard_date', today()->format('Y-m-d')),
                'dashboard_shift' => $request->get('dashboard_shift', 'all'),
                'dashboard_cutoff' => $request->get('dashboard_cutoff', 'all'),
                'dashboard_work_type' => $request->get('dashboard_work_type', 'all'),
                'dashboard_alloc_type' => $request->get('dashboard_alloc_type', 'all')
            ];
            
            $stats = $this->getEndtimeDashboardStats($filters);
            $perLineSummary = $this->getPerLineSummary($filters);
            $perSizeSummary = $this->getPerSizeSummary($filters);
            $linePerformanceAnalysis = $this->getLinePerformanceAnalysis($filters);
            
            // Get dynamic panel data
            $previousShiftAchievement = $this->getPreviousShiftAchievement($filters);
            // Live Performance Monitor always uses TODAY's data regardless of filter date
            $currentPerformanceMonitor = $this->getCurrentPerformanceMonitor($filters);
            
            return response()->json([
                'success' => true,
                'stats' => $stats,
                'perLineSummary' => $perLineSummary,
                'perSizeSummary' => $perSizeSummary,
                'linePerformanceAnalysis' => $linePerformanceAnalysis,
                'previousShiftAchievement' => $previousShiftAchievement,
                'currentPerformanceMonitor' => $currentPerformanceMonitor
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update dashboard statistics: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * API endpoint for getting line-specific area performance
     */
    public function getLineAreaPerformanceData(Request $request)
    {
        try {
            $line = $request->get('line', 'A');
            $filters = [
                'dashboard_date' => $request->get('dashboard_date', today()->format('Y-m-d')),
                'dashboard_shift' => $request->get('dashboard_shift', 'all'),
                'dashboard_cutoff' => $request->get('dashboard_cutoff', 'all'),
                'dashboard_work_type' => $request->get('dashboard_work_type', 'all'),
                'dashboard_alloc_type' => $request->get('dashboard_alloc_type', 'all')
            ];
            
            $areaPerformance = $this->getLineAreaPerformance($line, $filters);
            
            // Add debugging information
            $debugInfo = $this->getLineAreaDebugInfo($line, $filters);
            
            return response()->json([
                'success' => true,
                'line' => $line,
                'area_performance' => $areaPerformance,
                'debug_info' => $debugInfo
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get line area performance: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * API endpoint for manufacturing overview chart data - OPTIMIZED with caching
     */
    public function getManufacturingOverviewData(Request $request)
    {
        try {
            // Get dashboard filters from request (same as dashboard cards)
            $filters = [
                'dashboard_date' => $request->get('dashboard_date', today()->format('Y-m-d')),
                'dashboard_shift' => $request->get('dashboard_shift', 'all'),
                'dashboard_cutoff' => $request->get('dashboard_cutoff', 'all'),
                'dashboard_work_type' => $request->get('dashboard_work_type', 'all'),
                'dashboard_alloc_type' => $request->get('dashboard_alloc_type', 'all')
            ];
            
            // Create cache key based on filters (cache for 2 minutes)
            $cacheKey = 'manufacturing_overview_' . md5(json_encode($filters));
            
            $chartData = \Cache::remember($cacheKey, 120, function() use ($filters) {
                return $this->generateManufacturingChartDataFromFilters($filters);
            });
            
            return response()->json([
                'success' => true,
                'data' => $chartData,
                'cached_at' => now()->toISOString()
            ]);
        } catch (\Exception $e) {
            \Log::error('Manufacturing overview API error: ' . $e->getMessage(), [
                'filters' => $filters ?? null,
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to get manufacturing overview data: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Generate manufacturing chart data distributed by production lines (using filters) - OPTIMIZED
     */
    private function generateManufacturingChartDataFromFilters($filters)
    {
        $targetCapacity = [];
        $totalEndtime = [];
        $submittedLots = [];
        $remainingLots = [];
        $labels = [];
        
        // Production lines A through K
        $productionLines = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K'];
        
        // OPTIMIZATION: Fetch all equipment data at once instead of line by line
        $allEquipmentData = $this->getAllEquipmentDataOptimized($filters);
        $allEndtimeData = $this->getAllEndtimeDataOptimized($filters);
        
        foreach ($productionLines as $line) {
            $labels[] = "Line {$line}";
            
            // Get pre-fetched data for this specific line
            $lineEquipment = $allEquipmentData[$line] ?? [];
            $lineEndtime = $allEndtimeData[$line] ?? ['total' => 0, 'submitted' => 0];
            
            // Calculate target capacity for this line
            $lineTargetCapacity = $this->calculateLineTargetCapacity($lineEquipment, $filters);
            
            // Target Capacity for this line (Card 1 data distributed by line)
            $targetCapacity[] = round($lineTargetCapacity / 1000000, 1); // Convert to millions
            
            // Total Endtime for this line (Card 2 data distributed by line)
            $totalEndtime[] = round($lineEndtime['total'] / 1000000, 1); // Convert to millions
            
            // Submitted Lots for this line (Card 3 data distributed by line)
            $submittedLots[] = round($lineEndtime['submitted'] / 1000000, 1); // Convert to millions
            
            // Remaining Lots for this line (Card 4 data distributed by line)
            $remainingLots[] = round(($lineEndtime['total'] - $lineEndtime['submitted']) / 1000000, 1); // Convert to millions
        }
        
        // Calculate totals for verification
        $totalTargetCapacity = array_sum($targetCapacity) * 1000000; // Convert back to PCS for comparison
        $totalTotalEndtime = array_sum($totalEndtime) * 1000000;
        $totalSubmittedLots = array_sum($submittedLots) * 1000000;
        $totalRemainingLots = array_sum($remainingLots) * 1000000;
        
        // Log totals for debugging
        \Log::info('Chart Totals vs Dashboard Cards:', [
            'Target Capacity' => number_format($totalTargetCapacity) . ' PCS',
            'Total Endtime' => number_format($totalTotalEndtime) . ' PCS',
            'Submitted Lots' => number_format($totalSubmittedLots) . ' PCS',
            'Remaining Lots' => number_format($totalRemainingLots) . ' PCS',
        ]);
        
        return [
            'labels' => $labels,
            'series' => [
                [
                    'name' => 'Target',
                    'type' => 'area',
                    'data' => array_map(function($label, $value) {
                        return ['x' => $label, 'y' => $value];
                    }, $labels, $targetCapacity)
                ],
                [
                    'name' => 'Endtime',
                    'type' => 'bar',
                    'data' => array_map(function($label, $value) {
                        return ['x' => $label, 'y' => $value];
                    }, $labels, $totalEndtime)
                ],
                [
                    'name' => 'Submitted',
                    'type' => 'bar',
                    'chart' => [
                        'dropShadow' => [
                            'enabled' => true,
                            'enabledOnSeries' => null,
                            'top' => 5,
                            'left' => 0,
                            'blur' => 3,
                            'color' => '#000',
                            'opacity' => 0.1,
                        ],
                    ],
                    'data' => array_map(function($label, $value) {
                        return ['x' => $label, 'y' => $value];
                    }, $labels, $submittedLots)
                ],
                [
                    'name' => 'Remaining',
                    'type' => 'bar',
                    'data' => array_map(function($label, $value) {
                        return ['x' => $label, 'y' => $value];
                    }, $labels, $remainingLots)
                ]
            ],
            'totals_debug' => [
                'target_capacity' => $totalTargetCapacity,
                'total_endtime' => $totalTotalEndtime,
                'submitted_lots' => $totalSubmittedLots,
                'remaining_lots' => $totalRemainingLots
            ]
        ];
    }
    
    /**
     * Get data for a specific production line based on dashboard filters
     */
    private function getLineDataByFilters($line, $filters)
    {
        // Extract filters
        $currentDate = $filters['dashboard_date'] ?? today()->format('Y-m-d');
        $currentShift = $filters['dashboard_shift'] ?? 'all';
        $currentCutoff = $filters['dashboard_cutoff'] ?? 'all';
        $workType = $filters['dashboard_work_type'] ?? 'all';
        $allocType = $filters['dashboard_alloc_type'] ?? 'all';
        
        // Get equipment for this line that matches the alloc type filter (OPERATIONAL, PLANSTOP, IDDLE status)
        $equipmentQuery = Equipment::where('eqp_line', $line)
            ->whereIn('eqp_status', ['OPERATIONAL', 'PLANSTOP', 'IDDLE']);
        
        // Apply alloc_type filter if specified
        if (isset($filters['dashboard_alloc_type']) && $allocType !== 'all') {
            $equipmentQuery->where('alloc_type', $allocType);
        }
        
        $equipment = $equipmentQuery->get();
        
        // Calculate target capacity for this line (same method as dashboard cards)
        $targetCapacity = 0;
        
        // Calculate time multiplier based on shift and cutoff selection (same as dashboard cards)
        $timeMultiplier = $this->getTimeMultiplier($currentShift, $currentCutoff);
        
        foreach ($equipment as $eq) {
            $oee = floatval($eq->eqp_oee) ?: 0;
            $speed = floatval(str_replace(',', '', $eq->loading_speed)) ?: 0;
            $operationTime = floatval(str_replace(',', '', $eq->operation_time)) ?: 0;
            
            // Calculate capacity based on time period: OEE × Speed × Operation Time × Time Multiplier
            $periodCapacity = round($oee * $speed * $operationTime * $timeMultiplier);
            $targetCapacity += $periodCapacity;
        }
        
        // Get endtime data for this line using the SAME filters as dashboard cards
        $endtimeQuery = $this->getLineEndtimeQuery($line, $currentDate, $currentShift, $currentCutoff, $workType);
        
        // Total endtime for this line (same as card 2)
        $totalEndtime = $endtimeQuery->sum('lot_qty') ?: 0;
        
        // Submitted lots for this line (same as card 3)
        $submittedQuery = clone $endtimeQuery;
        $submittedLots = $submittedQuery->where('status', 'Submitted')->sum('lot_qty') ?: 0;
        
        // Remaining lots for this line (same as card 4)
        $remainingLots = $totalEndtime - $submittedLots;
        
        return [
            'target_capacity' => $targetCapacity,
            'total_endtime' => $totalEndtime,
            'submitted_lots' => $submittedLots,
            'remaining_lots' => $remainingLots
        ];
    }
    
    /**
     * Get endtime query for specific line using SAME filters as dashboard cards
     */
    private function getLineEndtimeQuery($line, $date, $shift, $cutoff, $workType)
    {
        // Base endtime query for this line (endtime table has eqp_line column directly)
        $query = Endtime::where('eqp_line', $line);
        
        // Apply the EXACT SAME time filter logic as dashboard cards
        $this->applyDashboardTimeFilter($query, $date, $shift, $cutoff);
        
        // Apply work type filter if needed
        if ($workType !== 'all') {
            $query->where('work_type', $workType);
        }
        
        return $query;
    }
    
    /**
     * Get monthly equipment capacity
     */
    private function getMonthlyCapacity($month, $allocType)
    {
        $equipmentQuery = Equipment::whereIn('eqp_status', ['OPERATIONAL', 'PLANSTOP', 'IDDLE']);
        
        // Apply alloc_type filter if specified
        if ($allocType !== 'all') {
            $equipmentQuery->where('alloc_type', $allocType);
        }

        $equipment = $equipmentQuery->get();
        $totalCapacity = 0;
        
        foreach ($equipment as $eq) {
            $oee = floatval($eq->eqp_oee) ?: 0;
            $speed = floatval(str_replace(',', '', $eq->loading_speed)) ?: 0;
            $operationTime = floatval(str_replace(',', '', $eq->operation_time)) ?: 0;
            
            // Calculate monthly capacity (30 days average): OEE × Speed × Operation Time × 30
            $monthlyCapacity = round($oee * $speed * $operationTime * 30);
            $totalCapacity += $monthlyCapacity;
        }
        
        return $totalCapacity;
    }
    
    /**
     * Get monthly total endtime (all lots)
     */
    private function getMonthlyTotalEndtime($month, $workType)
    {
        $query = Endtime::whereMonth('est_endtime', $month->month)
                        ->whereYear('est_endtime', $month->year);
                        
        if ($workType !== 'all') {
            $query->where('work_type', $workType);
        }
        
        return $query->sum('lot_qty') ?: 0;
    }
    
    /**
     * Get monthly submitted endtime
     */
    private function getMonthlySubmittedEndtime($month, $workType)
    {
        $query = Endtime::whereMonth('actual_submitted_at', $month->month)
                        ->whereYear('actual_submitted_at', $month->year)
                        ->where('status', 'Submitted');

        if ($workType !== 'all') {
            $query->where('work_type', $workType);
        }

        return $query->sum('lot_qty') ?: 0;
    }
    
    /**
     * Get monthly completion rate
     */
    private function getMonthlyCompletionRate($month, $workType)
    {
        $totalQuery = Endtime::whereMonth('est_endtime', $month->month)
                             ->whereYear('est_endtime', $month->year);

        $submittedQuery = Endtime::whereMonth('actual_submitted_at', $month->month)
                                 ->whereYear('actual_submitted_at', $month->year)
                                 ->where('status', 'Submitted');

        if ($workType !== 'all') {
            $totalQuery->where('work_type', $workType);
            $submittedQuery->where('work_type', $workType);
        }

        $total = $totalQuery->count();
        $submitted = $submittedQuery->count();

        return $total > 0 ? ($submitted / $total) * 100 : 0;
    }
    
    /**
     * OPTIMIZATION: Get all equipment data grouped by line in a single query
     */
    private function getAllEquipmentDataOptimized($filters)
    {
        $allocType = $filters['dashboard_alloc_type'] ?? 'all';
        
        $equipmentQuery = Equipment::whereIn('eqp_status', ['OPERATIONAL', 'PLANSTOP', 'IDDLE'])
                                  ->select('eqp_line', 'eqp_oee', 'loading_speed', 'operation_time');
        
        // Apply alloc_type filter if specified
        if ($allocType !== 'all') {
            $equipmentQuery->where('alloc_type', $allocType);
        }
        
        $allEquipment = $equipmentQuery->get();
        
        // Group by line
        $groupedEquipment = [];
        foreach ($allEquipment as $equipment) {
            $line = $equipment->eqp_line;
            if (!isset($groupedEquipment[$line])) {
                $groupedEquipment[$line] = [];
            }
            $groupedEquipment[$line][] = $equipment;
        }
        
        return $groupedEquipment;
    }
    
    /**
     * OPTIMIZATION: Get all endtime data grouped by line in minimal queries
     */
    private function getAllEndtimeDataOptimized($filters)
    {
        $currentDate = $filters['dashboard_date'] ?? today()->format('Y-m-d');
        $currentShift = $filters['dashboard_shift'] ?? 'all';
        $currentCutoff = $filters['dashboard_cutoff'] ?? 'all';
        $workType = $filters['dashboard_work_type'] ?? 'all';
        
        // Base query with time and work type filters
        $baseQuery = Endtime::query();
        $this->applyDashboardTimeFilter($baseQuery, $currentDate, $currentShift, $currentCutoff);
        
        if ($workType !== 'all') {
            $baseQuery->where('work_type', $workType);
        }
        
        // Get all endtime data grouped by line and status in two queries
        $totalEndtimeByLine = (clone $baseQuery)
            ->select('eqp_line', DB::raw('SUM(lot_qty) as total_qty'))
            ->groupBy('eqp_line')
            ->pluck('total_qty', 'eqp_line')
            ->toArray();
            
        $submittedEndtimeByLine = (clone $baseQuery)
            ->where('status', 'Submitted')
            ->select('eqp_line', DB::raw('SUM(lot_qty) as submitted_qty'))
            ->groupBy('eqp_line')
            ->pluck('submitted_qty', 'eqp_line')
            ->toArray();
        
        // Combine the data
        $result = [];
        $productionLines = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K'];
        
        foreach ($productionLines as $line) {
            $result[$line] = [
                'total' => $totalEndtimeByLine[$line] ?? 0,
                'submitted' => $submittedEndtimeByLine[$line] ?? 0
            ];
        }
        
        return $result;
    }
    
    /**
     * OPTIMIZATION: Calculate target capacity for a line using pre-fetched equipment data
     */
    private function calculateLineTargetCapacity($lineEquipment, $filters)
    {
        $currentShift = $filters['dashboard_shift'] ?? 'all';
        $currentCutoff = $filters['dashboard_cutoff'] ?? 'all';
        
        $targetCapacity = 0;
        $timeMultiplier = $this->getTimeMultiplier($currentShift, $currentCutoff);
        
        foreach ($lineEquipment as $eq) {
            $oee = floatval($eq->eqp_oee) ?: 0;
            $speed = floatval(str_replace(',', '', $eq->loading_speed)) ?: 0;
            $operationTime = floatval(str_replace(',', '', $eq->operation_time)) ?: 0;
            
            // Calculate capacity based on time period: OEE × Speed × Operation Time × Time Multiplier
            $periodCapacity = round($oee * $speed * $operationTime * $timeMultiplier);
            $targetCapacity += $periodCapacity;
        }
        
        return $targetCapacity;
    }
    
    /**
     * Get daily equipment capacity
     */
    private function getDailyCapacity($date, $allocType)
    {
        $equipmentQuery = Equipment::whereIn('eqp_status', ['OPERATIONAL', 'PLANSTOP', 'IDDLE']);
        
        // Apply alloc_type filter if specified
        if ($allocType !== 'all') {
            $equipmentQuery->where('alloc_type', $allocType);
        }

        $equipment = $equipmentQuery->get();
        $totalCapacity = 0;
        
        foreach ($equipment as $eq) {
            $oee = floatval($eq->eqp_oee) ?: 0;
            $speed = floatval(str_replace(',', '', $eq->loading_speed)) ?: 0;
            $operationTime = floatval(str_replace(',', '', $eq->operation_time)) ?: 0;
            
            // Calculate daily capacity: OEE × Speed × Operation Time
            $dailyCapacity = round($oee * $speed * $operationTime);
            $totalCapacity += $dailyCapacity;
        }
        
        return $totalCapacity;
    }
    
    /**
     * Get daily total endtime (all lots)
     */
    private function getDailyTotalEndtime($date, $workType)
    {
        $query = Endtime::whereDate('est_endtime', $date->format('Y-m-d'));
                        
        if ($workType !== 'all') {
            $query->where('work_type', $workType);
        }
        
        return $query->sum('lot_qty') ?: 0;
    }
    
    /**
     * Get daily submitted endtime
     */
    private function getDailySubmittedEndtime($date, $workType)
    {
        $query = Endtime::whereDate('actual_submitted_at', $date->format('Y-m-d'))
                        ->where('status', 'Submitted');

        if ($workType !== 'all') {
            $query->where('work_type', $workType);
        }

        return $query->sum('lot_qty') ?: 0;
    }
    
    /**
     * Get daily completion rate
     */
    private function getDailyCompletionRate($date, $workType)
    {
        $totalQuery = Endtime::whereDate('est_endtime', $date->format('Y-m-d'));
        $submittedQuery = Endtime::whereDate('actual_submitted_at', $date->format('Y-m-d'))
                                 ->where('status', 'Submitted');

        if ($workType !== 'all') {
            $totalQuery->where('work_type', $workType);
            $submittedQuery->where('work_type', $workType);
        }

        $total = $totalQuery->count();
        $submitted = $submittedQuery->count();

        return $total > 0 ? ($submitted / $total) * 100 : 0;
    }
    
    /**
     * Get Per Line Summary data based on dashboard filters
     */
    private function getPerLineSummary($filters)
    {
        $lines = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K'];
        $summary = [
            'lines' => $lines,
            'target' => [],
            'endtime' => [],
            'submitted' => [],
            'submitted_percent' => [],
            'endtime_percent' => []
        ];
        
        foreach ($lines as $line) {
            $lineData = $this->getLineDataByFilters($line, $filters);
            
            // Target capacity in millions
            $targetCapacity = $lineData['target_capacity'];
            $summary['target'][$line] = $targetCapacity > 0 ? number_format($targetCapacity / 1000000, 0) . ' M' : '0 M';
            
            // Endtime (total lots) in millions
            $totalEndtime = $lineData['total_endtime'];
            $summary['endtime'][$line] = $totalEndtime > 0 ? number_format($totalEndtime / 1000000, 0) . ' M' : '0 M';
            
            // Submitted lots in millions
            $submittedLots = $lineData['submitted_lots'];
            $summary['submitted'][$line] = $submittedLots > 0 ? number_format($submittedLots / 1000000, 0) . ' M' : '0 M';
            
            // Calculate percentages
            $submittedPercent = $totalEndtime > 0 ? round(($submittedLots / $totalEndtime) * 100, 1) : 0;
            $endtimePercent = $targetCapacity > 0 ? round(($totalEndtime / $targetCapacity) * 100, 1) : 0;
            
            $summary['submitted_percent'][$line] = $submittedPercent;
            $summary['endtime_percent'][$line] = $endtimePercent;
        }
        
        return $summary;
    }
    
    /**
     * Get Line Performance Analysis data with categorization
     * - Top Performers: Lines with 100% and above result
     * - Needs Attention: Worst 3 lines
     * - Average Performance: Below 100% excluding the worst 3 lines
     * - Each line has 4 areas
     */
    private function getLinePerformanceAnalysis($filters)
    {
        $lines = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K'];
        $performanceData = [];
        
        // Get shift context to sync header with Panel 1
        $shiftContext = $this->getShiftContext($filters);
        $currentContext = $shiftContext['current'];
        
        // Calculate performance for each line
        foreach ($lines as $line) {
            $lineData = $this->getLineDataByFilters($line, $filters);
            
            // Calculate performance percentage (submitted vs target)
            $targetCapacity = $lineData['target_capacity'];
            $submittedLots = $lineData['submitted_lots'];
            $totalEndtime = $lineData['total_endtime'];
            $performancePercent = $targetCapacity > 0 ? round(($submittedLots / $targetCapacity) * 100, 1) : 0;
            
            // Get area performance for this line (each line has 4 areas)
            $lineAreaPerformance = $this->getLineAreaPerformance($line, $filters);
            
            $performanceData[$line] = [
                'line' => $line,
                'target_capacity' => $targetCapacity,
                'target_formatted' => $targetCapacity > 0 ? number_format($targetCapacity / 1000000, 1) . 'M PCS' : '0 PCS',
                'result' => $submittedLots,
                'result_formatted' => $submittedLots > 0 ? number_format($submittedLots / 1000000, 1) . 'M PCS' : '0 PCS',
                'performance_percent' => $performancePercent,
                'area_performance' => $lineAreaPerformance, // 4 areas per line
                'is_active' => false // Will be set to true for selected line
            ];
        }
        
        // Sort by performance percentage (descending)
        uasort($performanceData, function($a, $b) {
            return $b['performance_percent'] <=> $a['performance_percent'];
        });
        
        // Categorize lines
        $topPerformers = [];
        $needsAttention = [];
        $averagePerformance = [];
        
        // Get lines with 100% and above (Top Performers)
        foreach ($performanceData as $line => $data) {
            if ($data['performance_percent'] >= 100) {
                $topPerformers[] = $data;
            }
        }
        
        // Get worst 3 lines (Needs Attention)
        $sortedByPerformance = array_values($performanceData);
        $needsAttention = array_slice(array_reverse($sortedByPerformance), 0, 3);
        
        // Get below 100% excluding worst 3 (Average Performance)
        $belowHundred = array_filter($performanceData, function($data) {
            return $data['performance_percent'] < 100;
        });
        
        // Remove worst 3 from below 100%
        $worstThreeLines = array_column($needsAttention, 'line');
        $averagePerformance = array_filter($belowHundred, function($data) use ($worstThreeLines) {
            return !in_array($data['line'], $worstThreeLines);
        });
        
        // Set default selected line (first in top performers, or first overall)
        $defaultSelectedLine = !empty($topPerformers) ? $topPerformers[0]['line'] : 'A';
        $defaultAreaPerformance = $performanceData[$defaultSelectedLine]['area_performance'];
        
        return [
            'top_performers' => array_values($topPerformers),
            'needs_attention' => array_values($needsAttention),
            'average_performance' => array_values($averagePerformance),
            'all_lines' => $performanceData,
            'selected_line' => $defaultSelectedLine,
            'area_performance' => $defaultAreaPerformance, // Default area performance
            // Dynamic header information synced with Panel 1
            'panel_title' => $this->generatePanel2Title($currentContext['title'] ?? 'Line Performance Analysis'),
            'panel_subtitle' => $this->generatePanel2Subtitle($currentContext['subtitle'] ?? 'Current Performance by Line & Area'),
            'period_desc' => $currentContext['period_desc'] ?? 'Current period'
        ];
    }
    
    /**
     * Calculate how much of a lot's quantity should be attributed to each area
     * based on equipment capacity contribution
     */
    private function calculateLotAreaAttribution($lotRecord, $filters)
    {
        $attributions = [];
        $equipmentUsed = [];
        
        // Collect all equipment used in this lot
        for ($i = 1; $i <= 10; $i++) {
            $equipmentNo = $lotRecord->{"eqp_{$i}"};
            if (!empty($equipmentNo)) {
                $equipmentUsed[] = $equipmentNo;
            }
        }
        
        if (empty($equipmentUsed)) {
            return []; // No equipment used
        }
        
        // Get equipment details with area information
        $equipmentDetails = Equipment::whereIn('eqp_no', $equipmentUsed)
                                     ->whereIn('eqp_status', ['OPERATIONAL', 'PLANSTOP', 'IDDLE']);
        
        // Apply alloc_type filter if specified
        if (isset($filters['dashboard_alloc_type']) && $filters['dashboard_alloc_type'] !== 'all') {
            $equipmentDetails->where('alloc_type', $filters['dashboard_alloc_type']);
        }
        
        $equipmentDetails = $equipmentDetails->select('eqp_no', 'eqp_line', 'eqp_area', 'eqp_oee', 'loading_speed', 'operation_time')
                                             ->get()
                                             ->keyBy('eqp_no');
        
        // Calculate capacity per area
        $areaCapacities = [];
        $totalCapacity = 0;
        
        foreach ($equipmentUsed as $eqpNo) {
            $equipment = $equipmentDetails[$eqpNo] ?? null;
            if (!$equipment) continue;
            
            $area = $equipment->eqp_area;
            $oee = floatval($equipment->eqp_oee) ?: 0;
            $speed = floatval(str_replace(',', '', $equipment->loading_speed)) ?: 0;
            $operationTime = floatval(str_replace(',', '', $equipment->operation_time)) ?: 0;
            
            // Calculate capacity using OEE × Speed × Operation Time formula
            $capacity = round($oee * $speed * $operationTime);
            
            if (!isset($areaCapacities[$area])) {
                $areaCapacities[$area] = 0;
            }
            $areaCapacities[$area] += $capacity;
            $totalCapacity += $capacity;
        }
        
        // Calculate attribution per area
        $lotQuantity = $lotRecord->lot_qty ?: 0;
        
        foreach ($areaCapacities as $area => $areaCapacity) {
            $attributionPercent = $totalCapacity > 0 ? ($areaCapacity / $totalCapacity) : 0;
            $attributions[$area] = [
                'quantity' => round($lotQuantity * $attributionPercent),
                'percentage' => round($attributionPercent * 100, 2),
                'capacity_contribution' => $areaCapacity
            ];
        }
        
        return $attributions;
    }
    
    /**
     * Calculate area performance for a specific line and area using capacity-based attribution
     */
    private function calculateAreaPerformance($line, $area, $filters)
    {
        // Get all lots that used equipment from this line
        $lotsQuery = Endtime::query();
        $this->applyDashboardTimeFilter($lotsQuery, $filters['dashboard_date'], 
                                       $filters['dashboard_shift'], $filters['dashboard_cutoff']);
        
        // Apply work type filter
        if ($filters['dashboard_work_type'] !== 'all') {
            $lotsQuery->where('work_type', $filters['dashboard_work_type']);
        }
        
        // Get lots that use any equipment from this line
        $lotsInLine = $lotsQuery->where('eqp_line', $line)->get();
        
        // Convert numeric area to equipment area format (1 -> F1, 2 -> F2, etc.)
        $equipmentArea = $line . $area; // e.g., "F" + "1" = "F1"
        
        $areaTarget = $this->getAreaTargetCapacity($line, $equipmentArea, $filters);
        $areaSubmittedQuantity = 0;
        
        foreach ($lotsInLine as $lot) {
            if ($lot->status !== 'Submitted') continue;
            
            // Calculate how much of this lot should be attributed to this area
            $lotAttributions = $this->calculateLotAreaAttribution($lot, $filters);
            
            // Check both numeric area and equipment area format
            if (isset($lotAttributions[$area])) {
                $areaSubmittedQuantity += $lotAttributions[$area]['quantity'];
            } elseif (isset($lotAttributions[$equipmentArea])) {
                $areaSubmittedQuantity += $lotAttributions[$equipmentArea]['quantity'];
            }
        }
        
        return $areaTarget > 0 ? round(($areaSubmittedQuantity / $areaTarget) * 100, 1) : 0;
    }
    
    /**
     * Get area target capacity for a specific line and area
     */
    private function getAreaTargetCapacity($line, $area, $filters)
    {
        $equipmentInArea = Equipment::where('eqp_line', $line)
                                    ->where('eqp_area', $area)
                                    ->whereIn('eqp_status', ['OPERATIONAL', 'PLANSTOP', 'IDDLE']);
        
        // Apply alloc_type filter if specified
        if (isset($filters['dashboard_alloc_type']) && $filters['dashboard_alloc_type'] !== 'all') {
            $equipmentInArea->where('alloc_type', $filters['dashboard_alloc_type']);
        }
        
        $equipment = $equipmentInArea->get();
        $timeMultiplier = $this->getTimeMultiplier($filters['dashboard_shift'], 
                                                  $filters['dashboard_cutoff']);
        
        $totalCapacity = 0;
        foreach ($equipment as $eq) {
            $oee = floatval($eq->eqp_oee) ?: 0;
            $speed = floatval(str_replace(',', '', $eq->loading_speed)) ?: 0;
            $operationTime = floatval(str_replace(',', '', $eq->operation_time)) ?: 0;
            
            // Calculate capacity based on time period: OEE × Speed × Operation Time × Time Multiplier
            $capacity = round($oee * $speed * $operationTime * $timeMultiplier);
            $totalCapacity += $capacity;
        }
        
        return $totalCapacity;
    }
    
    /**
     * Get area performance for a specific line (1 line = 4 areas)
     * Now uses real capacity-based calculation instead of simulation
     */
    private function getLineAreaPerformance($line, $filters)
    {
        $areaPerformance = [];
        
        // Calculate performance for each area (1-4) in the line
        for ($area = 1; $area <= 4; $area++) {
            $areaPerformance[$area] = $this->calculateAreaPerformance($line, $area, $filters);
        }
        
        // If all areas show 0% or very low values, provide fallback calculation
        $totalAreaPerformance = array_sum($areaPerformance);
        $allZeroOrVeryLow = $totalAreaPerformance < 0.1; // Less than 0.1% total
        
        if ($allZeroOrVeryLow) {
            $areaPerformance = $this->getFallbackAreaPerformance($line, $filters);
        }
        
        return $areaPerformance;
    }
    
    /**
     * Fallback area performance calculation when detailed tracking is unavailable
     * Distributes line performance evenly across areas or based on simple heuristics
     */
    private function getFallbackAreaPerformance($line, $filters)
    {
        // Get line overall performance
        $lineData = $this->getLineDataByFilters($line, $filters);
        $linePerformance = $lineData['target_capacity'] > 0 
            ? round(($lineData['submitted_lots'] / $lineData['target_capacity']) * 100, 1)
            : 0;
            
        // Check if we have equipment with area assignments (OPERATIONAL, PLANSTOP, IDDLE status)
        $equipmentQuery = Equipment::where('eqp_line', $line)
            ->whereIn('eqp_status', ['OPERATIONAL', 'PLANSTOP', 'IDDLE']);
        
        // Apply alloc_type filter if specified
        if (isset($filters['dashboard_alloc_type']) && $filters['dashboard_alloc_type'] !== 'all') {
            $equipmentQuery->where('alloc_type', $filters['dashboard_alloc_type']);
        }
        
        $equipment = $equipmentQuery->whereNotNull('eqp_area')->get();
        
        $areaPerformance = [];
        
        if ($equipment->count() > 0) {
            // Distribute based on equipment capacity per area
            $areaCapacities = [];
            $totalCapacity = 0;
            
            foreach ($equipment as $eq) {
                $rawArea = $eq->eqp_area;
                // Map equipment area (like "F1", "F2", "F3") to numeric area (1, 2, 3, 4)
                $area = $this->mapEquipmentAreaToNumericArea($rawArea, $line);
                
                if ($area > 0) { // Only process valid numeric areas
                    $oee = floatval($eq->eqp_oee) ?: 0;
                    $speed = floatval(str_replace(',', '', $eq->loading_speed)) ?: 0;
                    $operationTime = floatval(str_replace(',', '', $eq->operation_time)) ?: 0;
                    
                    // Calculate capacity using OEE × Speed × Operation Time formula
                    $equipmentCapacity = round($oee * $speed * $operationTime);
                    
                    if (!isset($areaCapacities[$area])) {
                        $areaCapacities[$area] = 0;
                    }
                    $areaCapacities[$area] += $equipmentCapacity;
                    $totalCapacity += $equipmentCapacity;
                }
            }
            
            // Distribute line performance proportionally to area capacity
            for ($area = 1; $area <= 4; $area++) {
                if (isset($areaCapacities[$area]) && $totalCapacity > 0) {
                    $areaRatio = $areaCapacities[$area] / $totalCapacity;
                    $areaPerformance[$area] = round($linePerformance * $areaRatio, 1);
                } else {
                    $areaPerformance[$area] = 0;
                }
            }
        } else {
            // If no equipment has area assignments, distribute evenly
            $evenDistribution = round($linePerformance / 4, 1);
            for ($area = 1; $area <= 4; $area++) {
                $areaPerformance[$area] = $evenDistribution;
            }
        }
        
        // Ensure we never return all zeros if line has performance
        $totalCalculated = array_sum($areaPerformance);
        if ($totalCalculated == 0 && $linePerformance > 0) {
            // Last resort: distribute evenly
            $evenDistribution = round($linePerformance / 4, 1);
            for ($area = 1; $area <= 4; $area++) {
                $areaPerformance[$area] = $evenDistribution;
            }
        }
        
        return $areaPerformance;
    }
    
    /**
     * Map equipment area names to numeric areas (1-4)
     */
    private function mapEquipmentAreaToNumericArea($equipmentArea, $line)
    {
        if (empty($equipmentArea)) {
            return 0;
        }
        
        // Handle different area naming patterns
        if (is_numeric($equipmentArea)) {
            return (int) $equipmentArea;
        }
        
        // Pattern: F1, F2, F3 -> 1, 2, 3
        if (preg_match('/^[A-Z]([1-4])$/', $equipmentArea, $matches)) {
            return (int) $matches[1];
        }
        
        // Pattern: Area1, Area2, etc.
        if (preg_match('/Area([1-4])/', $equipmentArea, $matches)) {
            return (int) $matches[1];
        }
        
        // Default mapping based on common patterns
        $areaMap = [
            'F1' => 1, 'F2' => 2, 'F3' => 3, 'F4' => 4,
            'A1' => 1, 'A2' => 2, 'A3' => 3, 'A4' => 4,
            'B1' => 1, 'B2' => 2, 'B3' => 3, 'B4' => 4,
            'C1' => 1, 'C2' => 2, 'C3' => 3, 'C4' => 4,
            'D1' => 1, 'D2' => 2, 'D3' => 3, 'D4' => 4,
            'E1' => 1, 'E2' => 2, 'E3' => 3, 'E4' => 4,
            'G1' => 1, 'G2' => 2, 'G3' => 3, 'G4' => 4,
            'H1' => 1, 'H2' => 2, 'H3' => 3, 'H4' => 4,
            'I1' => 1, 'I2' => 2, 'I3' => 3, 'I4' => 4,
            'J1' => 1, 'J2' => 2, 'J3' => 3, 'J4' => 4,
            'K1' => 1, 'K2' => 2, 'K3' => 3, 'K4' => 4,
        ];
        
        return $areaMap[$equipmentArea] ?? 0;
    }
    
    /**
     * Get debugging information for line area performance
     */
    private function getLineAreaDebugInfo($line, $filters)
    {
        // Check equipment in this line (only OPERATIONAL status)
        $equipmentQuery = Equipment::where('eqp_line', $line)
            ->whereIn('eqp_status', ['OPERATIONAL', 'PLANSTOP', 'IDDLE']);
        // Note: work_type filter removed as it doesn't exist in equipment table
        // Work type filtering is handled at the endtime level
        $equipment = $equipmentQuery->get(['eqp_no', 'eqp_line', 'eqp_area', 'eqp_oee', 'loading_speed', 'operation_time', 'alloc_type']);
        
        // Check lots for this line
        $lotsQuery = Endtime::query();
        $this->applyDashboardTimeFilter($lotsQuery, $filters['dashboard_date'], 
                                       $filters['dashboard_shift'], $filters['dashboard_cutoff']);
        if ($filters['dashboard_work_type'] !== 'all') {
            $lotsQuery->where('work_type', $filters['dashboard_work_type']);
        }
        $lots = $lotsQuery->where('eqp_line', $line)->get(['id', 'lot_id', 'lot_qty', 'status', 'eqp_line', 'work_type', 'eqp_1', 'eqp_2', 'eqp_3', 'eqp_4', 'eqp_5', 'eqp_6', 'eqp_7', 'eqp_8', 'eqp_9', 'eqp_10']);
        
        // Get area target capacities
        $areaTargets = [];
        for ($area = 1; $area <= 4; $area++) {
            $areaTargets[$area] = $this->getAreaTargetCapacity($line, $area, $filters);
        }
        
        // Get submitted lots count
        $submittedLots = $lots->where('status', 'Submitted');
        
        return [
            'equipment_count' => $equipment->count(),
            'equipment_by_area' => $equipment->groupBy('eqp_area')->map->count(),
            'total_lots' => $lots->count(),
            'submitted_lots' => $submittedLots->count(),
            'area_targets' => $areaTargets,
            'equipment_sample' => $equipment->take(5)->toArray(),
            'lots_sample' => $lots->take(3)->toArray(),
            'submitted_lots_sample' => $submittedLots->take(3)->toArray(),
            'filters' => $filters
        ];
    }
    
    /**
     * Generate Panel 2 title from Panel 1 title
     */
    private function generatePanel2Title($panel1Title)
    {
        // Transform Panel 1 title to Panel 2 title
        $transformations = [
            'Full Day Performance Monitor' => 'Full Day Line Performance Analysis',
            'Day Shift Performance Monitor' => 'Day Shift Line Performance Analysis', 
            'Night Shift Performance Monitor' => 'Night Shift Line Performance Analysis',
            'Day Shift 1st Cutoff Monitor' => 'Day Shift 1st Cutoff Line Analysis',
            'Day Shift 2nd Cutoff Monitor' => 'Day Shift 2nd Cutoff Line Analysis',
            'Day Shift 3rd Cutoff Monitor' => 'Day Shift 3rd Cutoff Line Analysis',
            'Night Shift 1st Cutoff Monitor' => 'Night Shift 1st Cutoff Line Analysis',
            'Night Shift 2nd Cutoff Monitor' => 'Night Shift 2nd Cutoff Line Analysis',
            'Night Shift 3rd Cutoff Monitor' => 'Night Shift 3rd Cutoff Line Analysis'
        ];
        
        // Check for exact matches first
        if (isset($transformations[$panel1Title])) {
            return $transformations[$panel1Title];
        }
        
        // Fallback transformations for any variations
        return str_replace(
            ['Performance Monitor', 'Monitor', 'Achievement'], 
            ['Line Performance Analysis', 'Line Analysis', 'Line Analysis'], 
            $panel1Title
        );
    }
    
    /**
     * Generate Panel 2 subtitle from Panel 1 subtitle
     */
    private function generatePanel2Subtitle($panel1Subtitle)
    {
        // Transform Panel 1 subtitle to Panel 2 subtitle for line performance context
        $transformations = [
            'Complete Day Analysis' => 'Performance by Line & Area',
            'Day Shift Progress' => 'Day Shift Performance by Line & Area',
            'Complete Day Results' => 'Line Performance Results by Area',
            'Day Shift Results' => 'Day Shift Line Performance by Area',
            'Night Shift Results' => 'Night Shift Line Performance by Area'
        ];
        
        // Check for exact matches in the subtitle
        foreach ($transformations as $search => $replace) {
            if (strpos($panel1Subtitle, $search) !== false) {
                return str_replace($search, $replace, $panel1Subtitle);
            }
        }
        
        // Fallback transformations
        $fallbackTransforms = [
            'Achievement' => 'Performance by Line & Area',
            'Progress' => 'Performance by Line & Area',
            'Results' => 'Line Performance by Area',
            'Analysis' => 'Performance by Line & Area'
        ];
        
        foreach ($fallbackTransforms as $search => $replace) {
            if (strpos($panel1Subtitle, $search) !== false) {
                return str_replace($search, $replace, $panel1Subtitle);
            }
        }
        
        // Default fallback
        return $panel1Subtitle . ' - Performance by Line & Area';
    }
    
    /**
     * Get dynamic shift context for the dashboard panels
     */
    private function getShiftContext($filters)
    {
        $currentDate = Carbon::parse($filters['dashboard_date'] ?? today()->format('Y-m-d'));
        $currentShift = $filters['dashboard_shift'] ?? 'all';
        $currentCutoff = $filters['dashboard_cutoff'] ?? 'all';
        
        // Determine current period context
        $currentContext = $this->getCurrentPeriodContext($currentDate, $currentShift, $currentCutoff);
        
        // Determine previous period context
        $previousContext = $this->getPreviousPeriodContext($currentDate, $currentShift, $currentCutoff);
        
        return [
            'current' => $currentContext,
            'previous' => $previousContext
        ];
    }
    
    /**
     * Get current period context based on filters
     */
    private function getCurrentPeriodContext($date, $shift, $cutoff)
    {
        $context = [
            'date' => $date->format('Y-m-d'),
            'shift' => $shift,
            'cutoff' => $cutoff,
            'title' => '',
            'subtitle' => '',
            'period_desc' => ''
        ];
        
        if ($shift === 'all') {
            $context['title'] = 'Full Day Performance Monitor';
            $context['subtitle'] = $date->format('M j, Y') . ' - Complete Day Analysis';
            $context['period_desc'] = 'Full 24-hour period';
        } elseif ($shift === 'day') {
            if ($cutoff === 'all') {
                $context['title'] = 'Day Shift Performance Monitor';
                $context['subtitle'] = $date->format('M j, Y') . ' - Day Shift Progress';
                $context['period_desc'] = '07:00 AM - 07:00 PM (12 hours)';
            } else {
                $cutoffNames = ['1' => '1st', '2' => '2nd', '3' => '3rd'];
                $cutoffTimes = [
                    '1' => '07:00 AM - 12:00 PM',
                    '2' => '12:00 PM - 04:00 PM', 
                    '3' => '04:00 PM - 07:00 PM'
                ];
                $context['title'] = 'Day Shift ' . ($cutoffNames[$cutoff] ?? '') . ' Cutoff Monitor';
                $context['subtitle'] = $date->format('M j, Y') . ' - ' . ($cutoffTimes[$cutoff] ?? '');
                $context['period_desc'] = $cutoffTimes[$cutoff] ?? '';
            }
        } elseif ($shift === 'night') {
            if ($cutoff === 'all') {
                $context['title'] = 'Night Shift Performance Monitor';
                $context['subtitle'] = $date->format('M j, Y') . ' 07:00 PM ~ ' . $date->copy()->addDay()->format('M j, Y') . ' 06:59 AM';
                $context['period_desc'] = '07:00 PM - 07:00 AM (12 hours)';
            } else {
                $cutoffNames = ['1' => '1st', '2' => '2nd', '3' => '3rd'];
                $nextDay = $date->copy()->addDay();
                
                // Generate proper date ranges for night shift cutoffs
                $cutoffDetails = [
                    '1' => [
                        'time' => '07:00 PM - 11:59 PM',
                        'subtitle' => $date->format('M j, Y') . ' - 07:00 PM - 11:59 PM'
                    ],
                    '2' => [
                        'time' => '12:00 AM - 03:59 AM', 
                        'subtitle' => $nextDay->format('M j, Y') . ' - 12:00 AM - 03:59 AM'
                    ],
                    '3' => [
                        'time' => '04:00 AM - 06:59 AM',
                        'subtitle' => $nextDay->format('M j, Y') . ' - 04:00 AM - 06:59 AM'
                    ]
                ];
                
                $context['title'] = 'Night Shift ' . ($cutoffNames[$cutoff] ?? '') . ' Cutoff Monitor';
                $context['subtitle'] = $cutoffDetails[$cutoff]['subtitle'] ?? ($date->format('M j, Y') . ' - Night Cutoff');
                $context['period_desc'] = $cutoffDetails[$cutoff]['time'] ?? '';
            }
        }
        
        return $context;
    }
    
    /**
     * Get previous period context based on current filters
     */
    private function getPreviousPeriodContext($date, $shift, $cutoff)
    {
        $context = [
            'date' => '',
            'shift' => '',
            'cutoff' => '',
            'title' => '',
            'subtitle' => '',
            'period_desc' => ''
        ];
        
        if ($shift === 'all') {
            // Previous day
            $prevDate = $date->copy()->subDay();
            $context['date'] = $prevDate->format('Y-m-d');
            $context['shift'] = 'all';
            $context['cutoff'] = 'all';
            $context['title'] = 'Previous Day Achievement';
            $context['subtitle'] = $prevDate->format('M j, Y') . ' - Complete Day Results';
            $context['period_desc'] = 'Full 24-hour period';
        } elseif ($shift === 'day') {
            if ($cutoff === 'all') {
                // Previous night shift (same date)
                $context['date'] = $date->format('Y-m-d');
                $context['shift'] = 'night';
                $context['cutoff'] = 'all';
                $context['title'] = 'Previous Night Shift Achievement';
                $context['subtitle'] = $date->format('M j, Y') . ' 07:00 PM ~ ' . $date->copy()->addDay()->format('M j, Y') . ' 06:59 AM';
                $context['period_desc'] = 'Night shift (12 hours)';
            } else {
                // Previous cutoff period
                $prevCutoff = $this->getPreviousCutoff('day', $cutoff);
                if ($prevCutoff['cutoff'] === '3' && $cutoff === '1') {
                    // Previous day's 3rd cutoff
                    $context['date'] = $date->copy()->subDay()->format('Y-m-d');
                } else {
                    $context['date'] = $date->format('Y-m-d');
                }
                $context['shift'] = $prevCutoff['shift'];
                $context['cutoff'] = $prevCutoff['cutoff'];
                $context['title'] = 'Previous ' . $prevCutoff['title'] . ' Achievement';
                $context['subtitle'] = $prevCutoff['period_desc'];
                $context['period_desc'] = $prevCutoff['period_desc'];
            }
        } elseif ($shift === 'night') {
            if ($cutoff === 'all') {
                // Previous day shift
                $context['date'] = $date->format('Y-m-d');
                $context['shift'] = 'day';
                $context['cutoff'] = 'all';
                $context['title'] = 'Previous Day Shift Achievement';
                $context['subtitle'] = $date->format('M j, Y') . ' 07:00 AM ~ 07:00 PM';
                $context['period_desc'] = 'Day shift (12 hours)';
            } else {
                // Previous cutoff period
                $prevCutoff = $this->getPreviousCutoff('night', $cutoff);
                $context['date'] = $date->format('Y-m-d');
                $context['shift'] = $prevCutoff['shift'];
                $context['cutoff'] = $prevCutoff['cutoff'];
                $context['title'] = 'Previous ' . $prevCutoff['title'] . ' Achievement';
                $context['subtitle'] = $prevCutoff['period_desc'];
                $context['period_desc'] = $prevCutoff['period_desc'];
            }
        }
        
        return $context;
    }
    
    /**
     * Get previous cutoff period details
     */
    private function getPreviousCutoff($shift, $cutoff)
    {
        $cutoffSequence = [
            'day' => [
                '1' => ['shift' => 'night', 'cutoff' => '3', 'title' => 'Night 3rd Cutoff', 'period_desc' => '04:00 AM - 07:00 AM'],
                '2' => ['shift' => 'day', 'cutoff' => '1', 'title' => 'Day 1st Cutoff', 'period_desc' => '07:00 AM - 12:00 PM'],
                '3' => ['shift' => 'day', 'cutoff' => '2', 'title' => 'Day 2nd Cutoff', 'period_desc' => '12:00 PM - 04:00 PM']
            ],
            'night' => [
                '1' => ['shift' => 'day', 'cutoff' => '3', 'title' => 'Day 3rd Cutoff', 'period_desc' => '04:00 PM - 07:00 PM'],
                '2' => ['shift' => 'night', 'cutoff' => '1', 'title' => 'Night 1st Cutoff', 'period_desc' => '07:00 PM - 12:00 AM'],
                '3' => ['shift' => 'night', 'cutoff' => '2', 'title' => 'Night 2nd Cutoff', 'period_desc' => '12:00 AM - 04:00 AM']
            ]
        ];
        
        return $cutoffSequence[$shift][$cutoff] ?? [
            'shift' => $shift,
            'cutoff' => '1', 
            'title' => ucfirst($shift) . ' Shift',
            'period_desc' => 'Previous period'
        ];
    }
    
    /**
     * Get current filter period achievement data (Left Panel)
     */
    private function getPreviousShiftAchievement($filters)
    {
        $shiftContext = $this->getShiftContext($filters);
        $currentContext = $shiftContext['current'];
        
        // Use CURRENT filter data (not previous)
        $currentStats = $this->getEndtimeDashboardStats($filters);
        
        // Calculate achievement percentage
        $achievementPercent = $currentStats['target_capacity'] > 0 
            ? round(($currentStats['submitted_quantity'] / $currentStats['target_capacity']) * 100, 1)
            : 0;
        
        // Determine status
        $status = 'Below Target';
        $statusClass = 'warning';
        $gapText = '';
        $impact = 'Monitor progress closely';
        
        if ($achievementPercent >= 100) {
            $status = 'Target Met';
            $statusClass = 'success';
            $gapText = round($achievementPercent - 100, 1) . '% above target capacity';
            $impact = 'Excellent performance achieved';
        } elseif ($achievementPercent >= 90) {
            $status = 'Near Target';
            $statusClass = 'warning';
            $gapText = round(100 - $achievementPercent, 1) . '% below target capacity';
            $impact = 'Close to target - minor improvement needed';
        } else {
            $gapText = round(100 - $achievementPercent, 1) . '% below target capacity';
            $impact = 'Significant improvement required';
        }
        
        return [
            'title' => $currentContext['title'] . ' Achievement',
            'subtitle' => $currentContext['subtitle'],
            'period_desc' => $currentContext['period_desc'],
            'achievement_percent' => $achievementPercent,
            'target_pcs' => number_format($currentStats['target_capacity'] / 1000000, 1) . 'M',
            'actual_pcs' => number_format($currentStats['submitted_quantity'] / 1000000, 1) . 'M',
            // Total Endtime = Total Quantity (Ongoing + Submitted) in millions
            'total_endtime' => number_format($currentStats['total_quantity'] / 1000000, 1) . 'M',
            // Remaining Lots = Ongoing Lots count
            'remaining_lots' => $currentStats['ongoing_lots'],
            // Keep original data for backward compatibility
            'total_hours' => $this->calculatePeriodHours($currentContext['shift'], $currentContext['cutoff']),
            'lines_active' => $this->getActiveLinesCount($filters),
            'status' => $status,
            'status_class' => $statusClass,
            'gap_text' => $gapText,
            'impact' => $impact
        ];
    }
    
    /**
     * Get current performance monitor data (Right Panel) - Always shows TODAY's full day progress
     */
    private function getCurrentPerformanceMonitor($filters)
    {
        // Always use TODAY's date for Live Performance Monitor (not the filter date)
        $todayDate = today();
        
        // Create TODAY's full day filters (always 00:00-23:59 of TODAY)
        // Only connect to Type filter, ignore other filters
        $todayFilters = [
            'dashboard_date' => $todayDate->format('Y-m-d'),
            'dashboard_shift' => 'all', // Always full day
            'dashboard_cutoff' => 'all',
            'dashboard_work_type' => $filters['dashboard_work_type'] ?? 'all',
            'dashboard_alloc_type' => $filters['dashboard_alloc_type'] ?? 'all' // Include alloc_type filter
        ];
        
        // Get TODAY's full day stats
        $todayStats = $this->getEndtimeDashboardStats($todayFilters);
        
        // Calculate accurate progress percentage for TODAY's full day (time-based)
        $timeProgress = $this->calculateFullDayProgress($todayDate);
        
        // Calculate production progress percentage (actual vs target)
        $productionProgress = $todayStats['target_capacity'] > 0 
            ? round(($todayStats['submitted_quantity'] / $todayStats['target_capacity']) * 100, 1)
            : 0;
        
        // Get equipment running count (with ongoing lots)
        $equipmentRunning = $this->getEquipmentRunningCount($todayFilters);
        
        // Get actual submitted results in proper format
        $actualResults = $this->getActualResults($todayFilters);
        
        // Calculate new metrics
        $idealProgressQty = $this->calculateIdealProgressQty($todayStats, $timeProgress);
        $actualProgressQty = $actualResults['raw']; // Submitted quantity
        $recoveryQty = $this->calculateRecoveryQty($idealProgressQty, $actualProgressQty);
        
        // Progress Rate = Production Progress (not time-based efficiency)
        $progressRate = $productionProgress;
            
        $vsTarget = $todayStats['target_capacity'] > 0 
            ? round((($todayStats['submitted_quantity'] / $todayStats['target_capacity']) * 100) - 100, 1)
            : 0;
        $timeRemaining = $this->calculateFullDayRemainingTime($todayDate);
        
        // Get best and worst performing lines for TODAY's full day
        $linePerformance = $this->getTopAndWorstLines($todayFilters);
        
        // Generate AI analysis for TODAY's full day performance
        $aiAnalysis = $this->generateEnhancedAIAnalysis($todayFilters, $todayStats, $equipmentRunning, $timeProgress, $productionProgress);
        
        // Determine badge text based on TODAY's time and progress
        $badgeText = $this->getBadgeText($todayDate, $timeProgress);
        
        return [
            'title' => 'Live Performance Monitor',
            'subtitle' => $todayDate->format('M j, Y') . ' - Real-time AI Analysis',
            'period_desc' => 'Full 24-hour period with AI insights',
            // Time-based progress for progress bar (how much of the day has passed)
            'progress_percent' => $timeProgress,
            'time_progress' => $timeProgress,
            // Production progress for metrics
            'production_progress' => $productionProgress,
            // Equipment running (with ongoing lots)
            'equipment_running' => $equipmentRunning['running'] . '/' . $equipmentRunning['total'],
            'equipment_running_details' => $equipmentRunning,
            // Lines running (for compatibility)
            'lines_running' => $equipmentRunning['lines_active'] . '/11',
            // Actual submitted results
            'actual_results' => $actualResults['formatted'],
            'actual_results_raw' => $actualResults['raw'],
            // New metrics for the cards
            'ideal_progress_qty' => number_format($idealProgressQty / 1000000, 1) . 'M',
            'ideal_progress_qty_raw' => $idealProgressQty,
            'actual_progress_qty' => number_format($actualProgressQty / 1000000, 1) . 'M',
            'actual_progress_qty_raw' => $actualProgressQty,
            'recovery_qty' => $recoveryQty['formatted'],
            'recovery_qty_raw' => $recoveryQty['raw'],
            // Target capacity for calculations
            'target_capacity_raw' => $todayStats['target_capacity'],
            // Progress Rate = Production Progress (submitted vs target)
            'progress_rate' => number_format($progressRate, 1) . '%',
            'avg_efficiency' => number_format($progressRate, 1) . '%', // Keep for compatibility
            'vs_target' => ($vsTarget >= 0 ? '+' : '') . $vsTarget . '%',
            'vs_target_class' => $vsTarget >= 0 ? 'success' : 'danger',
            'time_remaining' => $timeRemaining,
            'best_line' => $linePerformance['best'],
            'worst_line' => $linePerformance['worst'],
            'ai_recommendation' => $aiAnalysis['recommendation'],
            'ai_alerts' => $aiAnalysis['alerts'],
            'badge_text' => $badgeText,
            'current_time' => now()->format('H:i'),
            'current_date' => $todayDate->format('Y-m-d')
        ];
    }
    
    /**
     * Calculate full day progress percentage
     */
    private function calculateFullDayProgress($date)
    {
        $now = now();
        $targetDate = Carbon::parse($date);
        
        // If the target date is in the future, return 0
        if ($targetDate->isFuture()) {
            return 0;
        }
        
        // If the target date is in the past, return 100
        if ($targetDate->endOfDay()->isPast()) {
            return 100;
        }
        
        // If it's today, calculate progress
        if ($targetDate->isToday()) {
            $startOfDay = $targetDate->copy()->startOfDay();
            $endOfDay = $targetDate->copy()->endOfDay();
            $totalMinutes = $endOfDay->diffInMinutes($startOfDay);
            $elapsedMinutes = $now->diffInMinutes($startOfDay);
            
            return min(round(($elapsedMinutes / $totalMinutes) * 100, 1), 100);
        }
        
        return 0;
    }
    
    /**
     * Calculate remaining time for full day
     */
    private function calculateFullDayRemainingTime($date)
    {
        $now = now();
        $targetDate = Carbon::parse($date);
        
        // If the target date is in the past, no time remaining
        if ($targetDate->endOfDay()->isPast()) {
            return '0.00h';
        }
        
        // If the target date is in the future, show full day
        if ($targetDate->isFuture()) {
            return '24.00h';
        }
        
        // If it's today, calculate remaining time
        if ($targetDate->isToday()) {
            $endOfDay = $targetDate->copy()->endOfDay();
            $remainingMinutes = max($now->diffInMinutes($endOfDay, false), 0);
            $remainingHours = $remainingMinutes / 60;
            
            return number_format($remainingHours, 2) . 'h';
        }
        
        return '0.00h';
    }
    
    /**
     * Generate dynamic AI analysis based on current performance data
     */
    private function generateAIAnalysis($filters)
    {
        // Get current period stats for analysis
        $currentStats = $this->getEndtimeDashboardStats($filters);
        
        // Get line performance data
        $linePerformance = $this->getTopAndWorstLines($filters);
        $avgEfficiency = $this->calculateAverageEfficiency($filters);
        
        // Check if there's any real data to analyze
        $hasData = isset($linePerformance['has_data']) && $linePerformance['has_data'] && $currentStats['submitted_quantity'] > 0;
        
        if (!$hasData) {
            return [
                'recommendation' => [
                    'type' => 'info',
                    'title' => 'No Data Available',
                    'message' => 'No production data available for analysis. Start production activities to see AI-powered insights and recommendations.'
                ],
                'alerts' => [
                    [
                        'type' => 'info',
                        'title' => 'Waiting for Data',
                        'message' => 'AI analysis will begin once production lots are submitted and processed.'
                    ]
                ]
            ];
        }
        
        // Get full day stats for comparison
        $currentDate = Carbon::parse($filters['dashboard_date'] ?? today()->format('Y-m-d'));
        $fullDayFilters = [
            'dashboard_date' => $currentDate->format('Y-m-d'),
            'dashboard_shift' => 'all',
            'dashboard_cutoff' => 'all',
            'dashboard_work_type' => $filters['dashboard_work_type'] ?? 'all'
        ];
        $fullDayStats = $this->getEndtimeDashboardStats($fullDayFilters);
        
        // Calculate key metrics
        $currentProgress = $currentStats['target_capacity'] > 0 
            ? round(($currentStats['submitted_quantity'] / $currentStats['target_capacity']) * 100, 1)
            : 0;
        
        $fullDayProgress = $fullDayStats['target_capacity'] > 0 
            ? round(($fullDayStats['submitted_quantity'] / $fullDayStats['target_capacity']) * 100, 1)
            : 0;
        
        $vsTarget = $currentProgress - 100;
        
        // Generate recommendation
        $recommendation = $this->generateRecommendation($currentProgress, $vsTarget, $avgEfficiency, $currentDate);
        
        // Generate alerts
        $alerts = $this->generateAlerts($linePerformance, $currentProgress, $fullDayProgress, $avgEfficiency);
        
        return [
            'recommendation' => $recommendation,
            'alerts' => $alerts
        ];
    }
    
    /**
     * Generate performance recommendation
     */
    private function generateRecommendation($currentProgress, $vsTarget, $avgEfficiency, $date)
    {
        $recommendation = [];
        
        if ($currentProgress >= 110) {
            $recommendation = [
                'type' => 'success',
                'title' => 'Excellent Performance',
                'message' => "Outstanding progress at {$currentProgress}%. Maintain current efficiency to exceed daily targets significantly."
            ];
        } elseif ($currentProgress >= 100) {
            $recommendation = [
                'type' => 'success', 
                'title' => 'Target Achieved',
                'message' => "Great work! Currently at {$currentProgress}%. Continue current pace to maintain target achievement."
            ];
        } elseif ($currentProgress >= 90) {
            $hoursRemaining = $this->calculateFullDayRemainingTime($date);
            $gapPercent = abs($vsTarget);
            $recommendation = [
                'type' => 'info',
                'title' => 'Near Target',
                'message' => "At {$currentProgress}%, only {$gapPercent}% below target. Focus on efficiency improvements in remaining {$hoursRemaining} to reach 100%."
            ];
        } elseif ($currentProgress >= 70) {
            $gapPercent = abs($vsTarget);
            $recommendation = [
                'type' => 'warning',
                'title' => 'Recovery Needed',
                'message' => "Current pace is {$gapPercent}% below target. Implement efficiency improvements and consider extending operations to recover."
            ];
        } else {
            $gapPercent = abs($vsTarget);
            $recommendation = [
                'type' => 'danger',
                'title' => 'Critical Gap',
                'message' => "Significant shortfall at {$currentProgress}%. Immediate intervention required - review line assignments, equipment status, and resource allocation."
            ];
        }
        
        return $recommendation;
    }
    
    /**
     * Generate performance alerts
     */
    private function generateAlerts($linePerformance, $currentProgress, $fullDayProgress, $avgEfficiency)
    {
        $alerts = [];
        
        // Line performance alerts
        if (isset($linePerformance['worst']) && $linePerformance['worst']) {
            $worstLine = $linePerformance['worst']['line'];
            $worstEfficiency = (float) str_replace(['% efficiency', '%'], '', $linePerformance['worst']['efficiency']);
            
            if ($worstEfficiency < 70) {
                $alerts[] = [
                    'type' => 'danger',
                    'title' => 'Critical Line Alert',
                    'message' => "Line {$worstLine} performance critical at {$worstEfficiency}%. Immediate intervention required to prevent daily target miss."
                ];
            } elseif ($worstEfficiency < 85) {
                $alerts[] = [
                    'type' => 'warning',
                    'title' => 'Line Performance Warning',
                    'message' => "Line {$worstLine} underperforming at {$worstEfficiency}%. Monitor closely and consider maintenance or process adjustments."
                ];
            }
        }
        
        // Best performer recognition
        if (isset($linePerformance['best']) && $linePerformance['best']) {
            $bestLine = $linePerformance['best']['line'];
            $bestEfficiency = (float) str_replace(['% efficiency', '%'], '', $linePerformance['best']['efficiency']);
            
            if ($bestEfficiency > 105) {
                $alerts[] = [
                    'type' => 'success',
                    'title' => 'Excellence Recognition',
                    'message' => "Line {$bestLine} excelling at {$bestEfficiency}%. Consider replicating this line's practices across other production lines."
                ];
            }
        }
        
        // Efficiency alerts
        if ($avgEfficiency < 80) {
            $alerts[] = [
                'type' => 'warning',
                'title' => 'Overall Efficiency Alert',
                'message' => "Average efficiency at {$avgEfficiency}% is below optimal. Review equipment maintenance schedules and operator training needs."
            ];
        }
        
        // Progress comparison alerts
        $progressDiff = $currentProgress - $fullDayProgress;
        if (abs($progressDiff) > 15) {
            if ($progressDiff > 0) {
                $alerts[] = [
                    'type' => 'info',
                    'title' => 'Period Outperforming',
                    'message' => "Current period is {$progressDiff}% above full day average. Excellent momentum - maintain this pace."
                ];
            } else {
                $alerts[] = [
                    'type' => 'warning',
                    'title' => 'Period Underperforming', 
                    'message' => "Current period is " . abs($progressDiff) . "% below full day average. Focus needed to improve this period's performance."
                ];
            }
        }
        
        // If no alerts generated, add a positive message
        if (empty($alerts) && $currentProgress > 85) {
            $alerts[] = [
                'type' => 'info',
                'title' => 'Steady Performance',
                'message' => "Operations running smoothly. Continue monitoring and maintain current production standards."
            ];
        }
        
        return $alerts;
    }
    
    /**
     * Get dynamic badge text for Live Performance Monitor
     */
    private function getBadgeText($currentDate, $progressPercent)
    {
        $now = now();
        $targetDate = Carbon::parse($currentDate);
        
        if ($targetDate->isToday()) {
            if ($progressPercent >= 100) {
                return 'Day Complete';
            } elseif ($progressPercent >= 75) {
                return 'Late Hours';
            } elseif ($progressPercent >= 50) {
                return 'Mid Day';
            } elseif ($progressPercent >= 25) {
                return 'Morning';
            } else {
                return 'Early Morning';
            }
        } elseif ($targetDate->isFuture()) {
            return 'Future Date';
        } else {
            return 'Full Day';
        }
    }
    
    /**
     * Calculate average efficiency across all lines for current filters
     */
    private function calculateAverageEfficiency($filters)
    {
        $linePerformance = $this->getTopAndWorstLines($filters);
        
        // Check if there's any real data
        if (empty($linePerformance['all_lines']) || !isset($linePerformance['has_data']) || !$linePerformance['has_data']) {
            return 0;
        }
        
        $totalEfficiency = 0;
        $lineCountWithData = 0;
        
        foreach ($linePerformance['all_lines'] as $line) {
            // Only include lines that have actual data
            if (isset($line['has_data']) && $line['has_data']) {
                $efficiency = (float) str_replace(['% efficiency', '%'], '', $line['efficiency']);
                $totalEfficiency += $efficiency;
                $lineCountWithData++;
            }
        }
        
        return $lineCountWithData > 0 ? round($totalEfficiency / $lineCountWithData, 1) : 0;
    }
    
    /**
     * Calculate period hours based on shift and cutoff
     */
    private function calculatePeriodHours($shift, $cutoff)
    {
        if ($shift === 'all') {
            return '24.00h';
        } elseif ($cutoff === 'all') {
            return '12.00h'; // Full shift
        } else {
            // Specific cutoff periods
            $cutoffHours = ['1' => 5, '2' => 4, '3' => 3]; // Approximate hours per cutoff
            return number_format($cutoffHours[$cutoff] ?? 4, 2) . 'h';
        }
    }
    
    /**
     * Get count of active lines
     */
    private function getActiveLinesCount($filters)
    {
        $totalLines = 11; // Lines A through K
        
        // Count lines that have submitted lots (actual production activity)
        $activeLinesQuery = Endtime::query();
        $this->applyDashboardTimeFilter($activeLinesQuery, 
            $filters['dashboard_date'], $filters['dashboard_shift'], $filters['dashboard_cutoff']);
        
        // Only count lines with submitted lots (actual production)
        $activeLinesQuery->where('status', 'Submitted');
        
        if ($filters['dashboard_work_type'] !== 'all') {
            $activeLinesQuery->where('work_type', $filters['dashboard_work_type']);
        }
        
        $activeLines = $activeLinesQuery->distinct('eqp_line')->count('eqp_line');
        
        return [
            'active' => $activeLines,
            'total' => $totalLines
        ];
    }
    
    /**
     * Calculate current progress for ongoing periods
     */
    private function calculateCurrentProgress($context)
    {
        $now = now();
        $currentDate = Carbon::parse($context['date']);
        
        // For demonstration, return a calculated progress based on time
        // In real implementation, this would be more sophisticated
        if ($context['shift'] === 'all') {
            $startOfDay = $currentDate->copy()->startOfDay();
            $endOfDay = $currentDate->copy()->endOfDay();
            $totalMinutes = $endOfDay->diffInMinutes($startOfDay);
            $elapsedMinutes = min($now->diffInMinutes($startOfDay), $totalMinutes);
            return round(($elapsedMinutes / $totalMinutes) * 100, 1);
        }
        
        // For specific shifts/cutoffs, return a reasonable progress estimate
        return min(round(($now->hour / 24) * 100, 1), 100);
    }
    
    /**
     * Calculate remaining time for current period
     */
    private function calculateRemainingTime($context)
    {
        // Simplified calculation - in real implementation this would be more precise
        $hours = $this->calculatePeriodHours($context['shift'], $context['cutoff']);
        $hoursFloat = (float) str_replace('h', '', $hours);
        
        // Estimate remaining time (this is a placeholder)
        $remaining = max($hoursFloat - 2, 0); // Assume 2 hours have passed
        
        return number_format($remaining, 2) . 'h';
    }
    
    /**
     * Get equipment running count (equipment with ongoing lots)
     */
    private function getEquipmentRunningCount($filters)
    {
        // Get total equipment count (only OPERATIONAL status)
        $totalEquipmentQuery = Equipment::where('eqp_status', 'OPERATIONAL');
        
        // Apply alloc_type filter if specified
        if (isset($filters['dashboard_alloc_type']) && $filters['dashboard_alloc_type'] !== 'all') {
            $totalEquipmentQuery->where('alloc_type', $filters['dashboard_alloc_type']);
        }
        
        $totalEquipment = $totalEquipmentQuery->count();
        
        // Get equipment that currently have ongoing lots (from equipment table)
        // This is more accurate than checking old endtime records
        $runningEquipmentQuery = Equipment::where('eqp_status', 'OPERATIONAL')
            ->whereNotNull('ongoing_lot')
            ->where('ongoing_lot', '!=', '');
            
        // Apply alloc_type filter if specified
        if (isset($filters['dashboard_alloc_type']) && $filters['dashboard_alloc_type'] !== 'all') {
            $runningEquipmentQuery->where('alloc_type', $filters['dashboard_alloc_type']);
        }
        
        $equipmentWithOngoing = $runningEquipmentQuery->count();
        
        // Count active lines (lines that have equipment with ongoing lots)
        $activeLinesCount = $runningEquipmentQuery
            ->distinct('eqp_line')
            ->count('eqp_line');
        
        return [
            'running' => $equipmentWithOngoing,
            'total' => $totalEquipment,
            'lines_active' => $activeLinesCount,
            'percentage' => $totalEquipment > 0 ? round(($equipmentWithOngoing / $totalEquipment) * 100, 1) : 0
        ];
    }
    
    /**
     * Get actual submitted results in proper format
     */
    private function getActualResults($filters)
    {
        $submittedQuery = Endtime::query();
        $this->applyDashboardTimeFilter($submittedQuery, 
            $filters['dashboard_date'], $filters['dashboard_shift'], $filters['dashboard_cutoff']);
        
        $submittedQuery->where('status', 'Submitted');
        
        if ($filters['dashboard_work_type'] !== 'all') {
            $submittedQuery->where('work_type', $filters['dashboard_work_type']);
        }
        
        $submittedQuantity = $submittedQuery->sum('lot_qty') ?: 0;
        $submittedLots = $submittedQuery->count();
        
        return [
            'raw' => $submittedQuantity,
            'lots_count' => $submittedLots,
            'formatted' => number_format($submittedQuantity / 1000000, 1) . 'M PCS',
            'lots_formatted' => number_format($submittedLots) . ' LOTS'
        ];
    }
    
    /**
     * Calculate ideal progress quantity based on time progress
     */
    private function calculateIdealProgressQty($todayStats, $timeProgress)
    {
        // Ideal progress = what we should have produced by this time
        // Formula: target_capacity * (time_progress / 100)
        $targetCapacity = $todayStats['target_capacity'] ?? 0;
        $idealQty = $targetCapacity * ($timeProgress / 100);
        
        return $idealQty;
    }
    
    /**
     * Calculate recovery quantity (difference between ideal and actual)
     */
    private function calculateRecoveryQty($idealProgressQty, $actualProgressQty)
    {
        $recoveryQtyRaw = $actualProgressQty - $idealProgressQty;
        
        // Format the recovery quantity
        $formatted = ($recoveryQtyRaw >= 0 ? '+' : '') . number_format($recoveryQtyRaw / 1000000, 1) . 'M';
        
        return [
            'raw' => $recoveryQtyRaw,
            'formatted' => $formatted,
            'status' => $recoveryQtyRaw > 0 ? 'ahead' : ($recoveryQtyRaw < 0 ? 'behind' : 'on_track')
        ];
    }
    
    /**
     * Generate enhanced AI analysis with better insights
     */
    private function generateEnhancedAIAnalysis($filters, $todayStats, $equipmentRunning, $timeProgress = null, $productionProgress = null)
    {
        $hasData = $todayStats['submitted_quantity'] > 0 || $todayStats['ongoing_quantity'] > 0;
        
        if (!$hasData) {
            return [
                'recommendation' => [
                    'type' => 'info',
                    'title' => 'No Data Available',
                    'message' => 'No production data available for analysis. Start production activities to see AI-powered insights and recommendations.'
                ],
                'alerts' => [
                    [
                        'type' => 'info',
                        'title' => 'Waiting for Data',
                        'message' => 'AI analysis will begin once production lots are submitted and processed.'
                    ]
                ]
            ];
        }
        
        // Use provided progress values or calculate them
        $currentProgress = $productionProgress ?? ($todayStats['target_capacity'] > 0 
            ? round(($todayStats['submitted_quantity'] / $todayStats['target_capacity']) * 100, 1)
            : 0);
        
        $dayTimeProgress = $timeProgress ?? $this->calculateFullDayProgress(today());
        $equipmentUtilization = $equipmentRunning['percentage'];
        
        // Generate recommendation based on current performance
        $recommendation = $this->generateSmartRecommendation($currentProgress, $equipmentUtilization, $dayTimeProgress, $todayStats);
        
        // Generate alerts based on performance metrics
        $alerts = $this->generateSmartAlerts($currentProgress, $equipmentUtilization, $dayTimeProgress, $todayStats);
        
        return [
            'recommendation' => $recommendation,
            'alerts' => $alerts
        ];
    }
    
    /**
     * Generate smart recommendations based on real-time data
     */
    private function generateSmartRecommendation($progress, $equipmentUtil, $dayProgress, $stats)
    {
        $now = now();
        $timeOfDay = $now->format('H:i');
        
        // High performance scenario
        if ($progress >= 100) {
            return [
                'type' => 'success',
                'title' => 'Excellent Performance',
                'message' => "Target exceeded by " . number_format($progress - 100, 1) . "%. Continue current operations and monitor for consistency."
            ];
        }
        
        // Good progress scenario
        if ($progress >= 80 && $equipmentUtil >= 70) {
            $remaining = 100 - $dayProgress;
            return [
                'type' => 'success',
                'title' => 'On Track Performance',
                'message' => "Good progress at {$timeOfDay}. With {$remaining}% time remaining, maintain current pace to meet targets."
            ];
        }
        
        // Low equipment utilization
        if ($equipmentUtil < 50) {
            return [
                'type' => 'warning',
                'title' => 'Low Equipment Utilization',
                'message' => "Only {$equipmentUtil}% equipment running. Consider starting more production lines to improve throughput."
            ];
        }
        
        // Behind schedule
        if ($progress < $dayProgress - 20) {
            return [
                'type' => 'danger',
                'title' => 'Behind Schedule',
                'message' => "Production is lagging behind time progress. Immediate action needed to recover target achievement."
            ];
        }
        
        // Default recommendation
        return [
            'type' => 'info',
            'title' => 'Monitor Progress',
            'message' => "Current progress: {$progress}%. Continue monitoring and optimize production processes as needed."
        ];
    }
    
    /**
     * Generate smart alerts based on performance data
     */
    private function generateSmartAlerts($progress, $equipmentUtil, $dayProgress, $stats)
    {
        $alerts = [];
        $now = now();
        
        // Critical performance alert
        if ($progress < 50 && $dayProgress > 50) {
            $alerts[] = [
                'type' => 'danger',
                'title' => 'Critical Performance Gap',
                'message' => 'Production significantly behind schedule. Urgent intervention required.'
            ];
        }
        
        // Equipment utilization alert
        if ($equipmentUtil < 30 && $now->hour >= 8 && $now->hour <= 18) {
            $alerts[] = [
                'type' => 'warning',
                'title' => 'Low Equipment Activity',
                'message' => 'Many production lines appear idle during peak hours.'
            ];
        }
        
        // Target achievement alert
        if ($progress >= 100) {
            $alerts[] = [
                'type' => 'success',
                'title' => 'Target Achieved',
                'message' => 'Daily target met! Focus on maintaining quality and efficiency.'
            ];
        }
        
        // End of shift alert
        if ($now->hour >= 17 && $progress < 80) {
            $alerts[] = [
                'type' => 'warning',
                'title' => 'End of Day Approaching',
                'message' => 'Consider extending operations or prioritizing critical lots.'
            ];
        }
        
        return $alerts;
    }
    
    /**
     * Get top and worst performing lines
     */
    private function getTopAndWorstLines($filters)
    {
        $lines = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K'];
        $linePerformances = [];
        $allLinesData = [];
        $hasAnyData = false;
        
        foreach ($lines as $line) {
            $lineData = $this->getLineDataByFilters($line, $filters);
            
            // Only calculate performance if there's actual submitted data
            if ($lineData['submitted_lots'] > 0 && $lineData['target_capacity'] > 0) {
                $performancePercent = round(($lineData['submitted_lots'] / $lineData['target_capacity']) * 100, 1);
                $hasAnyData = true;
            } else {
                $performancePercent = 0;
            }
            
            $linePerformances[$line] = $performancePercent;
            $allLinesData[$line] = [
                'line' => $line,
                'efficiency' => number_format($performancePercent, 1) . '% efficiency',
                'performance_percent' => $performancePercent,
                'target_capacity' => $lineData['target_capacity'],
                'submitted_lots' => $lineData['submitted_lots'],
                'total_endtime' => $lineData['total_endtime'],
                'has_data' => $lineData['submitted_lots'] > 0
            ];
        }
        
        // If no data at all, return no-data response
        if (!$hasAnyData) {
            return [
                'best' => [
                    'line' => '-',
                    'efficiency' => 'No data available',
                    'status' => 'No Data'
                ],
                'worst' => [
                    'line' => '-',
                    'efficiency' => 'No data available',
                    'status' => 'No Data'
                ],
                'all_lines' => $allLinesData,
                'has_data' => false
            ];
        }
        
        // Filter out lines with no data for best/worst calculation
        $linesWithData = array_filter($linePerformances, function($perf) {
            return $perf > 0;
        });
        
        if (empty($linesWithData)) {
            return [
                'best' => [
                    'line' => '-',
                    'efficiency' => 'No data available',
                    'status' => 'No Data'
                ],
                'worst' => [
                    'line' => '-',
                    'efficiency' => 'No data available',
                    'status' => 'No Data'
                ],
                'all_lines' => $allLinesData,
                'has_data' => false
            ];
        }
        
        // Sort to get best and worst from lines with actual data
        arsort($linesWithData);
        $bestLine = array_key_first($linesWithData);
        $bestEfficiency = reset($linesWithData);
        
        asort($linesWithData);
        $worstLine = array_key_first($linesWithData);
        $worstEfficiency = reset($linesWithData);
        
        return [
            'best' => [
                'line' => $bestLine,
                'efficiency' => number_format($bestEfficiency, 1) . '% efficiency',
                'status' => $bestEfficiency >= 100 ? 'Excellent' : 'Good'
            ],
            'worst' => [
                'line' => $worstLine,
                'efficiency' => number_format($worstEfficiency, 1) . '% efficiency',
                'status' => $worstEfficiency < 70 ? 'Critical' : ($worstEfficiency < 90 ? 'Warning' : 'Monitor')
            ],
            'all_lines' => $allLinesData,
            'has_data' => true
        ];
    }
    
    
    /**
     * Get Per Size Summary data based on dashboard filters
     */
    private function getPerSizeSummary($filters)
    {
        // Frontend display sizes
        $displaySizes = ['0603', '1005', '1608', '2012', '3216', '3225'];
        $summary = [
            'sizes' => $displaySizes,
            'target' => [],
            'endtime' => [],
            'submitted' => [],
            'submitted_percent' => [],
            'endtime_percent' => []
        ];
        
        foreach ($displaySizes as $displaySize) {
            // Convert frontend display size to database value
            $dbSize = $this->convertDisplaySizeToDbSize($displaySize);
            $sizeData = $this->getSizeDataByFilters($dbSize, $filters);
            
            // Target capacity in millions
            $targetCapacity = $sizeData['target_capacity'];
            $summary['target'][$displaySize] = $targetCapacity > 0 ? number_format($targetCapacity / 1000000, 0) . ' M' : '0 M';
            
            // Endtime (total lots) in millions
            $totalEndtime = $sizeData['total_endtime'];
            $summary['endtime'][$displaySize] = $totalEndtime > 0 ? number_format($totalEndtime / 1000000, 0) . ' M' : '0 M';
            
            // Submitted lots in millions
            $submittedLots = $sizeData['submitted_lots'];
            $summary['submitted'][$displaySize] = $submittedLots > 0 ? number_format($submittedLots / 1000000, 0) . ' M' : '0 M';
            
            // Calculate percentages
            $submittedPercent = $totalEndtime > 0 ? round(($submittedLots / $totalEndtime) * 100, 1) : 0;
            $endtimePercent = $targetCapacity > 0 ? round(($totalEndtime / $targetCapacity) * 100, 1) : 0;
            
            $summary['submitted_percent'][$displaySize] = $submittedPercent;
            $summary['endtime_percent'][$displaySize] = $endtimePercent;
        }
        
        return $summary;
    }
    
    /**
     * Convert frontend display size to database size values
     * Frontend: 0603, 1005, 1608, 2012, 3216, 3225
     * Database inconsistency: Equipment uses '3', '5', '10', '21', '31', '32'
     *                         Endtime uses '03', '05', '10'
     */
    private function convertDisplaySizeToDbSize($displaySize)
    {
        $sizeMapping = [
            '0603' => ['03', '3'],
            '1005' => ['05', '5'], 
            '1608' => ['10'],
            '2012' => ['21'],
            '3216' => ['31'],
            '3225' => ['32']
        ];
        
        return $sizeMapping[$displaySize] ?? [$displaySize];
    }
    
    /**
     * Get data for a specific size based on dashboard filters
     */
    private function getSizeDataByFilters($dbSizes, $filters)
    {
        // Extract filters
        $currentDate = $filters['dashboard_date'] ?? today()->format('Y-m-d');
        $currentShift = $filters['dashboard_shift'] ?? 'all';
        $currentCutoff = $filters['dashboard_cutoff'] ?? 'all';
        $workType = $filters['dashboard_work_type'] ?? 'all';
        $allocType = $filters['dashboard_alloc_type'] ?? 'all';
        
        // Get equipment for this size that matches the alloc type filter
        // Handle multiple possible size values due to database inconsistency
        $equipmentQuery = Equipment::whereIn('size', $dbSizes)
            ->whereIn('eqp_status', ['OPERATIONAL', 'PLANSTOP', 'IDDLE']);
        
        // Apply alloc_type filter if specified
        if (isset($filters['dashboard_alloc_type']) && $allocType !== 'all') {
            $equipmentQuery->where('alloc_type', $allocType);
        }
        
        $equipment = $equipmentQuery->get();
        
        // Calculate target capacity for this size (same method as dashboard cards)
        $targetCapacity = 0;
        
        // Calculate time multiplier based on shift and cutoff selection (same as dashboard cards)
        $timeMultiplier = $this->getTimeMultiplier($currentShift, $currentCutoff);
        
        foreach ($equipment as $eq) {
            $oee = floatval($eq->eqp_oee) ?: 0;
            $speed = floatval(str_replace(',', '', $eq->loading_speed)) ?: 0;
            $operationTime = floatval(str_replace(',', '', $eq->operation_time)) ?: 0;
            
            // Calculate capacity based on time period: OEE × Speed × Operation Time × Time Multiplier
            $periodCapacity = round($oee * $speed * $operationTime * $timeMultiplier);
            $targetCapacity += $periodCapacity;
        }
        
        // Get endtime data for this size using the SAME filters as dashboard cards
        $endtimeQuery = $this->getSizeEndtimeQuery($dbSizes, $currentDate, $currentShift, $currentCutoff, $workType);
        
        // Total endtime for this size (same as card 2)
        $totalEndtime = $endtimeQuery->sum('lot_qty') ?: 0;
        
        // Submitted lots for this size (same as card 3)
        $submittedQuery = clone $endtimeQuery;
        $submittedLots = $submittedQuery->where('status', 'Submitted')->sum('lot_qty') ?: 0;
        
        // Remaining lots for this size (same as card 4)
        $remainingLots = $totalEndtime - $submittedLots;
        
        
        return [
            'target_capacity' => $targetCapacity,
            'total_endtime' => $totalEndtime,
            'submitted_lots' => $submittedLots,
            'remaining_lots' => $remainingLots
        ];
    }
    
    /**
     * Get endtime query for specific size using SAME filters as dashboard cards
     */
    private function getSizeEndtimeQuery($dbSizes, $date, $shift, $cutoff, $workType)
    {
        // Base endtime query for this size (endtime table has lot_size column directly)
        // Handle multiple possible size values due to database inconsistency
        $query = Endtime::whereIn('lot_size', $dbSizes);
        
        // Apply the EXACT SAME time filter logic as dashboard cards
        $this->applyDashboardTimeFilter($query, $date, $shift, $cutoff);
        
        // Apply work type filter if needed
        if ($workType !== 'all') {
            $query->where('work_type', $workType);
        }
        
        return $query;
    }
    
    /**
     * Export Endtime data as CSV based on date range
     */
    public function exportEndtimeData(Request $request)
    {
        // Validate the request
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date'
        ]);
        
        $startDate = Carbon::parse($request->start_date)->startOfDay();
        $endDate = Carbon::parse($request->end_date)->endOfDay();
        
        // Query endtime data within the date range
        $endtimeData = Endtime::whereBetween('created_at', [$startDate, $endDate])
            ->orderBy('created_at', 'desc')
            ->get();
        
        // Generate CSV filename with date range
        $filename = 'endtime_data_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.csv';
        
        // Create CSV content
        $csvContent = $this->generateEndtimeCsv($endtimeData);
        
        // Return CSV response
        return response($csvContent, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0'
        ]);
    }
    
    /**
     * Generate CSV content from endtime data
     */
    private function generateEndtimeCsv($endtimeData)
    {
        // Define CSV headers
        $headers = [
            'ID',
            'Lot ID',
            'Model 15',
            'Lot Size',
            'Lot Quantity',
            'Work Type',
            'Lot Type',
            'Status',
            'Equipment Line',
            'Equipment Area',
            'Equipment 1',
            'Equipment 2', 
            'Equipment 3',
            'Equipment 4',
            'Equipment 5',
            'Equipment 6',
            'Equipment 7',
            'Equipment 8',
            'Equipment 9',
            'Equipment 10',
            'Start Time 1',
            'Start Time 2',
            'Start Time 3',
            'Start Time 4',
            'Start Time 5',
            'Start Time 6',
            'Start Time 7',
            'Start Time 8',
            'Start Time 9',
            'Start Time 10',
            'NG Percent 1',
            'NG Percent 2',
            'NG Percent 3',
            'NG Percent 4',
            'NG Percent 5',
            'NG Percent 6',
            'NG Percent 7',
            'NG Percent 8',
            'NG Percent 9',
            'NG Percent 10',
            'Estimated End Time',
            'Actual Submitted At',
            'Remarks',
            'LIPAS YN',
            'Submission Notes',
            'Created At',
            'Updated At',
            'Created By',
            'Modified By'
        ];
        
        // Start with headers
        $csvData = [];
        $csvData[] = $headers;
        
        // Add data rows
        foreach ($endtimeData as $row) {
            $csvData[] = [
                $row->id,
                $row->lot_id,
                $row->model_15,
                $row->lot_size,
                $row->lot_qty,
                $row->work_type,
                $row->lot_type,
                $row->status,
                $row->eqp_line,
                $row->eqp_area,
                $row->eqp_1,
                $row->eqp_2,
                $row->eqp_3,
                $row->eqp_4,
                $row->eqp_5,
                $row->eqp_6,
                $row->eqp_7,
                $row->eqp_8,
                $row->eqp_9,
                $row->eqp_10,
                $row->start_time_1,
                $row->start_time_2,
                $row->start_time_3,
                $row->start_time_4,
                $row->start_time_5,
                $row->start_time_6,
                $row->start_time_7,
                $row->start_time_8,
                $row->start_time_9,
                $row->start_time_10,
                $row->ng_percent_1,
                $row->ng_percent_2,
                $row->ng_percent_3,
                $row->ng_percent_4,
                $row->ng_percent_5,
                $row->ng_percent_6,
                $row->ng_percent_7,
                $row->ng_percent_8,
                $row->ng_percent_9,
                $row->ng_percent_10,
                $row->est_endtime,
                $row->actual_submitted_at,
                $row->remarks,
                $row->lipas_yn,
                $row->submission_notes,
                $row->created_at,
                $row->updated_at,
                $row->created_by,
                $row->modified_by
            ];
        }
        
        // Convert to CSV string
        $output = fopen('php://temp', 'r+');
        
        foreach ($csvData as $row) {
            fputcsv($output, $row);
        }
        
        rewind($output);
        $csvContent = stream_get_contents($output);
        fclose($output);
        
        return $csvContent;
    }
}
