[2025-09-28 14:29:18] local.ERROR: View [lot-requests.create] not found. {"userId":"21278703","exception":"[object] (InvalidArgumentException(code: 0): View [lot-requests.create] not found. at C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:138)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(78): Illuminate\\View\\FileViewFinder->findInPaths('lot-requests.cr...', Array)
#1 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(150): Illuminate\\View\\FileViewFinder->find('lot-requests.cr...')
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(1119): Illuminate\\View\\Factory->make('lot-requests.cr...', Array, Array)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Controllers\\LotRequestController.php(84): view('lot-requests.cr...', Array)
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\LotRequestController->create()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('create', Array)
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\LotRequestController), 'create')
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(822): Illuminate\\Routing\\Route->run()
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#55 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#56 {main}
"} 
[2025-09-28 14:32:28] local.WARNING: Permission denied for user {"user_role":"ADMIN","user_id":1,"requested_route":"lot-requests","requested_action":"manage","full_permission":"lot-requests:manage","request_url":"http://127.0.0.1:8000/lot-requests/6/accept","request_method":"PATCH"} 
[2025-09-28 14:33:55] local.WARNING: Permission denied for user {"user_role":"ADMIN","user_id":1,"requested_route":"lot-requests","requested_action":"manage","full_permission":"lot-requests:manage","request_url":"http://127.0.0.1:8000/lot-requests/8/accept","request_method":"PATCH"} 
[2025-09-28 14:35:57] local.INFO: Chart Totals vs Dashboard Cards: {"Target Capacity":"1,762,900,000 PCS","Total Endtime":"35,500,000 PCS","Submitted Lots":"35,500,000 PCS","Remaining Lots":"0 PCS"} 
[2025-09-28 14:35:59] local.INFO: Chart Totals vs Dashboard Cards: {"Target Capacity":"293,900,000 PCS","Total Endtime":"0 PCS","Submitted Lots":"0 PCS","Remaining Lots":"0 PCS"} 
[2025-09-28 14:48:21] local.WARNING: Permission denied for user {"user_role":"ADMIN","user_id":1,"requested_route":"lot-requests","requested_action":"manage","full_permission":"lot-requests:manage","request_url":"http://127.0.0.1:8000/lot-requests/8/accept","request_method":"PATCH"} 
[2025-09-28 15:05:02] local.ERROR: Call to a member function count() on array (View: C:\inetpub\wwwroot\process-dashboard\resources\views\lot-requests\assign-lots.blade.php) {"userId":"21278703","exception":"[object] (Illuminate\\View\\ViewException(code: 0): Call to a member function count() on array (View: C:\\inetpub\\wwwroot\\process-dashboard\\resources\\views\\lot-requests\\assign-lots.blade.php) at C:\\inetpub\\wwwroot\\process-dashboard\\storage\\framework\\views\\f14f00e117fb63e2bd9c694bd77a259e.php:330)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Error), 1)
#1 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\inetpub\\\\wwwr...', Array)
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\inetpub\\\\wwwr...', Array)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Middleware\\PermissionMiddleware.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'lot-requests:ma...')
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#58 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#59 {main}

[previous exception] [object] (Error(code: 0): Call to a member function count() on array at C:\\inetpub\\wwwroot\\process-dashboard\\storage\\framework\\views\\f14f00e117fb63e2bd9c694bd77a259e.php:330)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require()
#1 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\inetpub\\\\wwwr...', Array)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\inetpub\\\\wwwr...', Array)
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\inetpub\\\\wwwr...', Array)
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Middleware\\PermissionMiddleware.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'lot-requests:ma...')
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#35 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#36 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#60 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#61 {main}
"} 
[2025-09-28 15:05:41] local.ERROR: Call to a member function count() on array (View: C:\inetpub\wwwroot\process-dashboard\resources\views\lot-requests\assign-lots.blade.php) {"userId":"21278703","exception":"[object] (Illuminate\\View\\ViewException(code: 0): Call to a member function count() on array (View: C:\\inetpub\\wwwroot\\process-dashboard\\resources\\views\\lot-requests\\assign-lots.blade.php) at C:\\inetpub\\wwwroot\\process-dashboard\\storage\\framework\\views\\f14f00e117fb63e2bd9c694bd77a259e.php:330)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Error), 1)
#1 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\inetpub\\\\wwwr...', Array)
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\inetpub\\\\wwwr...', Array)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Middleware\\PermissionMiddleware.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'lot-requests:ma...')
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#58 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#59 {main}

[previous exception] [object] (Error(code: 0): Call to a member function count() on array at C:\\inetpub\\wwwroot\\process-dashboard\\storage\\framework\\views\\f14f00e117fb63e2bd9c694bd77a259e.php:330)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require()
#1 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\inetpub\\\\wwwr...', Array)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\inetpub\\\\wwwr...', Array)
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\inetpub\\\\wwwr...', Array)
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Middleware\\PermissionMiddleware.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'lot-requests:ma...')
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#35 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#36 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#60 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#61 {main}
"} 
[2025-09-28 15:08:02] local.ERROR: Call to a member function count() on array (View: C:\inetpub\wwwroot\process-dashboard\resources\views\lot-requests\assign-lots.blade.php) {"userId":"21278703","exception":"[object] (Illuminate\\View\\ViewException(code: 0): Call to a member function count() on array (View: C:\\inetpub\\wwwroot\\process-dashboard\\resources\\views\\lot-requests\\assign-lots.blade.php) at C:\\inetpub\\wwwroot\\process-dashboard\\storage\\framework\\views\\f14f00e117fb63e2bd9c694bd77a259e.php:330)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Error), 1)
#1 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\inetpub\\\\wwwr...', Array)
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\inetpub\\\\wwwr...', Array)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Middleware\\PermissionMiddleware.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'lot-requests:ma...')
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#58 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#59 {main}

[previous exception] [object] (Error(code: 0): Call to a member function count() on array at C:\\inetpub\\wwwroot\\process-dashboard\\storage\\framework\\views\\f14f00e117fb63e2bd9c694bd77a259e.php:330)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require()
#1 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\inetpub\\\\wwwr...', Array)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\inetpub\\\\wwwr...', Array)
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\inetpub\\\\wwwr...', Array)
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Middleware\\PermissionMiddleware.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'lot-requests:ma...')
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#35 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#36 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#60 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#61 {main}
"} 
[2025-09-28 15:08:11] local.ERROR: Call to a member function count() on array (View: C:\inetpub\wwwroot\process-dashboard\resources\views\lot-requests\assign-lots.blade.php) {"userId":"21278703","exception":"[object] (Illuminate\\View\\ViewException(code: 0): Call to a member function count() on array (View: C:\\inetpub\\wwwroot\\process-dashboard\\resources\\views\\lot-requests\\assign-lots.blade.php) at C:\\inetpub\\wwwroot\\process-dashboard\\storage\\framework\\views\\f14f00e117fb63e2bd9c694bd77a259e.php:330)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Error), 1)
#1 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\inetpub\\\\wwwr...', Array)
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\inetpub\\\\wwwr...', Array)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Middleware\\PermissionMiddleware.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'lot-requests:ma...')
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#58 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#59 {main}

[previous exception] [object] (Error(code: 0): Call to a member function count() on array at C:\\inetpub\\wwwroot\\process-dashboard\\storage\\framework\\views\\f14f00e117fb63e2bd9c694bd77a259e.php:330)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require()
#1 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\inetpub\\\\wwwr...', Array)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\inetpub\\\\wwwr...', Array)
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\inetpub\\\\wwwr...', Array)
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Middleware\\PermissionMiddleware.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'lot-requests:ma...')
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#35 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#36 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#60 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#61 {main}
"} 
[2025-09-28 15:27:50] local.INFO: UpdateWipController@create called {"user_id":"21278703","user_role":"ADMIN","user_emp_no":"21278703","timestamp":"2025-09-28 15:27:50"} 
[2025-09-28 15:27:57] local.INFO: Available lots cache refreshed via observer {"lot_id":"AK9SP6P","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:57] local.INFO: Available lots cache refreshed via observer {"lot_id":"AKA1P12","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:57] local.INFO: Available lots cache refreshed via observer {"lot_id":"AKB4P6R","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:58] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL1MP6B","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:58] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL24P8F","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:58] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL24P8G","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:58] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL24P8H","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:58] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL24P8I","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:58] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL24P8J","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:58] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL3RP2Y","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:58] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL3RP9K","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:58] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL41P2G","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:58] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL46P7M","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:58] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL47P6Q","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:58] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL4DHTU","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:59] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL4FHYK","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:59] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL4GP63","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:59] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL4MP4T","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:59] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL4RP1O","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:59] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL54P3Q","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:59] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL5KP2F","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:59] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL5KP52","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:59] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL5KPB5","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:59] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL5RHTV","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:59] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL5UP6B","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:59] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL64P2X","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:59] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL6IP4F","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:59] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL6JHU7","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:27:59] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL6JP5X","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:00] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL6LP98","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:00] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL6OHTS","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:00] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL6QP2J","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:00] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL71P7Q","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:00] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL75P3Y","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:00] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL75P56","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:00] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL77P2A","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:00] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL77P5U","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:00] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL78P4T","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:00] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7BP17","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:00] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7DHV4","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:00] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7DP3B","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:00] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7DP7M","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:00] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7FP3K","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:01] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7IP84","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:01] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7JP37","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:01] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7JP49","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:01] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7KP3L","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:01] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7MP26","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:01] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7MP2I","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:01] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7MP9B","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:01] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7NP1B","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:01] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7NP22","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:01] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7OP2V","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:01] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7OP8S","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:01] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7RP92","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:01] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7TP8J","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:02] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7UP65","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:02] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7UP7K","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:02] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7VP69","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:02] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL7VP9B","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:02] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL81P3Z","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:02] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL81P5Y","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:02] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL81PAP","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:02] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL82P1V","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:02] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL82P31","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:02] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL82P7E","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:02] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL82P8R","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:02] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL83P12","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:03] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL83P5X","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:03] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL83P7E","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:03] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL83P7Y","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:03] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL84P15","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:03] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL84P7O","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:03] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL85P8S","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:03] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL85P9G","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:03] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL86P8K","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:03] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL86PAD","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:03] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL87P59","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:04] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL87P9F","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:04] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL89P5O","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:04] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL8AP1D","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:04] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL8AP3O","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:04] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL8AP5P","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:04] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL8AP7I","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:04] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL8BP47","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:04] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL8BP55","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:04] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL8BP7A","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:04] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL8BP8J","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:04] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL8BP9X","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:05] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL8CP1N","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:05] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL8EP1Y","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:05] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL8HP2Y","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:05] local.INFO: Available lots cache refreshed via observer {"lot_id":"AL8HP33","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:05] local.INFO: Available lots cache refreshed via observer {"lot_id":"BK9OP86","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:05] local.INFO: Available lots cache refreshed via observer {"lot_id":"BK9SP7G","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:05] local.INFO: Available lots cache refreshed via observer {"lot_id":"BKA4P2X","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:05] local.INFO: Available lots cache refreshed via observer {"lot_id":"BKACP9B","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:05] local.INFO: Available lots cache refreshed via observer {"lot_id":"BKARP44","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:05] local.INFO: Available lots cache refreshed via observer {"lot_id":"BKB5P6A","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:05] local.INFO: Available lots cache refreshed via observer {"lot_id":"BKBFP4F","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:05] local.INFO: Available lots cache refreshed via observer {"lot_id":"BKC8P65","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:06] local.INFO: Available lots cache refreshed via observer {"lot_id":"BKCHP57","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:06] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL3FP73","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:06] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL49P81","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:06] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL4DHTS","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:06] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL4HP44","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:06] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL53P6Q","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:06] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL59P5J","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:06] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL59P6K","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:06] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL5GP5O","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:06] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL5HP38","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:06] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL5IP7N","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:06] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL5KP78","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:06] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL5KPB4","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:07] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL5MP3B","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:07] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL5MP9C","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:07] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL5OP8U","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:07] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL5OP8V","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:07] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL5PP1Z","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:07] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL5PP21","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:07] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL5SP4P","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:07] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL5SP4Q","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:07] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL5TP17","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:07] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL5TP8I","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:07] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL5TPAI","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:07] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL5UP3Q","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:08] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL61P12","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:08] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL65P5M","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:08] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL65P8S","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:08] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL68P3F","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:08] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6BP8J","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:08] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6DP69","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:08] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6FP5W","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:08] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6GP33","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:08] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6HP33","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:08] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6HP36","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:08] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6HP3C","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:09] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6HP3U","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:09] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6HP44","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:09] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6HP49","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:09] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6HP6E","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:09] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6HPB8","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:09] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6IP9P","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:09] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6KP19","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:09] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6MP3Z","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:09] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6NP8Y","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:09] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6PP2X","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:09] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6PP6C","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:09] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6PP6D","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:10] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6PP8K","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:10] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6PP9I","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:10] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6QP4M","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:10] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6RP44","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:10] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6RP6D","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:10] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6SP2F","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:10] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6TP48","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:10] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6TP8K","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:10] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6UP1K","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:11] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6UP1N","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:11] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6UP74","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:11] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL6UP93","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:11] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL71P5B","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:11] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL72P6O","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:11] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL73P2Z","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:11] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL73P6L","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:11] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL73P8V","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:11] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL75P76","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:11] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL76P1N","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:11] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL78P4E","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:11] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL78P6B","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:12] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL78PAT","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:12] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL79P63","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:12] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL79P9A","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:12] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7AP1S","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:12] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7AP4T","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:12] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7AP6S","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:12] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7AP9K","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:12] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7BP16","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:12] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7BP5L","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:12] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7CP3W","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:12] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7CP51","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:13] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7CP8O","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:13] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7CP97","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:13] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7DP1I","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:13] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7DP2K","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:13] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7DP3Y","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:13] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7DP3Z","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:13] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7DP4I","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:13] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7DP4L","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:13] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7DP6B","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:13] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7EP2M","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:13] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7EP34","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:13] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7EP4D","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:14] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7EP6J","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:14] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7EP99","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:14] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7FP5H","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:14] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7FP6N","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:14] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7FP6Z","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:14] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7FP7B","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:14] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7FPA5","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:14] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7GP49","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:14] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7HP58","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:14] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7IPAO","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:14] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7JP3H","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:14] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7JP4D","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:15] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7JP4G","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:15] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7KP27","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:15] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7KP6W","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:15] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7LP2S","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:15] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7LP2W","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:15] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7LP4C","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:15] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7LP5T","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:15] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7MP47","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:15] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7MP6K","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:16] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7MP6Z","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:16] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7MP77","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:16] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7MP7C","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:16] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7NP35","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:16] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7NP9H","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:16] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7NP9I","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:16] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7NPAB","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:16] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7OP19","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:16] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7OP1S","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:16] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7OP4P","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:16] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7OP5Y","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:17] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7OP73","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:17] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7OPAF","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:17] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7OPAH","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:17] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7PP3T","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:17] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7PP44","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:17] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7PP4R","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:17] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7PP59","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:17] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7PP8A","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:17] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7QP14","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:17] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7QP2E","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:18] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7QP3U","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:18] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7QP4F","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:18] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7QP8G","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:18] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7QP8Y","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:18] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7RP4A","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:18] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7RP56","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:18] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7RP7G","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:18] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7RP7Q","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:18] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7SP1P","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:18] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7SP4D","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:19] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7SP63","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:19] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7SP6M","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:19] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7SP9E","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:19] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7TP1P","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:19] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7TP1U","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:19] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7TP2I","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:19] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7TP3Y","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:19] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7TP9Z","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:19] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7UP4A","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:19] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7UP5H","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:19] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7UP6C","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:20] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7UP6H","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:20] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7UP6Z","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:20] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7UP7T","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:20] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7UP7V","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:20] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7UP7X","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:20] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7VP2X","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:20] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7VP4M","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:20] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7VP4W","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:20] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7VP52","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:20] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7VP7J","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:20] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7VP7L","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:21] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7VP7O","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:21] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL7VPBA","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:21] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL81P12","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:21] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL81P14","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:21] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL81P3S","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:21] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL81P4L","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:21] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL81P51","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:21] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL81P5K","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:21] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL81P6J","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:22] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL81P8E","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:22] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL82P2E","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:22] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL82P2H","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:22] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL82P2O","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:22] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL82P3G","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:22] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL82P3K","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:22] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL82P4G","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:22] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL82P5G","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:22] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL82P7F","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:22] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL82P7X","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:22] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL82P85","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:23] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL82P9B","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:23] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL82PAZ","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:23] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL82PBC","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:23] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL83P1G","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:23] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL83P2B","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:23] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL83P2L","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:23] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL83P4A","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:23] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL83P4P","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:23] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL83P59","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:23] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL83P68","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:23] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL83P7Q","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:24] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL84P2A","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:24] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL84P38","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:24] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL84P49","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:24] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL84P4A","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:24] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL84P4D","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:24] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL84P5T","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:24] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL84P64","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:24] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL84P82","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:24] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL84P8M","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:24] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL85P18","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:24] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL85P26","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:25] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL85P4P","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:25] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL85P5U","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:25] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL85P7V","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:25] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL85P8L","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:25] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL86P2L","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:25] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL86P7E","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:25] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL86P7N","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:25] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL86P91","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:25] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL87P5X","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:25] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL87P7N","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:26] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL87P8U","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:26] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL87P9P","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:26] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL88P11","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:26] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL88P1X","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:26] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL88P9A","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:26] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL89P3F","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:26] local.INFO: Available lots cache refreshed via observer {"lot_id":"BL89P6U","lot_code":"AVAILABLE","trigger":"model_observer"} 
[2025-09-28 15:28:26] local.ERROR: Maximum execution time of 30 seconds exceeded {"userId":"21278703","exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 30 seconds exceeded at C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:570)
[stacktrace]
#0 {main}
"} 
[2025-09-28 15:33:34] local.INFO: UpdateWipController@create called {"user_id":"21278703","user_role":"ADMIN","user_emp_no":"21278703","timestamp":"2025-09-28 15:33:34"} 
[2025-09-28 15:33:46] local.INFO: UpdateWipController@create called {"user_id":"21278703","user_role":"ADMIN","user_emp_no":"21278703","timestamp":"2025-09-28 15:33:46"} 
[2025-09-28 15:55:08] local.ERROR: Failed to refresh available lots cache: There is no active transaction  
[2025-09-28 15:55:16] local.ERROR: Failed to refresh available lots cache: There is no active transaction  
[2025-09-28 15:55:22] local.ERROR: Failed to refresh available lots cache: There is no active transaction  
[2025-09-28 15:55:33] local.ERROR: Failed to refresh available lots cache: There is no active transaction  
[2025-09-28 16:46:49] local.ERROR: syntax error, unexpected token "endif" (View: C:\inetpub\wwwroot\process-dashboard\resources\views\lot-requests\assign-lots.blade.php) {"userId":"21278703","exception":"[object] (Illuminate\\View\\ViewException(code: 0): syntax error, unexpected token \"endif\" (View: C:\\inetpub\\wwwroot\\process-dashboard\\resources\\views\\lot-requests\\assign-lots.blade.php) at C:\\inetpub\\wwwroot\\process-dashboard\\storage\\framework\\views\\f14f00e117fb63e2bd9c694bd77a259e.php:401)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(ParseError), 1)
#1 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\inetpub\\\\wwwr...', Array)
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\inetpub\\\\wwwr...', Array)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Middleware\\PermissionMiddleware.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'lot-requests:ma...')
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#58 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#59 {main}

[previous exception] [object] (ParseError(code: 0): syntax error, unexpected token \"endif\" at C:\\inetpub\\wwwroot\\process-dashboard\\storage\\framework\\views\\f14f00e117fb63e2bd9c694bd77a259e.php:401)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\inetpub\\\\wwwr...', Array)
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\inetpub\\\\wwwr...', Array)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\inetpub\\\\wwwr...', Array)
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Middleware\\PermissionMiddleware.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'lot-requests:ma...')
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#60 {main}
"} 
[2025-09-28 16:50:49] local.ERROR: syntax error, unexpected token "endif" (View: C:\inetpub\wwwroot\process-dashboard\resources\views\lot-requests\assign-lots.blade.php) {"userId":"21278703","exception":"[object] (Illuminate\\View\\ViewException(code: 0): syntax error, unexpected token \"endif\" (View: C:\\inetpub\\wwwroot\\process-dashboard\\resources\\views\\lot-requests\\assign-lots.blade.php) at C:\\inetpub\\wwwroot\\process-dashboard\\storage\\framework\\views\\f14f00e117fb63e2bd9c694bd77a259e.php:401)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(ParseError), 1)
#1 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\inetpub\\\\wwwr...', Array)
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\inetpub\\\\wwwr...', Array)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Middleware\\PermissionMiddleware.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'lot-requests:ma...')
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#58 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#59 {main}

[previous exception] [object] (ParseError(code: 0): syntax error, unexpected token \"endif\" at C:\\inetpub\\wwwroot\\process-dashboard\\storage\\framework\\views\\f14f00e117fb63e2bd9c694bd77a259e.php:401)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\inetpub\\\\wwwr...', Array)
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\inetpub\\\\wwwr...', Array)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\inetpub\\\\wwwr...', Array)
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Middleware\\PermissionMiddleware.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): App\\Http\\Middleware\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'lot-requests:ma...')
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#35 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#60 {main}
"} 
[2025-09-28 17:38:04] local.INFO: Chart Totals vs Dashboard Cards: {"Target Capacity":"1,762,900,000 PCS","Total Endtime":"35,500,000 PCS","Submitted Lots":"35,500,000 PCS","Remaining Lots":"0 PCS"} 
[2025-09-28 17:38:25] local.INFO: Chart Totals vs Dashboard Cards: {"Target Capacity":"293,900,000 PCS","Total Endtime":"0 PCS","Submitted Lots":"0 PCS","Remaining Lots":"0 PCS"} 
[2025-09-28 18:23:22] local.INFO: Chart Totals vs Dashboard Cards: {"Target Capacity":"1,762,900,000 PCS","Total Endtime":"35,500,000 PCS","Submitted Lots":"35,500,000 PCS","Remaining Lots":"0 PCS"} 
[2025-09-28 18:31:55] local.ERROR: syntax error, unexpected token "endforeach", expecting "elseif" or "else" or "endif" (View: C:\inetpub\wwwroot\process-dashboard\resources\views\lot-requests\index.blade.php) {"userId":"21278703","exception":"[object] (Illuminate\\View\\ViewException(code: 0): syntax error, unexpected token \"endforeach\", expecting \"elseif\" or \"else\" or \"endif\" (View: C:\\inetpub\\wwwroot\\process-dashboard\\resources\\views\\lot-requests\\index.blade.php) at C:\\inetpub\\wwwroot\\process-dashboard\\storage\\framework\\views\\b454d9751a2c51fc1b65bef36fbd6a5e.php:449)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(59): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(ParseError), 1)
#1 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\inetpub\\\\wwwr...', Array)
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\inetpub\\\\wwwr...', Array)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#57 {main}

[previous exception] [object] (ParseError(code: 0): syntax error, unexpected token \"endforeach\", expecting \"elseif\" or \"else\" or \"endif\" at C:\\inetpub\\wwwroot\\process-dashboard\\storage\\framework\\views\\b454d9751a2c51fc1b65bef36fbd6a5e.php:449)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#1 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(57): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\inetpub\\\\wwwr...', Array)
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(76): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\inetpub\\\\wwwr...', Array)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\inetpub\\\\wwwr...', Array)
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(78): Illuminate\\View\\View->render()
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(34): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(939): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(906): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(821): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#54 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#55 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#56 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#57 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#58 {main}
"} 
[2025-09-28 18:48:18] local.ERROR: syntax error, unexpected token "\", expecting ":" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"\\\", expecting \":\" at C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Controllers\\LotRequestController.php:621)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\inetpub\\\\wwwr...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1119): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1056): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(834): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(816): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#35 {main}
"} 
[2025-09-28 18:48:22] local.ERROR: syntax error, unexpected token "\", expecting ":" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"\\\", expecting \":\" at C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Controllers\\LotRequestController.php:621)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\inetpub\\\\wwwr...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1119): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1056): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(834): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(816): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#35 {main}
"} 
[2025-09-28 18:49:34] local.ERROR: syntax error, unexpected token "\", expecting ":" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"\\\", expecting \":\" at C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Controllers\\LotRequestController.php:621)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\inetpub\\\\wwwr...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1119): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1056): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(834): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(816): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#35 {main}
"} 
[2025-09-28 18:49:36] local.ERROR: syntax error, unexpected token "\", expecting ":" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"\\\", expecting \":\" at C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Controllers\\LotRequestController.php:621)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\inetpub\\\\wwwr...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1119): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1056): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(834): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(816): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#35 {main}
"} 
[2025-09-28 18:49:38] local.ERROR: syntax error, unexpected token "\", expecting ":" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"\\\", expecting \":\" at C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Controllers\\LotRequestController.php:621)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\inetpub\\\\wwwr...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1119): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1056): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(834): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(816): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#35 {main}
"} 
[2025-09-28 18:49:41] local.ERROR: syntax error, unexpected token "\", expecting ":" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"\\\", expecting \":\" at C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Controllers\\LotRequestController.php:621)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\inetpub\\\\wwwr...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1119): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1056): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(834): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(816): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#35 {main}
"} 
[2025-09-28 18:49:45] local.ERROR: syntax error, unexpected token "\", expecting ":" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"\\\", expecting \":\" at C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Controllers\\LotRequestController.php:621)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\inetpub\\\\wwwr...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1119): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1056): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(834): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(816): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#35 {main}
"} 
[2025-09-28 20:45:14] local.INFO: Chart Totals vs Dashboard Cards: {"Target Capacity":"1,762,900,000 PCS","Total Endtime":"35,500,000 PCS","Submitted Lots":"35,500,000 PCS","Remaining Lots":"0 PCS"} 
[2025-09-29 04:25:45] local.INFO: Chart Totals vs Dashboard Cards: {"Target Capacity":"1,762,900,000 PCS","Total Endtime":"0 PCS","Submitted Lots":"0 PCS","Remaining Lots":"0 PCS"} 
[2025-09-29 04:41:38] local.ERROR: The "intl" PHP extension is required to use the [format] method. {"exception":"[object] (RuntimeException(code: 0): The \"intl\" PHP extension is required to use the [format] method. at C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Number.php:437)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Number.php(38): Illuminate\\Support\\Number::ensureIntlExtensionIsInstalled()
#1 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Number.php(214): Illuminate\\Support\\Number::format(26.859375, 2, NULL)
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\ShowCommand.php(176): Illuminate\\Support\\Number::fileSize(26.859375, 2)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\ShowCommand.php(136): Illuminate\\Database\\Console\\ShowCommand->displayForCli(Array)
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\ShowCommand.php(65): Illuminate\\Database\\Console\\ShowCommand->display(Array)
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\ShowCommand->handle(Object(Illuminate\\Database\\DatabaseManager))
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(835): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\ShowCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#19 {main}
"} 
[2025-09-29 04:41:55] local.ERROR: PHP Parse error: Syntax error, unexpected '=' on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected '=' on line 1 at C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php  = \\\\App\\\\M...', false)
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode(' = \\\\App\\\\Models\\\\...', true)
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode(' = \\\\App\\\\Models\\\\...', true)
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute(' = \\\\App\\\\Models\\\\...')
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(835): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-09-29 04:44:38] local.ERROR: syntax error, unexpected token "*", expecting end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"*\", expecting end of file at C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Controllers\\LotRequestController.php:653)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\inetpub\\\\wwwr...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1119): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1056): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(834): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(816): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#35 {main}
"} 
[2025-09-29 04:44:46] local.ERROR: syntax error, unexpected token "*", expecting end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"*\", expecting end of file at C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Controllers\\LotRequestController.php:653)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\inetpub\\\\wwwr...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1119): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1056): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(834): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(816): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#35 {main}
"} 
[2025-09-29 04:45:10] local.ERROR: syntax error, unexpected token "*", expecting end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"*\", expecting end of file at C:\\inetpub\\wwwroot\\process-dashboard\\app\\Http\\Controllers\\LotRequestController.php:653)
[stacktrace]
#0 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\inetpub\\\\wwwr...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1119): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1056): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(834): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(816): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(800): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#7 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(764): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#8 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(753): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#9 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#10 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(219): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(137): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 C:\\inetpub\\wwwroot\\process-dashboard\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#34 C:\\inetpub\\wwwroot\\process-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\inetpub\\\\wwwr...')
#35 {main}
"} 
