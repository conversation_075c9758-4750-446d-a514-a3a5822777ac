<?php

namespace App\Notifications;

use App\Models\LotRequest;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class LotRequestAccepted extends Notification
{
    use Queueable;

    public function __construct(
        public LotRequest $lotRequest,
        public User $manager
    ) {}

    public function via(object $notifiable): array
    {
        return ['database']; // Add 'mail' later if mailer is configured
    }

    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'lot_request_accepted',
            'lot_request_id' => $this->lotRequest->id,
            'request_number' => $this->lotRequest->request_number,
            'new_status' => 'accepted',
            'performed_by' => [
                'emp_no' => $this->manager->emp_no,
                'name' => $this->manager->getDisplayName(),
            ],
            'message' => sprintf(
                'Your lot request %s was accepted by %s (%s).',
                $this->lotRequest->request_number,
                $this->manager->getDisplayName(),
                $this->manager->emp_no
            ),
            'url' => '/lot-requests/' . $this->lotRequest->id,
        ];
    }
}
