<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Equipment extends Model
{
    use HasFactory;

    protected $table = 'equipment';

    protected $fillable = [
        'eqp_no',
        'eqp_line',
        'eqp_area',
        'eqp_maker',
        'cam_class',
        'insp_type',
        'linear_type',
        'eqp_type',
        'size',
        'alloc_type',
        'eqp_status',
        'loading_speed',
        'eqp_oee',
        'eqp_passing',
        'eqp_yield',
        'operation_time',
        'ideal_capa',
        'oee_capa',
        'output_capa',
        'ongoing_lot',
        'modified_by',
    ];

    protected $casts = [
        'eqp_oee' => 'decimal:4',
        'eqp_passing' => 'decimal:4',
        'eqp_yield' => 'decimal:4',
        'loading_speed' => 'integer',
        'operation_time' => 'integer',
        'ideal_capa' => 'integer',
        'oee_capa' => 'integer',
        'output_capa' => 'integer',
    ];
    
    /**
     * Get daily capacity using oee_capa.
     * The OEE capacity is already calculated and stored in the database
     * 
     * @return int
     */
    public function getDailyCapacity()
    {
        return $this->oee_capa ?? 0;
    }
    
    /**
     * Calculate rate per minute for endtime calculations.
     * Uses oee_capa for the calculation
     * 
     * @return float
     */
    public function getRatePerMinute()
    {
        $dailyCapacity = $this->getDailyCapacity();
        
        if (!$dailyCapacity) {
            return 0;
        }
        
        return $dailyCapacity / 1440; // Convert daily capacity to per minute
    }
    
    /**
     * Get formatted daily capacity with thousands separator.
     * Uses oee_capa for display
     * 
     * @return string
     */
    public function getFormattedDailyCapacity()
    {
        return number_format($this->oee_capa ?? 0);
    }
    
    /**
     * Get formatted ideal capacity with thousands separator.
     * 
     * @return string
     */
    public function getFormattedIdealCapacity()
    {
        return number_format($this->ideal_capa ?? 0);
    }
    
    /**
     * Get formatted OEE capacity with thousands separator.
     * 
     * @return string
     */
    public function getFormattedOeeCapacity()
    {
        return number_format($this->oee_capa ?? 0);
    }
    
    /**
     * Get equipment capacity reference based on alloc_type and size
     */
    public function getCapacityReference()
    {
        return EqpCapaRef::getByWorkTypeAndSize($this->alloc_type, $this->size);
    }
    
    /**
     * Get equipment speed reference based on eqp_type and size
     */
    public function getSpeedReference()
    {
        return EqpSpeedRef::getByEqpTypeAndSize($this->eqp_type, $this->size);
    }
    
    /**
     * Auto-populate reference data from lookup tables
     */
    public function populateFromReference()
    {
        // Get speed reference (loading_speed from eqp_speed)
        $speedRef = $this->getSpeedReference();
        if ($speedRef) {
            $this->loading_speed = $speedRef->eqp_speed;
        }
        
        // Get capacity reference (OEE, passing, yield from eqp_capa_ref)
        $capaRef = $this->getCapacityReference();
        if ($capaRef) {
            $this->eqp_oee = $capaRef->oee;
            $this->eqp_passing = $capaRef->passing;
            $this->eqp_yield = $capaRef->yield;
        }
        
        // Calculate capacities
        $this->calculateCapacities();
    }
    
    /**
     * Calculate ideal, OEE, and output capacities using the correct formulas
     */
    public function calculateCapacities()
    {
        if ($this->loading_speed && $this->operation_time) {
            // Ideal capacity = loading_speed * operation_time
            $this->ideal_capa = $this->loading_speed * $this->operation_time;
            
            // OEE capacity = loading_speed * eqp_oee * operation_time
            $this->oee_capa = (int)($this->loading_speed * ($this->eqp_oee ?? 0) * $this->operation_time);
            
            // Output capacity = loading_speed * eqp_oee * eqp_passing * eqp_yield * operation_time
            $this->output_capa = (int)($this->loading_speed * ($this->eqp_oee ?? 0) * ($this->eqp_passing ?? 0) * ($this->eqp_yield ?? 0) * $this->operation_time);
        }
    }
}
