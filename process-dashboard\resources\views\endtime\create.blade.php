<x-app-layout>
    <x-slot name="header">
        ENDTIME | CREATE NEW ENDTIME
    </x-slot>

    <!-- Enhanced CSS for endtime functionality -->
    <link href="{{ asset('css/endtime.css') }}?v={{ time() }}" rel="stylesheet">

    <!-- New Lot Entry & Endtime Forecast -->
    <div class="endtime-header">
        <div class="endtime-title">
            <i class="fas fa-clock"></i> New Lot Entry & Endtime Forecast
        </div>
        <div class="endtime-actions">
            <a href="{{ route('dashboard') }}" class="btn-dashboard">
                <i class="fas fa-home"></i> Dashboard
            </a>
            <a href="{{ route('endtime.index') }}" class="btn-back">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
            <a href="{{ route('endtime.submit.show') }}" class="btn-submit">
                <i class="fas fa-plus-circle"></i> ADD SUBMITTED LOT
            </a>
        </div>
    </div>

    <form id="newLotForm" action="{{ isset($isEditMode) && $isEditMode ? route('endtime.update', $lot->id) : route('endtime.store') }}" method="POST">
        @csrf
        @if(isset($isEditMode) && $isEditMode)
            @method('PUT')
        @endif

        <!-- Display Validation Errors -->
        @if ($errors->any())
            <div class="alert alert-danger mb-3">
                <div class="alert-header">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Validation Error</strong>
                </div>
                <ul class="mb-0 mt-2">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Display Success Messages -->
        @if (session('success'))
            <div class="alert alert-success mb-3">
                <i class="fas fa-check-circle"></i>
                {{ session('success') }}
            </div>
        @endif

        <!-- Lot Information Section -->
        <div class="lot-info-section">
            <div class="section-header">
                <span style="text-align: left;"><i class="fas fa-info-circle"></i> Lot Information</span>
            </div>
            <div class="lot-form-row">
                <!-- Lot ID -->
                <div class="lot-field">
                    <label>Lot No.</label>
                    <div class="lot-id-input">
                        <input type="text" id="lot_id" name="lot_id" placeholder="Lot No.." required maxlength="15"
                               value="{{ old('lot_id', isset($lot) ? $lot->lot_id : '') }}" />
                        <button type="button" class="search-btn" onclick="forceLotLookup()" style="padding: 4px 8px; font-size: 12px;">
                            <i class="fa fa-search"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Lot Qty -->
                <div class="lot-field">
                    <label>Lot Qty</label>
                    <div class="lot-qty-display">
                        <div class="qty-number" id="lotQtyValue">--</div>
                        <div class="qty-label">PCS</div>
                    </div>
                </div>
                
                <!-- Lot Type -->
                <div class="lot-field">
                    <label>Lot Type</label>
                    <div class="lot-type-options">
                        <label class="type-option main-selected">
                            <input type="radio" name="lot_type" value="MAIN" checked />
                            <span class="type-badge main">MAIN</span>
                        </label>
                        <label class="type-option">
                            <input type="radio" name="lot_type" value="WL/RW" />
                            <span class="type-badge wlrw">WL/RW</span>
                        </label>
                        <label class="type-option">
                            <input type="radio" name="lot_type" value="RL/LY" />
                            <span class="type-badge rlly">RL/LY</span>
                        </label>
                    </div>
                </div>
                
                <!-- Lot Details -->
                <div class="lot-field">
                    <label>Lot Details</label>
                    <div class="lot-details">
                        <div class="detail-item">Lot No: <span id="detailLotNo">--</span></div>
                        <div class="detail-item">Model: <span id="detailModel">--</span></div>
                        <div class="detail-item">LIPAS: <span id="detailLipas">--</span></div>
                        <div class="detail-item">Size: <span id="detailSize">--</span></div>
                    </div>
                </div>
                
                <!-- Spacer -->
                <div class="lot-field"></div>
                
                <!-- Calculation Results -->
                <div class="lot-field calculation-result">
                    <div class="calc-header">
                        <i class="fas fa-calculator"></i> Calculation Results
                    </div>
                    <div class="calc-content">
                        <div class="end-time">---,--,----</div>
                        <div class="time-diff">--:--</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Equipment Assignment Section -->
        <div class="equipment-section">
            <div class="section-header-with-btn">
                <div class="section-title">
                    <i class="fas fa-cogs"></i> Equipment Assignment & Loading Times
                    <span class="mc-count">1 Mc's</span>
                </div>
                <button type="button" class="add-equipment-btn" id="addEquipmentBtn">
                    <i class="fas fa-plus"></i> Add Equipment
                </button>
            </div>
            
            <div class="equipment-table">
                <table>
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>EQUIPMENT</th>
                            <th>NG %</th>
                            <th>START TIME</th>
                            <th>CAPACITY</th>
                            <th>EST. END</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody id="equipmentTableBody">
                        <tr class="equipment-row" data-index="1">
                            <td class="row-number">1</td>
                            <td>
                                <div class="equipment-search-wrapper">
                                    <div class="search-input-container">
                                        <i class="fas fa-search search-icon"></i>
                                        <input type="text" class="equipment-search form-control" id="equipment_1" name="equipment[1][eqp_no]" placeholder="Search equipment..." autocomplete="off" required />
                                        <div class="loading-spinner d-none" id="equipmentSpinner_1">
                                            <i class="fas fa-spinner fa-spin"></i>
                                        </div>
                                    </div>
                                    <div class="equipment-dropdown d-none" id="equipmentDropdown_1">
                                        <div class="dropdown-header">
                                            <span class="dropdown-title">Available Equipment</span>
                                            <span class="results-count" id="equipmentResultsCount_1">0 results</span>
                                        </div>
                                        <div class="dropdown-body" id="equipmentResults_1">
                                            <!-- Dynamic equipment options will be inserted here -->
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="ng-input">
                                    <input type="number" id="ng_percent_1" name="equipment[1][ng_percent]" value="0" min="0" max="100" step="0.1" />
                                    <span>%</span>
                                </div>
                            </td>
                            <td>
                                <div class="time-input">
                                    <input type="datetime-local" id="start_time_1" name="equipment[1][start_time]" step="60" placeholder="mm/dd/yyyy -- --" required />
                                    <button type="button" class="time-now-btn" title="Set to current time" data-equipment-index="1">
                                        <i class="fas fa-clock"></i>
                                    </button>
                                </div>
                            </td>
                            <td class="equipment-capacity" id="equipmentCapacity_1">
                                <i class="fas fa-calendar-alt"></i>
                            </td>
                            <td class="equipment-endtime" id="equipmentEndTime_1">
                                <i class="fas fa-times"></i>
                            </td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Form Actions -->
        <div class="form-actions">
            <a href="{{ route('endtime.index') }}" class="btn-cancel">
                <i class="fas fa-times"></i> Cancel
            </a>
            <button type="submit" class="btn-save" id="submitForecastBtn">
                <i class="fas fa-save"></i> Save Lot Entry
            </button>
        </div>
        
        <!-- Hidden form fields -->
        <input type="hidden" id="model_15" name="model_15" />
        <input type="hidden" id="lot_size" name="lot_size" value="10" />
        <input type="hidden" id="lot_qty" name="lot_qty" />
        <input type="hidden" id="work_type" name="work_type" value="NORMAL" />
        <input type="hidden" id="lot_type_hidden" name="lot_type" value="MAIN" />
        <input type="hidden" id="lipas_yn" name="lipas_yn" value="N" />
        <input type="hidden" id="edit_lot_id" name="edit_lot_id" />
    </form>
    
    <!-- JavaScript -->
    <script src="{{ asset('js/endtime.js') }}?v={{ time() }}"></script>
    
    <!-- Lot Data Pre-population Script -->
    <script>
        // Check if we're editing an existing lot
        document.addEventListener('DOMContentLoaded', function() {
            // Check URL parameters for edit mode
            const urlParams = new URLSearchParams(window.location.search);
            const editLotId = urlParams.get('edit');
            
            if (editLotId) {
                // Set the edit_lot_id hidden field
                const editLotIdInput = document.getElementById('edit_lot_id');
                if (editLotIdInput) {
                    editLotIdInput.value = editLotId;
                }
                
                // Update UI to indicate edit mode
                updateUIForEditMode(editLotId);
                
                // Check for stored lot data in sessionStorage
                const storedLotData = sessionStorage.getItem('editLotData');
                
                if (storedLotData) {
                    try {
                        const lotData = JSON.parse(storedLotData);
                        
                        // Pre-populate the form with lot data
                        populateFormWithLotData(lotData);
                        
                        // Clear the stored data after use
                        sessionStorage.removeItem('editLotData');
                        
                    } catch (error) {
                        // Handle error silently or show user-friendly message
                    }
                }
            }
        });
        
        /**
         * Populate the create form with existing lot data for editing
         */
        function populateFormWithLotData(lotData) {
            try {
                // Basic lot information
                if (lotData.lot_id && document.getElementById('lot_id')) {
                    document.getElementById('lot_id').value = lotData.lot_id;
                }
                
                if (lotData.model_15 && document.getElementById('model_15')) {
                    document.getElementById('model_15').value = lotData.model_15;
                }
                
                if (lotData.lot_size && document.getElementById('lot_size')) {
                    document.getElementById('lot_size').value = lotData.lot_size;
                }
                
                if (lotData.lot_qty) {
                    if (document.getElementById('lot_qty')) {
                        document.getElementById('lot_qty').value = lotData.lot_qty;
                    }
                    // Update the display
                    const qtyDisplay = document.getElementById('lotQtyValue');
                    if (qtyDisplay) {
                        qtyDisplay.textContent = parseInt(lotData.lot_qty).toLocaleString();
                    }
                }
                
                if (lotData.work_type && document.getElementById('work_type')) {
                    document.getElementById('work_type').value = lotData.work_type;
                }
                
                if (lotData.lipas_yn && document.getElementById('lipas_yn')) {
                    document.getElementById('lipas_yn').value = lotData.lipas_yn;
                }
                
                // Lot type radio buttons
                if (lotData.lot_type) {
                    const lotTypeRadios = document.querySelectorAll('input[name="lot_type"]');
                    lotTypeRadios.forEach(radio => {
                        if (radio && radio.value === lotData.lot_type) {
                            radio.checked = true;
                            // Update the visual styling
                            const typeOption = radio.closest('.type-option');
                            if (typeOption) {
                                typeOption.classList.add('main-selected');
                            }
                        } else if (radio) {
                            radio.checked = false;
                            const typeOption = radio.closest('.type-option');
                            if (typeOption) {
                                typeOption.classList.remove('main-selected');
                            }
                        }
                    });
                    
                    // Update hidden field
                    if (document.getElementById('lot_type_hidden')) {
                        document.getElementById('lot_type_hidden').value = lotData.lot_type;
                    }
                    
                    // Trigger lot type change handler if it's available
                    if (typeof handleLotTypeChange === 'function') {
                        handleLotTypeChange(lotData.lot_type);
                    }
                }
                
                // Update lot details display
                updateLotDetailsDisplay(lotData);
                
                // Pre-populate equipment data if available
                populateEquipmentData(lotData);
                
            } catch (error) {
                // Try to continue with equipment population despite the error
                try {
                    populateEquipmentData(lotData);
                } catch (equipmentError) {
                    // Handle equipment population error silently
                }
            }
        }
        
        /**
         * Update the lot details display section
         */
        function updateLotDetailsDisplay(lotData) {
            const elements = {
                'detailLotNo': lotData.lot_id,
                'detailModel': lotData.model_15,
                'detailLipas': lotData.lipas_yn === 'Y' ? 'Yes' : 'No',
                'detailSize': lotData.lot_size
            };
            
            Object.keys(elements).forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element && elements[elementId]) {
                    element.textContent = elements[elementId];
                }
            });
        }
        
        /**
         * Pre-populate equipment data from the existing lot
         */
        function populateEquipmentData(lotData) {
            if (!lotData) {
                return;
            }
            
            // Get all equipment fields from the lot data
            const equipmentAssignments = [];
            
            // Collect equipment data including equipment number, start time, and NG percent
            for (let i = 1; i <= 10; i++) {
                const equipmentNo = lotData[`eqp_${i}`];
                const startTime = lotData[`start_time_${i}`];
                const ngPercent = lotData[`ng_percent_${i}`] || 0;
                
                
                if (equipmentNo && equipmentNo.trim() !== '') {
                    equipmentAssignments.push({
                        index: i,
                        equipmentNo: equipmentNo,
                        startTime: startTime,
                        ngPercent: ngPercent
                    });
                }
            }
            
            
            if (equipmentAssignments.length > 0) {
                // Clear existing equipment rows (except the first one)
                const tableBody = document.getElementById('equipmentTableBody');
                if (tableBody) {
                    // Keep only the first row, remove others
                    const rows = tableBody.querySelectorAll('.equipment-row');
                    for (let i = 1; i < rows.length; i++) {
                        rows[i].remove();
                    }
                }
                
                // First, ensure we have enough equipment rows
                
                // Add additional rows if needed
                for (let i = 2; i <= equipmentAssignments.length; i++) {
                    const existingRow = document.getElementById(`equipment_${i}`);
                    if (!existingRow) {
                        if (typeof window.addEquipmentRow === 'function') {
                            window.addEquipmentRow();
                        } else {
                            break;
                        }
                    }
                }
                
                // Wait a bit for all rows to be created, then populate them
                setTimeout(() => {
                    equipmentAssignments.forEach((assignment, index) => {
                        const rowIndex = index + 1;
                        
                        // Set the equipment input value
                        const equipmentInput = document.getElementById(`equipment_${rowIndex}`);
                        if (equipmentInput) {
                            equipmentInput.value = assignment.equipmentNo;
                            
                            // Trigger equipment info update to show capacity
                            if (typeof window.updateEquipmentInfo === 'function') {
                                window.updateEquipmentInfo(equipmentInput);
                            }
                        }
                        
                        // Set the start time if available
                        if (assignment.startTime) {
                            const startTimeInput = document.getElementById(`start_time_${rowIndex}`);
                            if (startTimeInput) {
                                // Format the start time for datetime-local input (YYYY-MM-DDTHH:MM)
                                let formattedTime = assignment.startTime;
                                if (formattedTime && !formattedTime.includes('T')) {
                                    // If it doesn't have 'T', assume it's in 'YYYY-MM-DD HH:MM:SS' format
                                    formattedTime = formattedTime.replace(' ', 'T');
                                    // Remove seconds if present
                                    if (formattedTime.split('T')[1] && formattedTime.split('T')[1].split(':').length > 2) {
                                        formattedTime = formattedTime.substring(0, 16); // Keep YYYY-MM-DDTHH:MM
                                    }
                                }
                                startTimeInput.value = formattedTime;
                            }
                        }
                        
                        // Set the NG percent
                        const ngPercentInput = document.getElementById(`ng_percent_${rowIndex}`);
                        if (ngPercentInput) {
                            ngPercentInput.value = assignment.ngPercent;
                        }
                    });
                    
                }, 500); // Give more time for rows to be created
                
                // Update machine count display and trigger calculation
                setTimeout(() => {
                    const mcCount = document.querySelector('.mc-count');
                    if (mcCount) {
                        mcCount.textContent = `${equipmentAssignments.length} Mc's`;
                    }
                    
                    // Trigger calculation after all equipment is populated
                    if (typeof window.calculateEndtime === 'function') {
                        setTimeout(() => {
                            window.calculateEndtime();
                        }, 200);
                    }
                }, 800); // Give more time for all population to complete
            }
        }
        
        /**
         * Update UI elements to indicate edit mode
         */
        function updateUIForEditMode(editLotId) {
            // Update the page title
            const pageTitle = document.querySelector('.endtime-title');
            if (pageTitle) {
                pageTitle.innerHTML = '<i class="fas fa-edit"></i> Edit Lot Entry & Endtime Forecast';
            }
            
            // Update the submit button text
            const submitBtn = document.getElementById('submitForecastBtn');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-save"></i> Update Lot Entry';
            }
            
            // Add edit indicator to the header
            const headerTitle = document.querySelector('x-slot[name="header"]');
            if (headerTitle) {
                headerTitle.textContent = `ENDTIME | EDIT LOT ${editLotId}`;
            } else {
                // Alternative method if x-slot is not directly accessible
                document.title = `Edit Lot ${editLotId} - Process Dashboard`;
            }
            
            // Add a visual indicator badge
            const header = document.querySelector('.endtime-header');
            if (header) {
                const editBadge = document.createElement('div');
                editBadge.className = 'alert alert-info';
                editBadge.style.marginBottom = '1rem';
                editBadge.innerHTML = `
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Edit Mode:</strong> You are currently editing lot <strong>${editLotId}</strong>. 
                    Make your changes and click "Update Lot Entry" to save.
                `;
                header.insertAdjacentElement('afterend', editBadge);
            }
        }

        // Set edit mode flag if applicable
        @if(isset($isEditMode) && $isEditMode)
            window.isEditMode = true;
            window.editLotId = {{ $lot->id }};
        @else
            window.isEditMode = false;
        @endif
    </script>
</x-app-layout>
