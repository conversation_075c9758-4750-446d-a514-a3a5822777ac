<x-app-layout>
    <x-slot name="header">
        Edit Equipment
    </x-slot>

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark py-2">
                    <h6 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Edit Equipment - {{ $equipment->eqp_no }}
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('equipment.update', $equipment) }}" id="equipment-edit-form">
                        @csrf
                        @method('PUT')
                        
                        <!-- Hidden field to preserve filter parameters -->
                        @if(isset($filterQuery) && !empty($filterQuery))
                            <input type="hidden" name="return_filters" value="{{ $filterQuery }}">
                        @endif
                        
                        <!-- Compact Equipment Info & Editable Fields -->
                        <div class="mb-3">
                            <div class="row g-2">
                                <!-- Equipment Info (Read-only) -->
                                <div class="col-md-6">
                                    <div class="card h-100 border-secondary" style="border-width: 2px !important;">
                                        <div class="card-header py-2" style="background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%); border: none;">
                                            <small class="fw-bold text-white"><i class="fas fa-info-circle me-1"></i>Equipment Information</small>
                                        </div>
                                        <div class="card-body py-2" style="background-color: #f8f9fa;">
                                            <div class="row g-2">
                                                <div class="col-4">
                                                    <small class="text-muted fw-semibold">Number:</small><br>
                                                    <strong class="text-secondary" style="font-size: 0.95rem;">{{ $equipment->eqp_no }}</strong>
                                                </div>
                                                <div class="col-4">
                                                    <small class="text-muted fw-semibold">Type:</small><br>
                                                    <strong class="text-secondary" style="font-size: 0.95rem;">{{ $equipment->eqp_type }}</strong>
                                                </div>
                                                <div class="col-4">
                                                    <small class="text-muted fw-semibold">Maker:</small><br>
                                                    <strong class="text-secondary" style="font-size: 0.95rem;">{{ $equipment->eqp_maker }}</strong>
                                                </div>
                                                @if($equipment->eqp_status)
                                                <div class="col-12 mt-2">
                                                    <small class="text-muted fw-semibold">Status:</small>
                                                    <span class="badge {{ $equipment->eqp_status === 'OPERATIONAL' ? 'bg-success' : 'bg-secondary' }} ms-2">
                                                        {{ $equipment->eqp_status }}
                                                    </span>
                                                </div>
                                                @endif
                                            </div>
                                            <input type="hidden" name="eqp_no" value="{{ $equipment->eqp_no }}">
                                            <input type="hidden" name="eqp_type" value="{{ $equipment->eqp_type }}">
                                            <input type="hidden" name="eqp_maker" value="{{ $equipment->eqp_maker }}">
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Editable Fields -->
                                <div class="col-md-6">
                                    <div class="card h-100 border-primary" style="border-width: 2px !important;">
                                        <div class="card-header py-2" style="background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%); border: none;">
                                            <small class="fw-bold text-white"><i class="fas fa-edit me-1"></i>Editable Fields</small>
                                        </div>
                                        <div class="card-body py-2" style="background-color: #f8f9ff;">
                                            <div class="row g-2">
                                                <div class="col-6">
                                                    <label for="eqp_line" class="form-label mb-1 fw-semibold" style="font-size: 0.85rem; color: #0d6efd;">Line <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control form-control-sm border-primary @error('eqp_line') is-invalid @enderror" 
                                                           name="eqp_line" id="eqp_line" value="{{ old('eqp_line', $equipment->eqp_line) }}" required
                                                           style="border-width: 1.5px; background-color: #fff; box-shadow: 0 0 0 0.1rem rgba(13, 110, 253, 0.15);">
                                                </div>
                                                <div class="col-6">
                                                    <label for="eqp_area" class="form-label mb-1 fw-semibold" style="font-size: 0.85rem; color: #0d6efd;">Area</label>
                                                    <input type="text" class="form-control form-control-sm border-primary @error('eqp_area') is-invalid @enderror" 
                                                           name="eqp_area" id="eqp_area" value="{{ old('eqp_area', $equipment->eqp_area) }}"
                                                           style="border-width: 1.5px; background-color: #fff; box-shadow: 0 0 0 0.1rem rgba(13, 110, 253, 0.15);">
                                                </div>
                                                <div class="col-4">
                                                    <label for="size" class="form-label mb-1 fw-semibold" style="font-size: 0.85rem; color: #0d6efd;">Size <span class="text-danger">*</span></label>
                                                    <select class="form-select form-select-sm border-primary @error('size') is-invalid @enderror" 
                                                            name="size" id="size" required onchange="updateReferenceData()"
                                                            style="border-width: 1.5px; background-color: #fff; box-shadow: 0 0 0 0.1rem rgba(13, 110, 253, 0.15);">
                                                        <option value="">Select</option>
                                                        @if(isset($referenceData['sizes']))
                                                            @foreach($referenceData['sizes'] as $sizeOption)
                                                                <option value="{{ $sizeOption }}" {{ old('size', $equipment->size) === $sizeOption ? 'selected' : '' }}>{{ $sizeOption }}</option>
                                                            @endforeach
                                                        @endif
                                                    </select>
                                                </div>
                                                <div class="col-5">
                                                    <label for="alloc_type" class="form-label mb-1 fw-semibold" style="font-size: 0.85rem; color: #0d6efd;">Alloc Type <span class="text-danger">*</span></label>
                                                    <select class="form-select form-select-sm border-primary @error('alloc_type') is-invalid @enderror" 
                                                            name="alloc_type" id="alloc_type" required onchange="updateReferenceData()"
                                                            style="border-width: 1.5px; background-color: #fff; box-shadow: 0 0 0 0.1rem rgba(13, 110, 253, 0.15);">
                                                        <option value="">Select</option>
                                                        @if(isset($referenceData['alloc_types']))
                                                            @foreach($referenceData['alloc_types'] as $type)
                                                                <option value="{{ $type }}" {{ old('alloc_type', $equipment->alloc_type) === $type ? 'selected' : '' }}>{{ $type }}</option>
                                                            @endforeach
                                                        @endif
                                                    </select>
                                                </div>
                                                <div class="col-3">
                                                    <label for="operation_time" class="form-label mb-1 fw-semibold" style="font-size: 0.85rem; color: #0d6efd;">Op Time <span class="text-danger">*</span></label>
                                                    <input type="number" class="form-control form-control-sm border-primary @error('operation_time') is-invalid @enderror" 
                                                           name="operation_time" id="operation_time" value="{{ old('operation_time', $equipment->operation_time) }}" 
                                                           min="0" step="1" required onchange="calculateCapacities()" placeholder="1440"
                                                           style="border-width: 1.5px; background-color: #fff; box-shadow: 0 0 0 0.1rem rgba(13, 110, 253, 0.15);">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                            </div>
                        </div>
                        
                        <!-- Reference Data & Status (Compact) -->
                        <div class="mb-3">
                            <div class="card">
                                <div class="card-header py-2 bg-success text-white">
                                    <small class="fw-bold"><i class="fas fa-database me-1"></i>Reference Data (Auto-populated)</small>
                                </div>
                                <div class="card-body py-2">
                                    <div class="row g-2">
                                        <div class="col-md-2">
                                            <small class="text-muted">Loading Speed:</small><br>
                                            <strong id="loading_speed_display" class="text-primary">{{ number_format($equipment->loading_speed) }}</strong>
                                            <small class="text-muted"> units/min</small>
                                            <input type="hidden" name="loading_speed" id="loading_speed" value="{{ $equipment->loading_speed }}">
                                        </div>
                                        <div class="col-md-2">
                                            <small class="text-muted">OEE:</small><br>
                                            <strong id="eqp_oee_display" class="text-primary">{{ number_format($equipment->eqp_oee, 4) }}</strong>
                                            <input type="hidden" name="eqp_oee" id="eqp_oee" value="{{ $equipment->eqp_oee }}">
                                        </div>
                                        <div class="col-md-2">
                                            <small class="text-muted">Passing:</small><br>
                                            <strong id="eqp_passing_display" class="text-primary">{{ number_format($equipment->eqp_passing, 4) }}</strong>
                                            <input type="hidden" name="eqp_passing" id="eqp_passing" value="{{ $equipment->eqp_passing }}">
                                        </div>
                                        <div class="col-md-2">
                                            <small class="text-muted">Yield:</small><br>
                                            <strong id="eqp_yield_display" class="text-primary">{{ number_format($equipment->eqp_yield, 4) }}</strong>
                                            <input type="hidden" name="eqp_yield" id="eqp_yield" value="{{ $equipment->eqp_yield }}">
                                        </div>
                                        <div class="col-md-2">
                                            <small class="text-muted">Status:</small><br>
                                            <span class="badge {{ $equipment->eqp_status === 'OPERATIONAL' ? 'bg-success' : 'bg-secondary' }}">{{ $equipment->eqp_status }}</span>
                                            <input type="hidden" name="eqp_status" value="{{ $equipment->eqp_status }}">
                                        </div>
                                        <div class="col-md-2">
                                            <small class="text-muted">Ongoing Lot:</small><br>
                                            <input type="text" class="form-control form-control-sm @error('ongoing_lot') is-invalid @enderror" 
                                                   name="ongoing_lot" id="ongoing_lot" value="{{ old('ongoing_lot', $equipment->ongoing_lot) }}" placeholder="Optional">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Calculated Capacities & Additional Info (Compact) -->
                        <div class="mb-3">
                            <div class="row g-2">
                                <!-- Calculated Capacities -->
                                <div class="col-md-8">
                                    <div class="card h-100">
                                        <div class="card-header py-2 bg-info text-white">
                                            <small class="fw-bold"><i class="fas fa-calculator me-1"></i>Calculated Capacities (Auto-updated)</small>
                                        </div>
                                        <div class="card-body py-2">
                                            <div class="row g-2">
                                                <div class="col-4">
                                                    <small class="text-muted">Ideal Capacity:</small><br>
                                                    <strong id="ideal_capa_display" class="text-info fs-5">{{ number_format($equipment->ideal_capa ?? 0) }}</strong>
                                                    <small class="text-muted"> units/day</small><br>
                                                    <small class="text-muted fst-italic">speed × time</small>
                                                    <input type="hidden" name="ideal_capa" id="ideal_capa" value="{{ $equipment->ideal_capa ?? 0 }}">
                                                </div>
                                                <div class="col-4">
                                                    <small class="text-muted">OEE Capacity:</small><br>
                                                    <strong id="oee_capa_display" class="text-primary fs-5">{{ number_format($equipment->oee_capa ?? 0) }}</strong>
                                                    <small class="text-muted"> units/day</small><br>
                                                    <small class="text-muted fst-italic">speed × oee × time</small>
                                                    <input type="hidden" name="oee_capa" id="oee_capa" value="{{ $equipment->oee_capa ?? 0 }}">
                                                </div>
                                                <div class="col-4">
                                                    <small class="text-muted">Output Capacity:</small><br>
                                                    <strong id="output_capa_display" class="text-success fs-5">{{ number_format($equipment->output_capa ?? 0) }}</strong>
                                                    <small class="text-muted"> units/day</small><br>
                                                    <small class="text-muted fst-italic">speed × oee × pass × yield × time</small>
                                                    <input type="hidden" name="output_capa" id="output_capa" value="{{ $equipment->output_capa ?? 0 }}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Additional Information -->
                                <div class="col-md-4">
                                    <div class="card h-100">
                                        <div class="card-header py-2 bg-secondary text-white">
                                            <small class="fw-bold"><i class="fas fa-info-circle me-1"></i>Additional Info</small>
                                        </div>
                                        <div class="card-body py-2">
                                            <div class="row g-2">
                                                <div class="col-12">
                                                    <small class="text-muted">CAM Class:</small> <strong>{{ $equipment->cam_class ?? 'N/A' }}</strong>
                                                    <input type="hidden" name="cam_class" value="{{ $equipment->cam_class }}">
                                                </div>
                                                <div class="col-12">
                                                    <small class="text-muted">Inspection:</small> <strong>{{ $equipment->insp_type ?? 'N/A' }}</strong>
                                                    <input type="hidden" name="insp_type" value="{{ $equipment->insp_type }}">
                                                </div>
                                                <div class="col-12">
                                                    <small class="text-muted">Linear Type:</small> <strong>{{ $equipment->linear_type ?? 'N/A' }}</strong>
                                                    <input type="hidden" name="linear_type" value="{{ $equipment->linear_type }}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action Buttons (Compact) -->
                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="d-flex gap-2 flex-wrap">
                                    <button type="submit" class="btn btn-warning btn-sm">
                                        <i class="fas fa-save me-1"></i>Update Equipment
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="updateReferenceData()">
                                        <i class="fas fa-sync me-1"></i>Refresh Data
                                    </button>
                                    <a href="{{ route('equipment.show', $equipment) }}" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-eye me-1"></i>View Details
                                    </a>
                                    <a href="{{ route('equipment.index') }}" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-arrow-left me-1"></i>Back
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Equipment data for reference
        const equipmentData = {
            eqp_type: '{{ $equipment->eqp_type }}',
            current_size: '{{ $equipment->size }}',
            current_alloc_type: '{{ $equipment->alloc_type }}'
        };
        
        // Reference data for lookups
        const referenceData = @json($referenceData ?? []);
        
        function updateReferenceData() {
            const size = document.getElementById('size').value;
            const allocType = document.getElementById('alloc_type').value;
            
            if (!size || !allocType) {
                alert('Please select both Size and Allocation Type to update reference data.');
                return;
            }
            
            // Show loading indicator
            const submitBtn = document.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
            submitBtn.disabled = true;
            
            // Build URL with parameters
            const params = new URLSearchParams({
                eqp_type: equipmentData.eqp_type,
                size: size,
                alloc_type: allocType
            });
            const url = '/equipment/reference-data?' + params.toString();
            
            // Fetch reference data
            fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                credentials: 'same-origin'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Unable to fetch reference data (${response.status})`);
                }
                return response.json();
            })
            .then(data => {
                
                if (data.success && data.data) {
                    // Update loading speed from eqp_speed_ref
                    if (data.data.loading_speed) {
                        document.getElementById('loading_speed').value = data.data.loading_speed;
                        document.getElementById('loading_speed_display').textContent = new Intl.NumberFormat().format(data.data.loading_speed);
                    }
                    
                    // Update OEE parameters from eqp_capa_ref
                    if (data.data.eqp_oee) {
                        document.getElementById('eqp_oee').value = data.data.eqp_oee;
                        document.getElementById('eqp_oee_display').textContent = parseFloat(data.data.eqp_oee).toFixed(4);
                    }
                    if (data.data.eqp_passing) {
                        document.getElementById('eqp_passing').value = data.data.eqp_passing;
                        document.getElementById('eqp_passing_display').textContent = parseFloat(data.data.eqp_passing).toFixed(4);
                    }
                    if (data.data.eqp_yield) {
                        document.getElementById('eqp_yield').value = data.data.eqp_yield;
                        document.getElementById('eqp_yield_display').textContent = parseFloat(data.data.eqp_yield).toFixed(4);
                    }
                    
                    // Recalculate capacities
                    calculateCapacities();
                }
            })
            .catch(error => {
                // Silent error handling - no user notification
            })
            .finally(() => {
                // Restore button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        }
        
        function calculateCapacities() {
            const loadingSpeed = parseFloat(document.getElementById('loading_speed').value) || 0;
            const operationTime = parseFloat(document.getElementById('operation_time').value) || 0;
            const eqpOee = parseFloat(document.getElementById('eqp_oee').value) || 0;
            const eqpPassing = parseFloat(document.getElementById('eqp_passing').value) || 0;
            const eqpYield = parseFloat(document.getElementById('eqp_yield').value) || 0;
            
            if (loadingSpeed && operationTime) {
                // Calculate capacities using correct formulas
                const idealCapa = loadingSpeed * operationTime;
                const oeeCapa = Math.floor(loadingSpeed * eqpOee * operationTime);
                const outputCapa = Math.floor(loadingSpeed * eqpOee * eqpPassing * eqpYield * operationTime);
                
                // Update display fields
                document.getElementById('ideal_capa_display').textContent = new Intl.NumberFormat().format(idealCapa);
                document.getElementById('oee_capa_display').textContent = new Intl.NumberFormat().format(oeeCapa);
                document.getElementById('output_capa_display').textContent = new Intl.NumberFormat().format(outputCapa);
                
                // Update hidden fields for form submission
                document.getElementById('ideal_capa').value = idealCapa;
                document.getElementById('oee_capa').value = oeeCapa;
                document.getElementById('output_capa').value = outputCapa;
            }
        }
        
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show mt-3`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const form = document.getElementById('equipment-edit-form');
            form.insertBefore(alertDiv, form.firstChild);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
        
        // Auto-calculate capacities on page load and when operation time changes
        document.addEventListener('DOMContentLoaded', function() {
            calculateCapacities();
        });
    </script>
</x-app-layout>
