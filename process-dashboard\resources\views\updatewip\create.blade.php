<x-app-layout>
    <x-slot name="header">
        Update Process WIP
    </x-slot>

    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Update Process WIP</h4>
                <a href="{{ route('updatewip.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Process WIP
                </a>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-10 col-md-12 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-database me-2"></i>WIP Data Import
                        <span class="badge bg-light text-primary ms-2">Current Records: {{ $currentCount }}</span>
                        @if($lastUpdateTime)
                            <span class="badge bg-light text-primary ms-2">Last Updated: {{ $lastUpdateTime->format('M d, Y H:i') }}</span>
                        @endif
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="card bg-info-transparent">
                                <div class="card-body text-center">
                                    <ol class="mb-0 mt-2">
                                        <li class="text-info" align="left">Copy the WIP data from Excel (including headers)</li>
                                        <li class="text-info" align="left">Paste the data into the text area below</li>
                                        <li class="text-info" align="left">Click "Update WIP Data" to process</li>
                                        <li class="text-danger" align="left"><strong>Warning:</strong> This will clear all existing WIP data before importing new data</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <a href="{{ route('updatewip.download.template') }}" class="text-decoration-none">
                                <div class="card bg-success-transparent border-success" style="transition: transform 0.2s; cursor: pointer;" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                    <div class="card-body text-center">
                                        <i class="fas fa-file-excel text-success fa-2x mb-2"></i>
                                        <h6 class="text-success">Excel Format</h6>
                                        <div class="mt-2">
                                            <i class="fas fa-download me-2"></i>
                                            <small class="text-success">Click to download template</small>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>

                    <form action="{{ route('updatewip.store') }}" method="POST">
                        @csrf
                        
                        <div class="mb-4">
                            <label for="raw_data" class="form-label">
                                <strong>Excel Data <span class="text-danger">*</span></strong>
                                <small class="text-muted">(Paste copied Excel data here)</small>
                            </label>
                            <textarea class="form-control @error('raw_data') is-invalid @enderror font-monospace" 
                                      id="raw_data" 
                                      name="raw_data" 
                                      rows="15" 
                                      style="height: 300px; resize: vertical;"
                                      placeholder="Paste your Excel data here..."
                                      required>{{ session('success') ? '' : old('raw_data') }}</textarea>
                            @error('raw_data')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('updatewip.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-danger" onclick="return confirm('This will delete all existing WIP data. Are you sure?')">
                                <i class="fas fa-sync-alt me-2"></i>Update WIP Data
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Helper Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const textarea = document.getElementById('raw_data');
            
            // Show preview of data structure
            textarea.addEventListener('input', function() {
                const lines = this.value.split('\n').filter(line => line.trim() !== '');
                const lineCount = lines.length - 1; // Subtract 1 for header
                
                let statusText = '';
                if (lines.length > 0) {
                    statusText = `${lineCount} data rows detected`;
                    if (lines.length > 1) {
                        const firstDataLine = lines[1].split('\t');
                        statusText += ` (${firstDataLine.length} columns)`;
                    }
                }
                
                // Update or create status display
                let statusDiv = document.getElementById('data-status');
                if (!statusDiv) {
                    statusDiv = document.createElement('div');
                    statusDiv.id = 'data-status';
                    statusDiv.className = 'form-text text-info mt-1';
                    textarea.parentNode.appendChild(statusDiv);
                }
                statusDiv.innerHTML = statusText ? `<i class="fas fa-info-circle me-1"></i>${statusText}` : '';
            });
        });
    </script>
</x-app-layout>
