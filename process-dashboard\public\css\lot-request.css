/*
* Lot Request Analytics Dashboard Styles
* Modern, responsive design with animations and dark mode support
*/

:root {
  --primary: #667eea;
  --secondary: #764ba2;
  --success: #32d484;
  --info: #17a2b8;
  --warning: #f6c343;
  --danger: #e74c3c;
  --light: #f8f9ff;
  --dark: #2c3e50;
  --muted: #6c757d;
  
  /* Chart Colors */
  --chart-primary: #667eea;
  --chart-success: #32d484;
  --chart-warning: #f6c343;
  --chart-danger: #e74c3c;
  --chart-info: #17a2b8;
  --chart-purple: #764ba2;
  --chart-orange: #fd7e14;
  --chart-teal: #20c997;
  
  /* Shadow */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
  --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.2);
  
  /* Border Radius */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  
  /* Transitions */
  --transition-fast: all 0.2s ease;
  --transition-normal: all 0.3s ease;
  --transition-slow: all 0.5s ease;
}

/* Dark Mode Variables */
[data-theme-mode="dark"] {
  --light: #1a1d29;
  --dark: #ffffff;
  --muted: #a0a6b8;
}

/* Base Styles */
.lot-analytics-dashboard {
  background: var(--light);
  min-height: 100vh;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Animation Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-6px);
  }
  70% {
    transform: translateY(-3px);
  }
  90% {
    transform: translateY(-1px);
  }
}

/* Quick Navigation Cards */
.nav-cards-container {
  margin-bottom: 2rem;
}

.nav-card-link {
  text-decoration: none;
  display: block;
  transition: var(--transition-normal);
}

.nav-card-link:hover {
  transform: translateY(-4px);
}

.nav-card {
  background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  height: 100%;
  position: relative;
  overflow: hidden;
  transition: var(--transition-normal);
  color: white;
  box-shadow: var(--shadow-md);
}

.nav-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1), transparent);
  transition: var(--transition-normal);
}

.nav-card:hover::before {
  opacity: 1;
}

.nav-card.primary {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
}

.nav-card.success {
  background: linear-gradient(135deg, var(--success), #28a745);
}

.nav-card.warning {
  background: linear-gradient(135deg, var(--warning), #fd7e14);
}

.nav-card.info {
  background: linear-gradient(135deg, var(--info), #6f42c1);
}

.nav-card-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  background: rgba(255,255,255,0.2);
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.nav-card-content h5 {
  color: white;
  margin-bottom: 0.5rem;
  font-weight: 600;
  font-size: 1.1rem;
}

.nav-card-count {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.nav-card-content p {
  color: rgba(255,255,255,0.8);
  margin: 0;
  font-size: 0.9rem;
}

.nav-card-arrow {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  opacity: 0.6;
  transition: var(--transition-normal);
}

.nav-card:hover .nav-card-arrow {
  opacity: 1;
  transform: translateX(4px);
}

/* Analytics Cards */
.analytics-card {
  background: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(0,0,0,0.05);
  height: 100%;
  transition: var(--transition-normal);
  animation: fadeInUp 0.6s ease-out;
}

.analytics-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.analytics-card-header {
  padding: 1.5rem 1.5rem 0;
  display: flex;
  justify-content: between;
  align-items: flex-start;
}

.analytics-card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  font-size: 1.3rem;
  margin-right: auto;
}

.analytics-card-header.primary .analytics-card-icon {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
}

.analytics-card-header.success .analytics-card-icon {
  background: linear-gradient(135deg, var(--success), #28a745);
  color: white;
}

.analytics-card-header.warning .analytics-card-icon {
  background: linear-gradient(135deg, var(--warning), #fd7e14);
  color: white;
}

.analytics-card-header.info .analytics-card-icon {
  background: linear-gradient(135deg, var(--info), #6f42c1);
  color: white;
}

.analytics-card-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-weight: 600;
}

.analytics-card-trend.up {
  background: rgba(50, 212, 132, 0.1);
  color: var(--success);
}

.analytics-card-trend.down {
  background: rgba(231, 76, 60, 0.1);
  color: var(--danger);
}

.analytics-card-body {
  padding: 0 1.5rem 1.5rem;
}

.analytics-card-body h3 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0.5rem 0;
  color: var(--dark);
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.analytics-card-body p {
  color: var(--muted);
  margin: 0;
  font-weight: 500;
}

.analytics-card-progress {
  margin-top: 1rem;
}

.analytics-card-progress .progress {
  height: 6px;
  border-radius: 3px;
  background: rgba(0,0,0,0.05);
  overflow: hidden;
}

.analytics-card-progress .progress-bar {
  border-radius: 3px;
  transition: width 1s ease-in-out;
}

/* Chart Cards */
.chart-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(0,0,0,0.05);
  height: 100%;
  max-height: 450px;
  overflow: hidden;
  transition: var(--transition-normal);
  animation: fadeInUp 0.6s ease-out;
  display: flex;
  flex-direction: column;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.chart-card-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(0,0,0,0.05);
  display: flex;
  justify-content: between;
  align-items: flex-start;
  background: linear-gradient(135deg, #f8f9ff, #ffffff);
}

.chart-card-title h5 {
  margin: 0;
  color: var(--dark);
  font-weight: 600;
  font-size: 1.1rem;
}

.chart-card-title p {
  margin: 0.25rem 0 0;
  color: var(--muted);
  font-size: 0.9rem;
}

.chart-card-controls {
  margin-left: auto;
}

.chart-card-controls .btn-group-sm .btn {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

.chart-card-body {
  padding: 1.5rem;
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.chart-card-body canvas {
  max-width: 100%;
  max-height: 400px !important;
}

/* Specific chart container heights */
#volumeTrendChart {
  max-height: 300px !important;
}

#statusDistributionChart {
  max-height: 250px !important;
}

#equipmentUtilizationChart {
  max-height: 300px !important;
}

#processingTimeChart {
  max-height: 280px !important;
}

#areaStationChart {
  max-height: 200px !important;
}

/* Chart container wrapper */
.chart-card-body > div {
  position: relative;
  max-height: inherit;
}

/* Prevent canvas from growing beyond container */
canvas {
  max-width: 100% !important;
  height: auto !important;
}

/* Specific row height constraints */
.row .col-xl-8 .chart-card {
  max-height: 400px;
}

.row .col-xl-4 .chart-card {
  max-height: 400px;
}

.row .col-xl-6 .chart-card {
  max-height: 350px;
}

/* Status Legend */
.status-legend {
  margin-top: 1rem;
  display: grid;
  gap: 0.5rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-color.pending {
  background: var(--warning);
}

.legend-color.in-process {
  background: var(--info);
}

.legend-color.completed {
  background: var(--success);
}

.legend-color.cancelled {
  background: var(--danger);
}

/* Processing Stats */
.processing-stats {
  margin-top: 1rem;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(0,0,0,0.05);
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.8rem;
  color: var(--muted);
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 1.1rem;
  font-weight: 600;
}

/* Activity Card */
.activity-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(0,0,0,0.05);
  height: 100%;
  max-height: 500px;
  overflow: hidden;
  transition: var(--transition-normal);
  animation: slideInLeft 0.6s ease-out;
  display: flex;
  flex-direction: column;
}

.activity-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.activity-card-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(0,0,0,0.05);
  display: flex;
  justify-content: between;
  align-items: center;
  background: linear-gradient(135deg, #f8f9ff, #ffffff);
}

.activity-card-header h5 {
  margin: 0;
  color: var(--dark);
  font-weight: 600;
}

.activity-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: var(--success);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--muted);
}

.status-indicator.active {
  background: var(--success);
  animation: pulse 2s infinite;
}

.activity-card-body {
  padding: 1rem;
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(0,0,0,0.05);
  transition: var(--transition-fast);
  animation: fadeInUp 0.4s ease-out;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item:hover {
  background: rgba(0,0,0,0.02);
  border-radius: var(--radius-sm);
}

.activity-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.activity-icon.new-request {
  background: rgba(102, 126, 234, 0.1);
  color: var(--primary);
}

.activity-icon.completed {
  background: rgba(50, 212, 132, 0.1);
  color: var(--success);
}

.activity-icon.in-process {
  background: rgba(23, 162, 184, 0.1);
  color: var(--info);
  animation: spin 2s linear infinite;
}

.activity-content p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--dark);
}

.activity-content small {
  color: var(--muted);
  font-size: 0.8rem;
}

.activity-card-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(0,0,0,0.05);
  background: rgba(0,0,0,0.02);
}

/* Table Card */
.table-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(0,0,0,0.05);
  height: 100%;
  max-height: 500px;
  overflow: hidden;
  transition: var(--transition-normal);
  animation: fadeInUp 0.6s ease-out;
  display: flex;
  flex-direction: column;
}

.table-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.table-card-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(0,0,0,0.05);
  display: flex;
  justify-content: between;
  align-items: center;
  background: linear-gradient(135deg, #f8f9ff, #ffffff);
}

.table-card-header h5 {
  margin: 0;
  color: var(--dark);
  font-weight: 600;
}

.table-card-body {
  padding: 0;
  max-height: 400px;
  overflow-y: auto;
}

.recent-requests-table {
  margin: 0;
}

.recent-requests-table th {
  background: rgba(0,0,0,0.02);
  border: none;
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--muted);
  padding: 1rem;
}

.recent-requests-table td {
  padding: 1rem;
  border-top: 1px solid rgba(0,0,0,0.05);
  vertical-align: middle;
}

.recent-requests-table tr:hover {
  background: rgba(0,0,0,0.02);
}

.request-link {
  color: var(--primary);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition-fast);
}

.request-link:hover {
  color: var(--secondary);
  text-decoration: underline;
}

.user-info strong {
  display: block;
  color: var(--dark);
  font-size: 0.9rem;
}

.user-info small {
  color: var(--muted);
  font-size: 0.8rem;
}

.equipment-count,
.lot-count {
  font-weight: 600;
  color: var(--dark);
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: capitalize;
}

.status-badge.pending {
  background: rgba(246, 195, 67, 0.1);
  color: var(--warning);
  border: 1px solid rgba(246, 195, 67, 0.2);
}

.status-badge.in-process {
  background: rgba(23, 162, 184, 0.1);
  color: var(--info);
  border: 1px solid rgba(23, 162, 184, 0.2);
}

.status-badge.completed {
  background: rgba(50, 212, 132, 0.1);
  color: var(--success);
  border: 1px solid rgba(50, 212, 132, 0.2);
}

.status-badge.cancelled {
  background: rgba(231, 76, 60, 0.1);
  color: var(--danger);
  border: 1px solid rgba(231, 76, 60, 0.2);
}

.time-info small {
  color: var(--muted);
  font-size: 0.8rem;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.action-buttons .btn {
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius-sm);
}

/* Metrics Card */
.metrics-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(0,0,0,0.05);
  height: 100%;
  max-height: 450px;
  overflow: hidden;
  transition: var(--transition-normal);
  animation: fadeInUp 0.6s ease-out;
  display: flex;
  flex-direction: column;
}

.metrics-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.metrics-card-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(0,0,0,0.05);
  display: flex;
  justify-content: between;
  align-items: center;
  background: linear-gradient(135deg, #f8f9ff, #ffffff);
}

.metrics-card-header h5 {
  margin: 0;
  color: var(--dark);
  font-weight: 600;
}

.metrics-card-body {
  padding: 1.5rem;
}

/* Requestor List */
.requestor-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.requestor-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(0,0,0,0.02);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.requestor-item:hover {
  background: rgba(0,0,0,0.05);
  transform: translateX(4px);
}

.requestor-rank {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary);
  color: white;
  font-weight: 700;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.requestor-info {
  flex: 1;
}

.requestor-info strong {
  display: block;
  color: var(--dark);
  font-size: 0.9rem;
}

.requestor-info small {
  color: var(--muted);
  font-size: 0.8rem;
}

.requestor-stats {
  text-align: right;
}

.requestor-stats .count {
  display: block;
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary);
}

.requestor-stats small {
  color: var(--muted);
  font-size: 0.8rem;
}

/* Health Indicator */
.health-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
}

.health-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.health-indicator.good {
  color: var(--success);
}

.health-indicator.good .health-dot {
  background: var(--success);
}

.health-indicator.warning {
  color: var(--warning);
}

.health-indicator.warning .health-dot {
  background: var(--warning);
}

.health-indicator.danger {
  color: var(--danger);
}

.health-indicator.danger .health-dot {
  background: var(--danger);
}

/* Health Metrics */
.health-metrics {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.health-metric {
  display: flex;
  justify-content: between;
  align-items: center;
  gap: 1rem;
}

.metric-label {
  font-size: 0.9rem;
  color: var(--muted);
  font-weight: 500;
}

.metric-value {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.metric-value .value {
  font-weight: 700;
  font-size: 0.9rem;
  min-width: 60px;
  text-align: right;
}

.metric-bar {
  flex: 1;
  height: 6px;
  background: rgba(0,0,0,0.05);
  border-radius: 3px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 1s ease-in-out;
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--primary);
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .analytics-card-body h3 {
    font-size: 2rem;
  }
  
  .nav-card-count {
    font-size: 1.5rem;
  }
}

@media (max-width: 992px) {
  .chart-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .chart-card-controls {
    margin-left: 0;
  }
  
  .processing-stats {
    grid-template-columns: 1fr;
    text-align: left;
  }
}

@media (max-width: 768px) {
  .lot-analytics-dashboard {
    padding: 1rem;
  }
  
  .analytics-card-body h3 {
    font-size: 1.8rem;
  }
  
  .nav-card {
    padding: 1rem;
  }
  
  .nav-card-count {
    font-size: 1.3rem;
  }
  
  .chart-card-body,
  .activity-card-body,
  .metrics-card-body {
    padding: 1rem;
  }
  
  .activity-card-body {
    max-height: 200px;
  }
  
  .table-card-body {
    max-height: 300px;
  }
  
  .recent-requests-table th,
  .recent-requests-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 576px) {
  .analytics-card-header {
    padding: 1rem 1rem 0;
  }
  
  .analytics-card-body {
    padding: 0 1rem 1rem;
  }
  
  .analytics-card-body h3 {
    font-size: 1.5rem;
  }
  
  .nav-card-content h5 {
    font-size: 1rem;
  }
  
  .nav-card-count {
    font-size: 1.1rem;
  }
  
  .requestor-item {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  
  .requestor-stats {
    text-align: center;
  }
}

/* Dark Mode Styles */
[data-theme-mode="dark"] .analytics-card,
[data-theme-mode="dark"] .chart-card,
[data-theme-mode="dark"] .activity-card,
[data-theme-mode="dark"] .table-card,
[data-theme-mode="dark"] .metrics-card {
  background: var(--light);
  border-color: rgba(255,255,255,0.1);
}

[data-theme-mode="dark"] .chart-card-header,
[data-theme-mode="dark"] .activity-card-header,
[data-theme-mode="dark"] .table-card-header,
[data-theme-mode="dark"] .metrics-card-header {
  background: linear-gradient(135deg, var(--light), rgba(255,255,255,0.05));
  border-bottom-color: rgba(255,255,255,0.1);
}

[data-theme-mode="dark"] .recent-requests-table th {
  background: rgba(255,255,255,0.05);
}

[data-theme-mode="dark"] .recent-requests-table td {
  border-top-color: rgba(255,255,255,0.1);
}

[data-theme-mode="dark"] .recent-requests-table tr:hover {
  background: rgba(255,255,255,0.05);
}

[data-theme-mode="dark"] .requestor-item {
  background: rgba(255,255,255,0.05);
}

[data-theme-mode="dark"] .requestor-item:hover {
  background: rgba(255,255,255,0.1);
}

[data-theme-mode="dark"] .activity-item:hover {
  background: rgba(255,255,255,0.05);
}

/* Print Styles */
@media print {
  .lot-analytics-dashboard {
    background: white;
  }
  
  .nav-card,
  .analytics-card,
  .chart-card,
  .activity-card,
  .table-card,
  .metrics-card {
    box-shadow: none;
    border: 1px solid #ddd;
    break-inside: avoid;
  }
  
  .activity-card-body {
    max-height: none;
    overflow: visible;
  }
  
  .table-card-body {
    max-height: none;
    overflow: visible;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus States */
.nav-card-link:focus,
.request-link:focus,
.btn:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .nav-card,
  .analytics-card,
  .chart-card,
  .activity-card,
  .table-card,
  .metrics-card {
    border-width: 2px;
    border-color: currentColor;
  }
}