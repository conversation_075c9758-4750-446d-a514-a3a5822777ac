<?php

namespace App\Services;

use App\Models\LotRequest;
use App\Models\LotAssignment;
use App\Models\AvailableLotsCache;
use App\Models\Equipment;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class LotManagementService
{
    /**
     * Calculate priority score for a lot request
     */
    public function calculateRequestPriority(LotRequest $request): int
    {
        $score = 0;
        
        // Base FIFO score (age of request in hours)
        $hoursOld = $request->request_date->diffInHours(now());
        $score += min($hoursOld, 1000); // Cap at 1000
        
        // Urgent requests get priority boost
        if ($request->is_urgent) {
            $score += 500;
        }
        
        // Equipment count factor (more equipment = higher priority)
        $equipmentCount = $request->lotRequestItems()->count();
        $score += ($equipmentCount * 10);
        
        // Stagnant request boost (if request is very old)
        if ($hoursOld >= 72) {
            $score += 200; // Critical age boost
        } elseif ($hoursOld >= 48) {
            $score += 100; // High age boost
        } elseif ($hoursOld >= 24) {
            $score += 50; // Medium age boost
        }
        
        return $score;
    }
    
    /**
     * Get prioritized lot requests for management queue
     */
    public function getPrioritizedRequests(string $status = 'pending'): Collection
    {
        return LotRequest::where('status', $status)
            ->with(['user', 'lotRequestItems.equipment', 'priority'])
            ->get()
            ->map(function ($request) {
                // Calculate and update priority score
                $calculatedScore = $this->calculateRequestPriority($request);
                $request->update(['priority_score' => $calculatedScore]);
                
                return $request;
            })
            ->sortByDesc('is_urgent')
            ->sortByDesc('priority_score')
            ->sortBy('request_date') // FIFO within same priority
            ->values();
    }
    
    /**
     * Find available lots matching equipment requirements
     */
    public function findMatchingLots(LotRequest $request): Collection
    {
        $matchingLots = collect();
        
        foreach ($request->lotRequestItems as $item) {
            $equipment = $item->equipment;
            if (!$equipment) continue;
            
            $lots = AvailableLotsCache::where('is_available', true)
                ->where('available_quantity', '>', 0)
                ->get()
                ->filter(function ($lot) use ($equipment) {
                    return $this->checkEquipmentLotCompatibility($equipment, $lot);
                });
            
            $matchingLots = $matchingLots->merge($lots);
        }
        
        // Remove duplicates and sort by priority
        return $matchingLots->unique('lot_code')
            ->sortBy(function ($lot) {
                return $this->calculateLotPriority($lot);
            })
            ->reverse()
            ->values();
    }
    
    /**
     * Evaluate compatibility with support for alternative (less-priority) matches
     * Returns an array with statuses and score.
     *
     * Rules per matrix:
     * - Size: exact match only
     * - Type (6S/4S): 6S can accept 4S (alt); 4S requires 4S
     * - Class (COLOR/MONO): COLOR can accept MONO (alt); MONO requires MONO
     * - Work:
     *     NORMAL can accept WH REWORK (alt)
     *     PROCESS RW, OI REWORK, WH REWORK can accept NORMAL (alt)
     */
    private function evaluateCompatibility(Equipment $equipment, AvailableLotsCache $lot): array
    {
        $size = strtoupper((string) ($equipment->size ?? ''));
        $lotSize = strtoupper((string) ($lot->lot_size ?? ''));
        $insp = strtoupper((string) ($equipment->insp_type ?? ''));
        $lotType = strtoupper((string) ($lot->eqp_class ?? '')); // 6S/4S
        $cam = strtoupper((string) ($equipment->cam_class ?? ''));
        $lotClass = strtoupper((string) ($lot->eqp_type ?? '')); // COLOR/MONO
        $alloc = strtoupper((string) ($equipment->alloc_type ?? ''));
        $lotWork = strtoupper((string) ($lot->work_type ?? ''));
        
        // Size must be exact
        $sizeStatus = ($size !== '' && $lotSize !== '' && $size === $lotSize) ? 'exact' : 'none';
        
        // Type (6S/4S)
        $typeStatus = 'none';
        if ($insp !== '' && $lotType !== '') {
            if ($insp === $lotType) {
                $typeStatus = 'exact';
            } elseif ($insp === '6S' && $lotType === '4S') {
                $typeStatus = 'alt';
            }
        }
        
        // Class (COLOR/MONO)
        $classStatus = 'none';
        if ($cam !== '' && $lotClass !== '') {
            if ($cam === $lotClass) {
                $classStatus = 'exact';
            } elseif ($cam === 'COLOR' && $lotClass === 'MONO') {
                $classStatus = 'alt';
            }
        }
        
        // Work type
        $workStatus = 'none';
        if ($alloc !== '' && $lotWork !== '') {
            if ($alloc === $lotWork) {
                $workStatus = 'exact';
            } else {
                $altMap = [
                    'NORMAL' => ['WH REWORK'],
                    'PROCESS RW' => ['NORMAL'],
                    'OI REWORK' => ['NORMAL'],
                    'WH REWORK' => ['NORMAL'],
                ];
                $alts = $altMap[$alloc] ?? [];
                if (in_array($lotWork, $alts, true)) {
                    $workStatus = 'alt';
                }
            }
        }
        
        $assignable = $sizeStatus !== 'none' && $typeStatus !== 'none' && $classStatus !== 'none' && $workStatus !== 'none';
        
        // Score: size exact=4; type exact=3 alt=1; class exact=3 alt=1; work exact=2 alt=1
        $score = 0;
        if ($sizeStatus === 'exact') $score += 4;
        $score += ($typeStatus === 'exact') ? 3 : (($typeStatus === 'alt') ? 1 : 0);
        $score += ($classStatus === 'exact') ? 3 : (($classStatus === 'alt') ? 1 : 0);
        $score += ($workStatus === 'exact') ? 2 : (($workStatus === 'alt') ? 1 : 0);
        
        return [
            'size' => $sizeStatus,
            'type' => $typeStatus,
            'class' => $classStatus,
            'work' => $workStatus,
            'assignable' => $assignable,
            'score' => $score,
        ];
    }
    
    /**
     * Check if equipment and lot are compatible based on the assignment matrix
     */
    public function checkEquipmentLotCompatibility(Equipment $equipment, AvailableLotsCache $lot): bool
    {
        $eval = $this->evaluateCompatibility($equipment, $lot);
        return (bool) $eval['assignable'];
    }
    
    /**
     * Calculate lot priority score based on LIPAS and stagnant TAT
     */
    public function calculateLotPriority(AvailableLotsCache $lot): int
    {
        $score = 0;
        
        // LIPAS gets highest priority
        if ($lot->lipas_yn) {
            $score += 10000;
        }
        
        // Stagnant TAT score
        $tat = (float) ($lot->stagnant_tat ?? 0);
        if ($tat >= 3) {
            $score += 1000; // Critical (>= 3 days)
        } elseif ($tat >= 2) {
            $score += 500; // High (>= 2 days)
        } elseif ($tat >= 1) {
            $score += 100; // Medium (>= 1 day)
        }
        
        // Add raw TAT value for fine-grained sorting
        $score += $tat;
        
        return $score;
    }
    
    /**
     * Assign lots to a request following FIFO and priority rules
     */
    public function assignLotsToRequest(LotRequest $request, array $lotSelections): array
    {
        $assignments = [];
        $errors = [];
        
        DB::beginTransaction();
        
        try {
            foreach ($lotSelections as $lotCode => $selection) {
                $lot = AvailableLotsCache::where('lot_code', $lotCode)->first();
                
                if (!$lot) {
                    $errors[] = "Lot {$lotCode} not found";
                    continue;
                }
                
                $quantity = $selection['quantity'] ?? 1;
                $equipmentNumber = $selection['equipment_number'] ?? null;
                
                // Validate quantity availability
                if ($lot->available_quantity < $quantity) {
                    $errors[] = "Lot {$lotCode} only has {$lot->available_quantity} units available";
                    continue;
                }
                
                // Reserve the lot quantity
                $lot->reserveQuantity($quantity);
                
                // Create assignment record
                // Build equipment_code as requested: TYPE-CLASS-WORK (e.g., COLOR-6S-NORMAL)
                $codeParts = array_filter([
                    trim((string) $lot->eqp_type),
                    trim((string) $lot->eqp_class),
                    trim((string) $lot->work_type),
                ]);
                $derivedEquipmentCode = implode('-', $codeParts);
                if ($derivedEquipmentCode === '') {
                    // Fallback to equipment model code if derivation failed
                    $derivedEquipmentCode = optional($equipmentNumber ? Equipment::where('eqp_no', $equipmentNumber)->first() : null)->eqp_code ?? 'N/A';
                }

                $assignment = LotAssignment::create([
                    'lot_request_id' => $request->id,
                    'lot_id' => $lot->lot_code,
                    'lot_code' => $lot->lot_code,
                    'equipment_number' => $equipmentNumber,
                    'equipment_code' => $derivedEquipmentCode,
                    'lot_quantity' => $quantity,
                    'assigned_date' => now(),
                    'assigned_by' => auth()->user()->id,
                    'assigned_by_manager' => auth()->user()->emp_no,
                    'assignment_status' => 'pending',
                    'lot_size' => $lot->lot_size,
                    'eqp_type' => $lot->eqp_type,
                    'eqp_class' => $lot->eqp_class,
                    'work_type' => $lot->work_type,
                    'lipas_yn' => $lot->lipas_yn,
                    'stagnant_tat' => $lot->stagnant_tat,
                    'lot_location' => $lot->lot_location,
                ]);
                
                // Remove the assigned lot from availability cache to prevent reuse
                // If partial assignment logic ever exists, this can be adjusted to keep leftovers
                try {
                    $lot->delete();
                } catch (\Exception $e) {
                    // As a fallback, mark it unavailable
                    try {
                        $lot->update(['is_available' => false, 'available_quantity' => 0]);
                    } catch (\Exception $inner) {
                        // ignore; transaction will still commit assignment
                    }
                }
                
                $assignments[] = $assignment;
            }
            
            if (empty($errors) && !empty($assignments)) {
                // Update request status to assigned
                $request->markLotsAssigned();
                
                DB::commit();
                return ['success' => true, 'assignments' => $assignments];
            } else {
                DB::rollBack();
                return ['success' => false, 'errors' => $errors];
            }
            
        } catch (\Exception $e) {
            DB::rollBack();
            return ['success' => false, 'errors' => ['Database error: ' . $e->getMessage()]];
        }
    }
    
    /**
     * Auto-assign best matching lots to a request
     */
    public function autoAssignBestLots(LotRequest $request): array
    {
        $availableLots = $this->findMatchingLots($request);
        $totalNeeded = $request->total_quantity;
        $selections = [];
        
        // Get perfect matches first
        $perfectMatches = $availableLots->filter(function ($lot) use ($request) {
            foreach ($request->lotRequestItems as $item) {
                if ($item->equipment && $this->isPerferectMatch($item->equipment, $lot)) {
                    return true;
                }
            }
            return false;
        });
        
        // Take top priority perfect matches
        $assigned = 0;
        foreach ($perfectMatches as $lot) {
            if ($assigned >= $totalNeeded) break;
            
            $quantity = min($lot->available_quantity, $totalNeeded - $assigned);
            $equipmentNumber = $this->getBestMatchingEquipment($request, $lot);
            
            $selections[$lot->lot_code] = [
                'quantity' => $quantity,
                'equipment_number' => $equipmentNumber
            ];
            
            $assigned += $quantity;
        }
        
        // If still need more, take partial matches
        if ($assigned < $totalNeeded) {
            $partialMatches = $availableLots->filter(function ($lot) use ($perfectMatches) {
                return !$perfectMatches->contains('lot_code', $lot->lot_code);
            });
            
            foreach ($partialMatches as $lot) {
                if ($assigned >= $totalNeeded) break;
                
                $quantity = min($lot->available_quantity, $totalNeeded - $assigned);
                $equipmentNumber = $this->getBestMatchingEquipment($request, $lot);
                
                $selections[$lot->lot_code] = [
                    'quantity' => $quantity,
                    'equipment_number' => $equipmentNumber
                ];
                
                $assigned += $quantity;
            }
        }
        
        return $this->assignLotsToRequest($request, $selections);
    }
    
    /**
     * Check if lot is a perfect match for equipment
     */
    private function isPerferectMatch(Equipment $equipment, AvailableLotsCache $lot): bool
    {
        return ($equipment->size === $lot->lot_size) &&
               ($equipment->cam_class === $lot->eqp_type) &&
               ($equipment->insp_type === $lot->eqp_class) &&
               ($equipment->alloc_type === $lot->work_type);
    }
    
    /**
     * Get the best matching equipment for a lot
     */
    public function getBestMatchingEquipment(LotRequest $request, AvailableLotsCache $lot): ?string
    {
        $bestMatch = null;
        $bestScore = -1;
        
        foreach ($request->lotRequestItems as $item) {
            if (!$item->equipment) continue;
            
            $evaluation = $this->evaluateCompatibility($item->equipment, $lot);
            $score = $evaluation['score'];
            
            if ($evaluation['assignable'] && $score > $bestScore) {
                $bestScore = $score;
                $bestMatch = $item->equipment->eqp_no;
            }
        }
        
        return $bestMatch;
    }
    
    /**
     * Update lot availability cache from WIP data
     */
    public function updateLotsCache(): void
    {
        // This would typically sync with your WIP/updatewip database
        // For now, we'll create a placeholder that managers can populate
        
        DB::statement('TRUNCATE TABLE available_lots_cache');
        
        // This should be replaced with actual WIP database sync
        // Example structure for manual lot entry:
        /*
        AvailableLotsCache::create([
            'lot_code' => 'LOT123456',
            'lot_size' => '03',
            'eqp_type' => 'COLOR',
            'eqp_class' => '6S',
            'work_type' => 'NORMAL',
            'lipas_yn' => false,
            'stagnant_tat' => 24,
            'lot_location' => 'RACK-A-001',
            'available_quantity' => 1,
            'is_available' => true,
            'last_updated' => now()
        ]);
        */
    }
    
    /**
     * Get FIFO order for new requests
     */
    public function getNextFifoOrder(): int
    {
        return LotRequest::max('id') + 1;
    }
}