<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Prevent flash of light theme and sidebar state -->
    <script>
        // Set theme immediately from localStorage to prevent flash
        (function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme-mode', savedTheme);
            
            // Set sidebar state immediately to prevent flash (desktop only)
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed && window.innerWidth > 768) {
                document.documentElement.classList.add('sidebar-collapsed-immediate');
            }
        })();
    </script>

    <!-- Local Fonts -->
    <link href="{{ asset('fonts/inter.css') }}" rel="stylesheet">
    
    <!-- Local Icons -->
    <link rel="stylesheet" href="{{ asset('libs/fontawesome/css/all.min.css') }}">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <!-- Emergency restore functionality now integrated into app.js -->
    
    <style>
        /* Enhanced dropdown functionality */
        .dropdown {
            position: relative;
        }
        
        .dropdown-menu {
            display: none !important;
            position: absolute;
            top: 100%;
            right: 0;
            z-index: 1050;
            min-width: 200px;
            padding: 0.5rem 0;
            margin: 0.2rem 0 0;
            font-size: 0.875rem;
            color: var(--default-text-color);
            background-color: var(--custom-white);
            border: 1px solid var(--default-border);
            border-radius: 0.5rem;
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,.175);
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.2s ease;
            pointer-events: none;
        }
        
        .dropdown-menu.show {
            display: block !important;
            opacity: 1 !important;
            transform: translateY(0) !important;
            pointer-events: auto !important;
        }
        
        .dropdown-item {
            display: block;
            width: 100%;
            padding: 0.5rem 1rem;
            clear: both;
            font-weight: 400;
            color: var(--default-text-color);
            text-align: inherit;
            text-decoration: none;
            white-space: nowrap;
            background-color: transparent;
            border: 0;
            transition: all 0.15s ease;
        }
        
        .dropdown-item:hover,
        .dropdown-item:focus {
            color: var(--default-text-color);
            background-color: var(--default-background);
        }
        
        .dropdown-divider {
            height: 0;
            margin: 0.5rem 0;
            overflow: hidden;
            border-top: 1px solid var(--default-border);
            opacity: 1;
        }
        
        .dropdown-item.text-danger {
            color: var(--danger-color) !important;
        }
        
        .dropdown-item.text-danger:hover {
            background-color: rgba(239, 68, 68, 0.1);
        }
        
        /* User dropdown button styling */
        #userDropdown {
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid var(--default-border) !important;
            background: var(--custom-white) !important;
            color: var(--default-text-color) !important;
        }
        
        #userDropdown:hover {
            background: var(--default-background) !important;
            border-color: var(--primary-color) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
        }
        
        #userDropdown:focus,
        #userDropdown:active {
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25) !important;
            border-color: var(--primary-color) !important;
        }
        
        #userDropdown[aria-expanded="true"] {
            background: var(--default-background) !important;
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25) !important;
        }
        
        /* Header specific dropdown overrides */
        .header-actions .dropdown {
            position: relative !important;
        }
        
        .header-actions .dropdown-menu {
            position: absolute !important;
            top: calc(100% + 5px) !important;
            right: 0 !important;
            left: auto !important;
            transform-origin: top right !important;
        }
        
        /* Ensure proper layering and keep header fixed at top */
        .main-header {
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .header-actions {
            position: relative;
            z-index: 101;
        }
        
        .header-actions .dropdown-menu {
            z-index: 102 !important;
        }
        
        .dropdown-toggle::after {
            display: none !important;
            content: none !important;
            border: none !important;
            margin: 0 !important;
            width: 0 !important;
            height: 0 !important;
        }
        
        /* Sidebar nested navigation styles */
        .sidebar-menu .nav-item.has-dropdown {
            position: relative;
        }
        
        .sidebar-menu .nav-item.has-dropdown .nav-link {
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
        }
        
        /* Dropdown arrows removed - no longer needed */
        
        /* Sidebar dropdown menu - override general dropdown styles */
        .sidebar-menu .dropdown-menu {
            display: none !important;
            background: rgba(0, 0, 0, 0.2) !important;
            border-radius: 8px !important;
            border: 1px solid rgba(255, 255, 255, 0.08) !important;
            position: static !important;
            box-shadow: none !important;
            margin: 2px 12px 6px 12px !important;
            padding: 6px 0 !important;
            width: calc(100% - 24px) !important;
            opacity: 1 !important;
            transform: none !important;
            pointer-events: auto !important;
            top: auto !important;
            right: auto !important;
            left: auto !important;
            min-width: auto !important;
            z-index: auto !important;
        }
        
        .sidebar-menu .nav-item.has-dropdown.open .dropdown-menu {
            display: block !important;
        }
        
        .sidebar-menu .dropdown-item {
            display: flex !important;
            align-items: center !important;
            justify-content: flex-start !important;
            text-align: left !important;
            padding: 10px 16px 10px 40px !important;
            color: rgba(255, 255, 255, 0.85) !important;
            text-decoration: none !important;
            border: none !important;
            background: transparent !important;
            transition: all 0.3s ease !important;
            font-size: 0.9rem !important;
            font-weight: 450 !important;
            min-height: 40px !important;
            margin: 2px 8px !important;
            border-radius: 6px !important;
        }
        
        .sidebar-menu .dropdown-item:hover {
            background: rgba(255, 255, 255, 0.12) !important;
            color: #ffffff !important;
            padding-left: 45px !important;
            transform: translateX(3px);
        }
        
        .sidebar-menu .dropdown-item.active {
            background: rgba(255, 255, 255, 0.2) !important;
            color: #ffffff !important;
            font-weight: 500 !important;
            border-left: 2px solid #60a5fa !important;
        }
        
        .sidebar-menu .dropdown-item i {
            margin-right: 12px !important;
            width: 18px !important;
            text-align: center !important;
            font-size: 14px !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            flex-shrink: 0;
        }
        
        .sidebar-menu .dropdown-item span {
            display: flex !important;
            align-items: center !important;
            line-height: 1.3 !important;
        }
        
        /* Collapsed sidebar adjustments - Override app.css */
        .sidebar.collapsed {
            width: 70px !important;
        }
        
        .sidebar.collapsed .nav-link {
            justify-content: center !important;
            text-align: center !important;
            padding: 12px 8px !important;
            margin: 3px 8px !important;
            transform: none !important;
            min-height: 44px !important;
            display: flex !important;
            align-items: center !important;
        }
        
        .sidebar.collapsed .nav-link:hover {
            transform: none !important;
            background-color: rgba(255, 255, 255, 0.12) !important;
        }
        
        .sidebar.collapsed .nav-link i {
            margin-right: 0 !important;
            width: 20px !important;
            font-size: 16px !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
        }
        
        .sidebar.collapsed .nav-link span {
            display: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
            width: 0 !important;
        }
        
        .sidebar.collapsed .nav-item.has-dropdown .dropdown-menu {
            position: absolute;
            left: 70px;
            top: 0;
            background: #2c3e50;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            min-width: 220px;
            z-index: 1000;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: none;
        }
        
        .sidebar.collapsed .nav-item.has-dropdown:hover .dropdown-menu {
            display: block !important;
        }
        
        .sidebar.collapsed .dropdown-item {
            padding: 12px 20px;
            color: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
        }
        
        .sidebar.collapsed .dropdown-item:hover {
            background: rgba(255, 255, 255, 0.15);
            color: #ffffff;
            padding-left: 25px;
        }
        
        /* Fix for profile link in collapsed mode */
        .sidebar.collapsed .nav-link[data-title="Profile"] {
            justify-content: center !important;
            text-align: center !important;
        }
        
        /* Tooltip for collapsed sidebar */
        .sidebar.collapsed .nav-link {
            position: relative;
        }
        
        .sidebar.collapsed .nav-link:hover::after {
            content: attr(data-title);
            position: absolute;
            left: calc(100% + 10px);
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.875rem;
            white-space: nowrap;
            z-index: 1001;
            pointer-events: none;
            opacity: 0;
            animation: fadeInTooltip 0.3s ease 0.5s forwards;
        }
        
        @keyframes fadeInTooltip {
            from {
                opacity: 0;
                transform: translateY(-50%) translateX(-5px);
            }
            to {
                opacity: 1;
                transform: translateY(-50%) translateX(0);
            }
        }
        
        /* Override any gradient backgrounds */
        .sidebar {
            background: #2c3e50 !important;
            background-image: none !important;
        }
        
        [data-theme-mode="dark"] .sidebar {
            background: #1e293b !important;
            background-image: none !important;
        }
        
        /* Modern sidebar styling */
        .sidebar {
            border-right: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        /* Enhanced navigation link styling */
        .sidebar-menu .nav-link {
            color: rgba(255, 255, 255, 0.95) !important;
            font-weight: 500;
            letter-spacing: 0.02em;
            padding: 12px 16px !important;
            margin: 3px 12px !important;
            border-radius: 8px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: flex-start !important;
            text-align: left !important;
            transition: all 0.3s ease !important;
            min-height: 44px;
        }
        
        .sidebar-menu .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.12) !important;
            color: #ffffff !important;
            transform: translateX(2px);
        }
        
        .sidebar-menu .nav-link.active,
        .sidebar-menu .nav-item.has-dropdown.active > .nav-link {
            background-color: rgba(255, 255, 255, 0.18) !important;
            color: #ffffff !important;
            border-left: 3px solid #60a5fa;
        }
        
        .sidebar-menu .nav-link i {
            color: rgba(255, 255, 255, 0.9);
            margin-right: 14px !important;
            width: 20px !important;
            text-align: center !important;
            font-size: 16px !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            flex-shrink: 0;
        }
        
        .sidebar-menu .nav-link span {
            display: flex !important;
            align-items: center !important;
            line-height: 1.4 !important;
            font-size: 0.95rem !important;
        }
        
        .sidebar-menu .nav-link:hover i,
        .sidebar-menu .nav-link.active i {
            color: #ffffff;
        }
        
        /* Sidebar header improvements */
        .sidebar .sidebar-header {
            background: rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .sidebar-header .sidebar-title {
            color: #ffffff;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .sidebar .sidebar-header:hover {
            background: rgba(0, 0, 0, 0.15);
        }
        
        /* Hide all square Bootstrap dropdown indicators */
        .dropdown-toggle::after,
        .btn.dropdown-toggle::after,
        .nav-link.dropdown-toggle::after {
            display: none !important;
            content: none !important;
            border: none !important;
        }
        
        /* Hide dropdown indicators from sidebar */
        .sidebar .dropdown-toggle::after {
            display: none !important;
        }
        
        /* Hide dropdown indicators from header */
        .main-header .dropdown-toggle::after {
            display: none !important;
        }
        
        /* Fix header dropdown button */
        .header-actions .dropdown-toggle {
            border: none !important;
            background: transparent !important;
            box-shadow: none !important;
        }
        
        .header-actions .dropdown-toggle:focus,
        .header-actions .dropdown-toggle:active {
            border: none !important;
            background: transparent !important;
            box-shadow: none !important;
        }
        
        /* Clean user profile button */
        #userDropdown {
            border: 1px solid var(--default-border) !important;
            background: var(--custom-white) !important;
            border-radius: 8px !important;
            padding: 8px 12px !important;
            transition: all 0.3s ease;
        }
        
        #userDropdown:hover {
            background: var(--default-background) !important;
            border-color: var(--primary-color) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
        }
        
        .user-name {
            font-weight: 500;
            font-size: 0.9rem;
            color: var(--default-text-color);
        }
        
        .user-role {
            font-size: 0.75rem;
            color: var(--text-muted);
        }
        
        /* Hide missing icon placeholders */
        .nav-link i:empty,
        .dropdown-item i:empty {
            display: none !important;
        }
        
        /* Ensure FontAwesome 6 icons load properly */
        .sidebar-menu .nav-link i,
        .sidebar-menu .dropdown-item i,
        .main-header i {
            font-family: "Font Awesome 6 Free" !important;
            font-weight: 900 !important;
            display: inline-block !important;
            font-style: normal !important;
            font-variant: normal !important;
            text-rendering: auto !important;
            line-height: 1 !important;
            min-width: 16px;
            text-align: center;
        }
        
        /* Ensure brand icons use correct family */
        .sidebar-menu i[class^="fab "],
        .main-header i[class^="fab "] {
            font-family: "Font Awesome 6 Brands" !important;
            font-weight: 400 !important;
        }
        
        /* Force FontAwesome variables */
        .fa, .fas, .far, .fab, .fal {
            -moz-osx-font-smoothing: grayscale;
            -webkit-font-smoothing: antialiased;
            display: var(--fa-display, inline-block);
            font-style: normal;
            font-variant: normal;
            line-height: 1;
            text-rendering: auto;
        }
        
        /* Additional cleanup for dropdown indicators */
        .dropdown-toggle:after {
            display: none !important;
        }
        
        /* Hide Bootstrap's default caret/arrow */
        .caret,
        .dropup .caret {
            display: none !important;
        }
        
        /* Immediate collapsed state to prevent flash */
        html.sidebar-collapsed-immediate .sidebar {
            width: 70px !important;
            transition: none !important;
        }
        
        html.sidebar-collapsed-immediate .main-content {
            margin-left: 70px !important;
            transition: none !important;
        }
        
        html.sidebar-collapsed-immediate .sidebar .sidebar-logo-full {
            display: none !important;
        }
        
        html.sidebar-collapsed-immediate .sidebar .sidebar-logo-compact {
            display: flex !important;
        }
        
        html.sidebar-collapsed-immediate .sidebar .nav-link span {
            opacity: 0 !important;
            visibility: hidden !important;
            width: 0 !important;
        }
        
        html.sidebar-collapsed-immediate .sidebar .nav-link {
            justify-content: center !important;
            padding: 0.75rem !important;
            margin: 0.25rem 0.5rem !important;
        }
        
        html.sidebar-collapsed-immediate .sidebar .nav-link i {
            margin-right: 0 !important;
        }
        
        /* Mobile specific - prevent flash on small screens */
        @media (max-width: 768px) {
            html.sidebar-collapsed-immediate .sidebar {
                transform: translateX(-100%) !important;
                width: var(--sidebar-width) !important;
            }
            
            html.sidebar-collapsed-immediate .main-content {
                margin-left: 0 !important;
            }
        }
        
        /* Light mode specific header fixes */
        [data-theme-mode="light"] .theme-toggle {
            background: var(--custom-white) !important;
            border: 1px solid var(--default-border) !important;
            color: var(--default-text-color) !important;
        }
        
        [data-theme-mode="light"] .theme-toggle:hover {
            background: var(--default-background) !important;
            border-color: var(--primary-color) !important;
            color: var(--primary-color) !important;
        }
        
        [data-theme-mode="light"] .sidebar-toggle {
            background: var(--custom-white) !important;
            border: 1px solid var(--default-border) !important;
            color: var(--default-text-color) !important;
        }
        
        [data-theme-mode="light"] .sidebar-toggle:hover {
            background: var(--default-background) !important;
            border-color: var(--primary-color) !important;
            color: var(--primary-color) !important;
        }
        
        [data-theme-mode="light"] .main-header {
            background: var(--custom-white) !important;
            border-bottom: 1px solid var(--default-border) !important;
        }
        
        [data-theme-mode="light"] .header-title {
            color: var(--default-text-color) !important;
        }
    </style>
</head>
<body>
    <div class="dashboard-wrapper">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header" id="sidebarHeader" style="cursor: pointer;" title="Toggle sidebar">
                <div class="logo-container">
                    <!-- Full logo for expanded sidebar -->
                    <div class="sidebar-logo-full">
                        <img src="{{ asset('assets/images/brand-logos/pd.png') }}" alt="Process Dashboard" class="me-2" style="height: 24px; width: auto;" />
                        <span class="sidebar-title">Process Dashboard</span>
                    </div>
                    <!-- Compact logo for collapsed sidebar -->
                    <div class="sidebar-logo-compact">
                        <img src="{{ asset('assets/images/brand-logos/pd.png') }}" alt="Process Dashboard" style="height: 20px; width: auto;" />
                    </div>
                </div>
            </div>
            
            <nav class="sidebar-menu">
                <!-- Menu 1: Dashboard - All roles can access -->
                <div class="nav-item has-dropdown {{ request()->routeIs('dashboard') || request()->routeIs('endtime.*') ? 'active' : '' }}">
                    <a href="#" class="nav-link dropdown-toggle" data-title="Dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                    <div class="dropdown-menu">
                        <a href="{{ route('dashboard') }}" class="dropdown-item {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                            <i class="fas fa-home"></i>
                            <span>End Time Dashboard</span>
                        </a>
                        <a href="{{ route('endtime.index') }}" class="dropdown-item {{ request()->routeIs('endtime.*') ? 'active' : '' }}">
                            <i class="fas fa-clock"></i>
                            <span>End Time & Submitted</span>
                        </a>
                    </div>
                </div>
                
                <!-- Menu 2: Lot Request - All roles can access -->
                <div class="nav-item has-dropdown {{ request()->routeIs('requests.dashboard') || request()->routeIs('lot-requests.*') ? 'active' : '' }}">
                    <a href="#" class="nav-link dropdown-toggle" data-title="Lot Request">
                        <i class="fas fa-clipboard-list"></i>
                        <span>Lot Request</span>
                    </a>
                    <div class="dropdown-menu">
                        <a href="{{ route('requests.dashboard') }}" class="dropdown-item {{ request()->routeIs('requests.dashboard') ? 'active' : '' }}">
                            <i class="fas fa-chart-line"></i>
                            <span>Lot Request Dashboard</span>
                        </a>
                        <a href="{{ route('lot-requests.index') }}" class="dropdown-item {{ request()->routeIs('lot-requests.index') ? 'active' : '' }}">
                            <i class="fas fa-list"></i>
                            <span>Request List</span>
                        </a>
                        <a href="{{ route('lot-requests.create') }}" class="dropdown-item {{ request()->routeIs('lot-requests.create') ? 'active' : '' }}">
                            <i class="fas fa-plus-circle"></i>
                            <span>Create Request</span>
                        </a>
                    </div>
                </div>
                
                <!-- Menu 3: WIP Management - All roles can access -->
                <div class="nav-item has-dropdown {{ request()->routeIs('wip.dashboard') || request()->routeIs('updatewip.*') ? 'active' : '' }}">
                    <a href="#" class="nav-link dropdown-toggle" data-title="WIP Management">
                        <i class="fas fa-box"></i>
                        <span>WIP Management</span>
                    </a>
                    <div class="dropdown-menu">
                        <a href="{{ route('wip.dashboard') }}" class="dropdown-item {{ request()->routeIs('wip.dashboard') ? 'active' : '' }}">
                            <i class="fas fa-chart-area"></i>
                            <span>WIP Dashboard</span>
                        </a>
                        <a href="{{ route('updatewip.index') }}" class="dropdown-item {{ request()->routeIs('updatewip.index') ? 'active' : '' }}">
                            <i class="fas fa-cubes"></i>
                            <span>Process WIP</span>
                        </a>
                        {{-- Update WIP link - Users can create/update, Admins get full access --}}
                        @if(auth()->user()->isUser() || auth()->user()->isManager() || auth()->user()->isAdmin())
                        <a href="{{ route('updatewip.create') }}" class="dropdown-item {{ request()->routeIs('updatewip.create') ? 'active' : '' }}">
                            <i class="fas fa-upload"></i>
                            <span>Update WIP</span>
                        </a>
                        @endif
                    </div>
                </div>
                
                <!-- Menu 4: Machine Allocation - Manager and Admin only -->
                @if(auth()->user()->canAccessMCAllocation())
                <div class="nav-item has-dropdown {{ request()->routeIs('machine.allocation.dashboard') || request()->routeIs('equipment.*') ? 'active' : '' }}">
                    <a href="#" class="nav-link dropdown-toggle" data-title="Machine Allocation">
                        <i class="fas fa-cogs"></i>
                        <span>Machine Allocation</span>
                    </a>
                    <div class="dropdown-menu">
                        <a href="{{ route('machine.allocation.dashboard') }}" class="dropdown-item {{ request()->routeIs('machine.allocation.dashboard') ? 'active' : '' }}">
                            <i class="fas fa-chart-pie"></i>
                            <span>MC Allocation Dashboard</span>
                        </a>
                        <a href="{{ route('equipment.index') }}" class="dropdown-item {{ request()->routeIs('equipment.*') ? 'active' : '' }}">
                            <i class="fas fa-tools"></i>
                            <span>EQP Management</span>
                        </a>
                    </div>
                </div>
                @endif
                
                <!-- Menu 5: Endline WIP Management - All roles can access -->
                <div class="nav-item has-dropdown {{ request()->routeIs('endline.report.dashboard') || request()->routeIs('endline.wip.form') ? 'active' : '' }}">
                    <a href="#" class="nav-link dropdown-toggle" data-title="Endline WIP Management">
                        <i class="fas fa-industry"></i>
                        <span>Endline Management</span>
                    </a>
                    <div class="dropdown-menu">
                        <a href="{{ route('endline.report.dashboard') }}" class="dropdown-item {{ request()->routeIs('endline.report.dashboard') ? 'active' : '' }}">
                            <i class="fas fa-chart-bar"></i>
                            <span>Endline Dashboard</span>
                        </a>
                        <a href="{{ route('endline.wip.form') }}" class="dropdown-item {{ request()->routeIs('endline.wip.form') ? 'active' : '' }}">
                            <i class="fas fa-edit"></i>
                            <span>Endline WIP</span>
                        </a>
                    </div>
                </div>
                
                <!-- Menu 6: Settings - Admin only -->
                @if(auth()->user()->canAccessManagement())
                <div class="nav-item has-dropdown {{ request()->is('management/*') ? 'active' : '' }}">
                    <a href="#" class="nav-link dropdown-toggle" data-title="Settings">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                    <div class="dropdown-menu">
                        <a href="/management/settings" class="dropdown-item {{ request()->is('management/settings*') ? 'active' : '' }}">
                            <i class="fas fa-sliders-h"></i>
                            <span>System Settings</span>
                        </a>
                        <a href="/management/data" class="dropdown-item {{ request()->is('management/data*') ? 'active' : '' }}">
                            <i class="fas fa-database"></i>
                            <span>Data Management</span>
                        </a>
                        <a href="/management/users" class="dropdown-item {{ request()->is('management/users*') ? 'active' : '' }}">
                            <i class="fas fa-users-cog"></i>
                            <span>User Management</span>
                        </a>
                    </div>
                </div>
                @endif
                
                <!-- Menu 7: Profile - All roles can access -->
                <a href="{{ route('profile.edit') }}" class="nav-link {{ request()->routeIs('profile.*') ? 'active' : '' }}" data-title="Profile">
                    <i class="fas fa-user"></i>
                    <span>Profile</span>
                </a>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <header class="main-header">
                <div class="d-flex align-items-center">
                    <!-- Sidebar Toggle for Desktop -->
                    <button class="sidebar-toggle btn btn-outline-secondary me-3" id="sidebarToggleDesktop" title="Toggle sidebar">
                        <i class="fas fa-bars"></i>
                    </button>
                    
                    <!-- Mobile Sidebar Toggle -->
                    <button class="sidebar-toggle btn btn-outline-primary d-lg-none me-3" id="sidebarToggleMobile">
                        <i class="fas fa-bars"></i>
                    </button>
                    
                    @isset($header)
                        <h1 class="header-title">{{ $header }}</h1>
                    @else
                        <h1 class="header-title">Dashboard</h1>
                    @endisset
                </div>
                
                <div class="header-actions">
                    <!-- Dark Mode Toggle -->
                    <button class="theme-toggle" id="themeToggle" title="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    
                    <div class="dropdown">
                        <button class="btn btn-light d-flex align-items-center" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="user-avatar me-2">
                                @if(Auth::user()->avatar)
                                    <img src="{{ asset('storage/' . Auth::user()->avatar) }}" alt="{{ Auth::user()->emp_name }}" class="rounded-circle" width="32" height="32" style="object-fit: cover;">
                                @else
                                    <div class="rounded-circle d-flex align-items-center justify-content-center" style="width: 32px; height: 32px; background-color: #6366f1; color: white; font-weight: 600; font-size: 14px;">
                                        {{ strtoupper(substr(Auth::user()->emp_name, 0, 1)) }}
                                    </div>
                                @endif
                            </div>
                            <div class="user-info text-start">
                                <div class="user-name">{{ Str::limit(Auth::user()->emp_name, 20, '') }}</div>
                                <small class="user-role text-muted">{{ Auth::user()->role }}</small>
                            </div>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ route('profile.edit') }}">
                                    <i class="fas fa-user me-2"></i>
                                    Profile
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="fas fa-sign-out-alt me-2"></i>
                                        Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </header>
            
            <!-- Content Area -->
            <main class="content-area">
                {{ $slot }}
            </main>
        </div>
    </div>

    <script>
        // Enhanced layout JavaScript - interactive functionality now handled by app.js
        
</body>
</html>

