<x-app-layout>
    <x-slot name="header">
        ENDTIME | SUBMITTED
    </x-slot>

    <!-- Custom CSS for indicator badges -->
    <style>
        .indicator-badge {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .indicator-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(0,0,0,0.2);
        }
        
        .indicator-badge .badge {
            font-size: 0.85rem;
            font-weight: 600;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.15);
            border: 1px solid rgba(255,255,255,0.3);
            white-space: nowrap;
            padding: 6px 12px !important;
        }
        
        .indicator-badge .badge i {
            font-size: 0.8rem;
        }
        
        .indicator-badge .badge strong {
            margin-right: 4px;
            font-size: 0.8rem;
        }
        
        /* Equipment Count - Blue */
        .eqp-count-badge {
            background: linear-gradient(135deg, #007bff, #0056b3) !important;
        }
        
        /* Ongoing Lots - Purple */
        .ongoing-count-badge {
            background: linear-gradient(135deg, #6f42c1, #5a32a3) !important;
        }
        
        /* Total Lots - Green */
        .total-lots-badge {
            background: linear-gradient(135deg, #28a745, #1e7e34) !important;
        }
        
        /* Utilization - Orange */
        .utilization-badge {
            background: linear-gradient(135deg, #fd7e14, #e55a00) !important;
        }
        
        /* Total Quantity - Teal */
        .total-qty-badge {
            background: linear-gradient(135deg, #20c997, #17a085) !important;
        }
        
        /* W/O Ongoing - Red */
        .without-ongoing-badge {
            background: linear-gradient(135deg, #dc3545, #c82333) !important;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .without-ongoing-badge:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }
        
        /* Loading state */
        .indicator-loading {
            opacity: 0.7;
            pointer-events: none;
            animation: pulse 1.5s ease-in-out infinite alternate;
        }
        
        @keyframes pulse {
            from {
                opacity: 0.7;
            }
            to {
                opacity: 0.4;
            }
        }
        
        /* Responsive design */
        @media (max-width: 1200px) {
            .indicator-badge .badge strong {
                display: none;
            }
            
            .indicator-badge .badge {
                font-size: 0.7rem;
                padding: 4px 8px;
            }
        }
        
        @media (max-width: 768px) {
            .indicator-badge {
                display: none;
            }
        }
        
        /* WIP Info Styles */
        .wip-info {
            flex-wrap: wrap;
        }
        
        .last-updated-info .badge {
            font-size: 0.75rem;
            line-height: 1.3;
            white-space: nowrap;
            min-width: 140px;
        }
        
        .update-wip-button .btn {
            font-weight: 600;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }
        
        .update-wip-button .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(0,0,0,0.2);
        }
        
        @media (max-width: 1200px) {
            .wip-info {
                gap: 1rem !important;
            }
            
            .last-updated-info .badge {
                font-size: 0.7rem;
                padding: 4px 8px;
                min-width: 120px;
            }
            
            .update-wip-button .btn {
                font-size: 0.8rem;
                padding: 4px 8px;
            }
        }
        
        @media (max-width: 768px) {
            .wip-info {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem !important;
            }
        }
        
        /* Enhanced Search Field Styles */
        .table-search-wrapper {
            min-width: 280px;
        }
        
        .table-search-input {
            background: rgba(255, 255, 255, 0.95) !important;
            border: 2px solid #e3f2fd !important;
            border-radius: 25px !important;
            padding: 8px 40px 8px 35px !important;
            font-size: 0.9rem !important;
            color: #2c3e50 !important;
            font-weight: 500 !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            transition: all 0.3s ease !important;
        }
        
        .table-search-input::placeholder {
            color: #6c757d !important;
            font-weight: 400 !important;
        }
        
        .table-search-input:focus {
            background: rgba(255, 255, 255, 1) !important;
            border-color: #007bff !important;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2) !important;
            outline: none !important;
        }
        
        .table-search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 0.9rem;
            z-index: 5;
        }
        
        .table-search-clear {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: transparent !important;
            border: none !important;
            color: #6c757d !important;
            font-size: 0.8rem;
            padding: 2px 6px !important;
            border-radius: 50% !important;
            transition: all 0.2s ease !important;
            z-index: 5;
        }
        
        .table-search-clear:hover {
            background: rgba(220, 53, 69, 0.1) !important;
            color: #dc3545 !important;
        }
        
        /* Search field responsive */
        @media (max-width: 768px) {
            .table-search-wrapper {
                min-width: 200px;
            }
            
            .table-search-input {
                padding: 6px 30px 6px 30px !important;
                font-size: 0.8rem !important;
            }
        }
        
        /* Equipment modal sortable headers */
        .sortable-header {
            user-select: none;
            transition: all 0.2s ease;
            position: relative;
        }
        
        .sortable-header:hover {
            background-color: rgba(13, 110, 253, 0.1) !important;
            color: #0d6efd !important;
        }
        
        .sortable-header:active {
            background-color: rgba(13, 110, 253, 0.2) !important;
        }
    </style>



    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><strong>Validation Errors:</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Merged Filter & Action Controls -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="merged-controls-wrapper">
                <!-- Unified Filter Card -->
                <div class="card border-0 shadow-sm unified-filter-card">
                    <div class="card-header bg-gradient-primary text-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <h6 class="mb-0 me-3">
                                    <i class="fas fa-filter me-2"></i>
                                    <span id="filter-toggle" onclick="toggleFilters()" style="cursor: pointer;">Filters & Controls</span>
                                </h6>
                                <!-- Quick Time Range Display -->
                                <div class="time-range-quick-display">
                                    <span class="badge bg-light text-primary px-3 py-2" id="cutoff_range_badge">Loading...</span>
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-3">
                                <!-- WIP Info and Update Button -->
                                <div class="wip-info d-flex align-items-center gap-3">
                                    <!-- Last Updated Info -->
                                    <div class="last-updated-info">
                                        <span class="badge bg-light text-info px-3 py-2">
                                            <i class="fas fa-clock me-2"></i>
                                            <small><strong>Last Updated:</strong></small>
                                            <br>
                                            <small>
                                                @if($wipLastUpdated)
                                                    {{ $wipLastUpdated->format('M d, H:i') }}
                                                    <span class="text-muted">({{ $wipLastUpdated->diffForHumans() }})</span>
                                                @else
                                                    <span class="text-muted">Never</span>
                                                @endif
                                            </small>
                                        </span>
                                    </div>
                                    <!-- Update WIP Button -->
                                    <div class="update-wip-button">
                                        <a href="{{ route('updatewip.create') }}" class="btn btn-info btn-sm" title="Update WIP Data">
                                            <i class="fas fa-sync-alt me-1"></i>
                                            <span>Update WIP</span>
                                        </a>
                                    </div>
                                </div>


                                <!-- Action Buttons -->
                                <div class="action-buttons-compact d-flex gap-2">
                                    <a href="{{ route('dashboard') }}" class="btn btn-warning btn-sm nav-button action-btn-labeled" title="Dashboard">
                                        <i class="fas fa-home me-1"></i>
                                        <span>Dashboard</span>
                                    </a>
                                    <a href="{{ route('endtime.create') }}" class="btn btn-primary btn-sm action-btn-labeled" title="Create New Lot">
                                        <i class="fas fa-plus-circle me-1"></i>
                                        <span>ADD ENDTIME Lot</span>
                                    </a>
                                    <a href="{{ route('endtime.submit.show') }}" class="btn btn-success btn-sm action-btn-labeled" title="Submit Ongoing Lot">
                                        <i class="fas fa-check-circle me-1"></i>
                                        <span>SUBMIT Lot</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Collapsible Filter Controls -->
                    <div id="filter-controls" class="collapse show">
                        <div class="card-body bg-light py-3">
                            <form method="GET" action="{{ route('endtime.index') }}" id="endtime-filters-form">
                                <!-- Ultra Compact Single Row Filter -->
                                <div class="single-row-filter">
                                    <!-- Production Schedule Group -->
                                    <div class="filter-group schedule-group">
                                        <div class="group-badge schedule-badge">
                                            <i class="fas fa-clock me-2"></i>
                                            <span></span>
                                        </div>
                                        <div class="group-controls">
                                            <div class="mini-group">
                                                <label>Date</label>
                                                <input type="date" id="cutoff_date_picker" class="form-control form-control-sm mini-input"
                                                       value="{{ request('cutoff_date', date('Y-m-d')) }}"
                                                       onchange="updateCutoffFilter()">
                                            </div>
                                            <div class="mini-group">
                                                <label>Shift</label>
                                                <select id="shift_selector" class="form-select form-select-sm mini-input"
                                                        onchange="updateCutoffFilter()">
                                                    <option value="all" {{ request('cutoff_shift', 'day') === 'all' ? 'selected' : '' }}>All</option>
                                                    <option value="day" {{ request('cutoff_shift', 'day') === 'day' ? 'selected' : '' }}>Day</option>
                                                    <option value="night" {{ request('cutoff_shift', 'day') === 'night' ? 'selected' : '' }}>Night</option>
                                                </select>
                                            </div>
                                            <div class="mini-group">
                                                <label>Period</label>
                                                <select id="cutoff_period_selector" class="form-select form-select-sm mini-input"
                                                        onchange="updateCutoffFilter()">
                                                    <option value="1" {{ request('cutoff_period', '1') === '1' ? 'selected' : '' }}>1st</option>
                                                    <option value="2" {{ request('cutoff_period', '1') === '2' ? 'selected' : '' }}>2nd</option>
                                                    <option value="3" {{ request('cutoff_period', '1') === '3' ? 'selected' : '' }}>3rd</option>
                                                    <option value="all" {{ request('cutoff_period', '1') === 'all' ? 'selected' : '' }}>All</option>
                                                </select>
                                            </div>
                                            <div class="mini-group range-display">
                                                <label>Range</label>
                                                <div class="mini-range" id="shift_description">Day - 1st</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Divider -->
                                    <div class="filter-divider"></div>

                                    <!-- Equipment & Status Group -->
                                    <div class="filter-group equipment-group">
                                        <div class="group-badge equipment-badge">
                                            <i class="fas fa-cogs me-2"></i>
                                            <span></span>
                                        </div>
                                        <div class="group-controls">
                                            <div class="mini-group">
                                                <label>Line</label>
                                                <select name="eqp_line" id="eqp_line" class="form-select form-select-sm mini-input">
                                                    <option value="all">All</option>
                                                    @if(isset($filterOptions['equipment_lines']))
                                                        @foreach($filterOptions['equipment_lines'] as $line)
                                                            <option value="{{ $line }}" {{ (isset($filters['eqp_line']) && $filters['eqp_line'] === $line) ? 'selected' : '' }}>{{ $line }}</option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>
                                            <div class="mini-group">
                                                <label>Area</label>
                                                <select name="eqp_area" id="eqp_area" class="form-select form-select-sm mini-input">
                                                    <option value="all">All</option>
                                                    @if(isset($filterOptions['equipment_areas']))
                                                        @foreach($filterOptions['equipment_areas'] as $area)
                                                            <option value="{{ $area }}" {{ (isset($filters['eqp_area']) && $filters['eqp_area'] === $area) ? 'selected' : '' }}>{{ $area }}</option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>
                                            <div class="mini-group">
                                                <label>Work</label>
                                                <select name="work_type" id="work_type" class="form-select form-select-sm mini-input">
                                                    <option value="all">All</option>
                                                    @if(isset($filterOptions['work_types']))
                                                        @foreach($filterOptions['work_types'] as $type)
                                                            <option value="{{ $type }}" {{ (isset($filters['work_type']) && $filters['work_type'] === $type) ? 'selected' : '' }}>{{ $type }}</option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>
                                            <div class="mini-group">
                                                <label>Lot</label>
                                                <select name="lot_type" id="lot_type" class="form-select form-select-sm mini-input">
                                                    <option value="all">All</option>
                                                    @if(isset($filterOptions['lot_types']))
                                                        @foreach($filterOptions['lot_types'] as $type)
                                                            <option value="{{ $type }}" {{ (isset($filters['lot_type']) && $filters['lot_type'] === $type) ? 'selected' : '' }}>{{ $type }}</option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>
                                            <div class="mini-group">
                                                <label>Status</label>
                                                <select name="status" id="status" class="form-select form-select-sm mini-input">
                                                    <option value="all">All</option>
                                                    @if(isset($filterOptions['statuses']))
                                                        @foreach($filterOptions['statuses'] as $status)
                                                            <option value="{{ $status }}" {{ (isset($filters['status']) && $filters['status'] === $status) ? 'selected' : '' }}>{{ $status }}</option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Filter Action Buttons -->
                                    <div class="filter-actions-inline">
                                        <button type="button" class="btn btn-primary btn-sm filter-btn" onclick="applyCutoffFilter()" title="Apply Filters">
                                            <i class="fas fa-search me-1"></i>Apply
                                        </button>
                                        <button type="button" class="btn btn-outline-warning btn-sm filter-btn" onclick="resetAllFilters()" title="Reset All">
                                            <i class="fas fa-undo me-1"></i>Reset
                                        </button>
                                    </div>
                                </div>

                                <!-- Hidden fields for cutoff parameters -->
                                <input type="hidden" name="cutoff_date" id="cutoff_date" value="{{ request('cutoff_date', date('Y-m-d')) }}">
                                <input type="hidden" name="cutoff_shift" id="cutoff_shift" value="{{ request('cutoff_shift', 'day') }}">
                                <input type="hidden" name="cutoff_period" id="cutoff_period" value="{{ request('cutoff_period', '1') }}">
                                <input type="hidden" name="search" id="hidden_search" value="{{ $filters['search'] ?? '' }}">
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>




    <!-- Lots Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <h6 class="mb-0 me-3"><i class="fas fa-clock me-2"></i>Forecasted End Times</h6>
                    
                    <!-- Statistics Indicators -->
                    <div class="d-flex align-items-center gap-2 flex-wrap">
                        <!-- EQP Count -->
                        <div class="indicator-badge" title="Total Equipment Available">
                            <span class="badge px-4 py-2 eqp-count-badge">
                                <i class="fas fa-cogs me-1"></i>
                                <strong>EQP Count:</strong>
                                <span id="eqp-count-value">{{ $stats['ideal_equipment_count'] ?? 0 }}</span>
                            </span>
                        </div>
                        
                        <!-- With ONGOING -->
                        <div class="indicator-badge" title="Lots Currently in Progress">
                            <span class="badge px-4 py-2 ongoing-count-badge">
                                <i class="fas fa-play-circle me-1"></i>
                                <strong>With ONGOING:</strong>
                                <span id="ongoing-count-value">{{ $stats['ongoing_lots_count'] ?? 0 }}</span>
                            </span>
                        </div>
                        
                        <!-- Utilization Percentage -->
                        <div class="indicator-badge" title="Equipment Utilization Rate">
                            <span class="badge px-4 py-2 utilization-badge">
                                <i class="fas fa-chart-line me-1"></i>
                                <strong>Utilization:</strong>
                                <span id="utilization-percentage">{{ $stats['utilization_percentage'] ?? 0 }}%</span>
                            </span>
                        </div>
                        
                        <!-- Total Lot Counts -->
                        <div class="indicator-badge" title="Total Filtered Lots">
                            <span class="badge px-4 py-2 total-lots-badge">
                                <i class="fas fa-list-ol me-1"></i>
                                <strong>Total Lots:</strong>
                                <span id="total-lots-indicator">{{ $stats['total_filtered_lots'] ?? 0 }}</span>
                            </span>
                        </div>

                        <!-- Total Endtime Quantity -->
                        <div class="indicator-badge" title="Total Endtime Quantity (PCS)">
                            <span class="badge px-4 py-2 total-qty-badge">
                                <i class="fas fa-cubes me-1"></i>
                                <strong>Total Quantity:</strong>
                                <span id="total-qty-indicator">{{ number_format($stats['total_endtime_quantity'] ?? 0) }}</span>
                            </span>
                        </div>
                        
                        <!-- Equipment Without Ongoing -->
                        <div class="indicator-badge" title="Equipment Without Ongoing Lots (Click to view details)">
                            <span class="badge px-4 py-2 without-ongoing-badge" onclick="showEquipmentWithoutOngoingModal()">
                                <i class="fas fa-pause-circle me-1"></i>
                                <strong>W/O Ongoing:</strong>
                                <span id="without-ongoing-indicator">{{ $stats['equipment_without_ongoing'] ?? 0 }}</span>
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="table-search-wrapper">
                    <div class="position-relative">
                        <i class="fas fa-search table-search-icon"></i>
                        <input type="text" id="tableSearch" class="form-control form-control-sm table-search-input" 
                               placeholder="Search lots..." value="{{ $filters['search'] ?? '' }}" 
                               autocomplete="off">
                        <button type="button" id="clearSearch" class="btn btn-sm table-search-clear" 
                                style="display: {{ !empty($filters['search']) ? 'block' : 'none' }};" title="Clear search">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lots Table -->
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Lot ID</th>
                            <th>Model</th>
                            <th>Size/Qty</th>
                            <th>Work Type</th>
                            <th>Lot Type</th>
                            <th>Equipment</th>
                            <th>Line/Area</th>
                            <th>Est. End Time</th>
                            <th>Status</th>
                            <th>Result</th>
                            <th>LIPAS</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($endtimeLots as $lot)
                        <tr class="{{ $lot->est_endtime < now() ? 'table-danger' : ($lot->est_endtime < now()->addHours(2) ? 'table-warning' : '') }}">
                            <td>
                                <strong>{{ $lot->lot_id }}</strong>
                            </td>
                            <td>{{ $lot->model_15 }}</td>
                            <td>
                                <span class="badge bg-secondary">{{ $lot->lot_size }}</span>
                                <br><small class="text-muted">Qty: {{ number_format($lot->lot_qty) }}</small>
                            </td>
                            <td>
                                <span class="badge bg-{{ $lot->work_type === 'PROD' ? 'primary' : 'secondary' }}">
                                    {{ $lot->work_type }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{{ $lot->lot_type === 'HOT' ? 'danger' : ($lot->lot_type === 'SUPER_HOT' ? 'warning' : 'info') }}">
                                    {{ $lot->lot_type }}
                                </span>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ $lot->eqp_1 }}
                                    @if($lot->eqp_2), {{ $lot->eqp_2 }}@endif
                                    @if($lot->eqp_3), {{ $lot->eqp_3 }}@endif
                                    @if($lot->eqp_4), {{ $lot->eqp_4 }}@endif
                                    @if($lot->eqp_5), {{ $lot->eqp_5 }}@endif
                                    @if($lot->eqp_6), {{ $lot->eqp_6 }}@endif
                                    @if($lot->eqp_7), {{ $lot->eqp_7 }}@endif
                                    @if($lot->eqp_8), {{ $lot->eqp_8 }}@endif
                                    @if($lot->eqp_9), {{ $lot->eqp_9 }}@endif
                                    @if($lot->eqp_10), {{ $lot->eqp_10 }}@endif
                                </small>
                            </td>
                            <td>
                                <strong>{{ $lot->eqp_line }}</strong>
                                <br><small class="text-muted">{{ $lot->eqp_area }}</small>
                            </td>
<td>
                                <div class="text-{{ $lot->est_endtime < now() ? 'danger' : ($lot->est_endtime < now()->addHours(2) ? 'warning' : 'success') }}">
                                    <strong>{{ $lot->est_endtime->format('M d, H:i') }}</strong>
                                    <br>
                                    <small>
                                        @if($lot->getProcessingDurationFormatted())
                                            {{ $lot->getProcessingDurationFormatted() }}
                                        @else
                                            Duration N/A
                                        @endif
                                    </small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{
                                    $lot->status === 'Submitted' ? 'success' :
                                    ($lot->status === 'Ongoing' ? 'primary' :
                                    ($lot->status === 'COMPLETED' ? 'info' :
                                    ($lot->status === 'MAINTENANCE' ? 'danger' : 'secondary')))
                                }}">
                                    {{ $lot->status }}
                                </span>
                                @if($lot->status === 'Submitted' && $lot->actual_submitted_at)
                                    <br><small class="text-muted">Submitted: {{ $lot->actual_submitted_at->format('M d, H:i') }}</small>
                                @endif
                            </td>
                            <td>
                                @if($lot->remarks)
                                    <span class="badge bg-{{
                                        $lot->remarks === 'Early' ? 'success' :
                                        ($lot->remarks === 'Delayed' ? 'danger' : 'primary')
                                    }}">
                                        {{ $lot->remarks }}
                                    </span>
                                    @if($lot->submission_notes)
                                        <br><small class="text-muted">{{ Str::limit($lot->submission_notes, 50) }}</small>
                                    @else
                                        <br><small class="text-muted">No notes provided</small>
                                    @endif
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-{{ $lot->lipas_yn === 'Y' ? 'success' : 'secondary' }}">
                                    {{ $lot->lipas_yn === 'Y' ? 'Yes' : 'No' }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <!-- View Details - Available to all authenticated users -->
                                    <button type="button" class="btn btn-sm btn-outline-info"
                                            onclick="viewLot({{ $lot->id }})"
                                            title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>

                                    <!-- Edit/Modify - Available to all authenticated users (USER, MANAGER, ADMIN) -->
                                    <!-- Only allow editing if status is 'Ongoing' -->
                                    @if(Auth::user()->isUser() || Auth::user()->isManager() || Auth::user()->isAdmin())
                                        @if($lot->status === 'Ongoing')
                                            <button type="button" class="btn btn-sm btn-outline-warning"
                                                    onclick="editLot({{ $lot->id }})"
                                                    title="Edit/Modify">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        @else
                                            <button type="button" class="btn btn-sm btn-outline-secondary" disabled
                                                    title="Cannot edit - Lot status is {{ $lot->status }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        @endif
                                    @endif

                                    <!-- Delete - Available to Managers and Admins only -->
                                    @if(Auth::user()->isManager() || Auth::user()->isAdmin())
                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                            onclick="deleteLot({{ $lot->id }}, '{{ $lot->lot_id }}')"
                                            title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="12" class="text-center py-4">
                                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">No forecasted end times found for the selected criteria</h6>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        @if($endtimeLots->hasPages())
        <div class="card-footer bg-light">
            {{ $endtimeLots->links() }}
        </div>
        @endif
    </div>


    <!-- JavaScript Integration -->
    <script>
        // Inject available equipment data for JavaScript consumption
        window.availableEquipment = @json($availableEquipment ?? []);


        // Add global calculation function for manual testing
        window.triggerCalculation = function() {
            if (typeof calculateEndtime === 'function') {
                calculateEndtime();
            }
        };
    </script>

    <!-- Include Endtime JavaScript -->
    <script src="{{ asset('js/endtime.js') }}"></script>

    <!-- Additional JavaScript for specific functions not in main file -->
    <script>
        // Functions that need to be global for inline handlers

        /**
         * View lot details in modal
         */
        function viewLot(lotId) {
            
            // Show modal with loading state
            const modal = new bootstrap.Modal(document.getElementById('lotDetailsModal'));
            const modalContent = document.getElementById('lotDetailsContent');
            const editBtn = document.getElementById('editFromModalBtn');
            
            // Reset modal content to loading state
            modalContent.innerHTML = `
                <div class="d-flex justify-content-center align-items-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span class="ms-3">Loading lot details...</span>
                </div>
            `;
            
            editBtn.style.display = 'none';
            modal.show();
            
            // Fetch lot details
            fetch(`/api/endtime/lot/${lotId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayLotDetails(data.lot);
                    // Show edit button only if status allows editing and set up click handler
                    if (data.lot.status === 'Ongoing') {
                        editBtn.style.display = 'inline-block';
                        editBtn.onclick = () => editLot(lotId);
                        editBtn.disabled = false;
                        editBtn.title = 'Edit/Modify';
                    } else {
                        editBtn.style.display = 'inline-block';
                        editBtn.onclick = null;
                        editBtn.disabled = true;
                        editBtn.title = `Cannot edit - Lot status is ${data.lot.status}`;
                        editBtn.classList.remove('btn-warning');
                        editBtn.classList.add('btn-secondary');
                    }
                } else {
                    modalContent.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            ${data.message || 'Failed to load lot details'}
                        </div>
                    `;
                }
            })
            .catch(error => {
                modalContent.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Error loading lot details. Please try again.
                    </div>
                `;
            });
        }
        
        /**
         * Display lot details in modal
         */
        function displayLotDetails(lot) {
            const modalContent = document.getElementById('lotDetailsContent');
            const equipmentList = [
                lot.eqp_1, lot.eqp_2, lot.eqp_3, lot.eqp_4, lot.eqp_5,
                lot.eqp_6, lot.eqp_7, lot.eqp_8, lot.eqp_9, lot.eqp_10
            ].filter(eqp => eqp && eqp.trim() !== '').join(', ');
            
            const statusClass = lot.status === 'Submitted' ? 'success' :
                               (lot.status === 'Ongoing' ? 'primary' :
                               (lot.status === 'COMPLETED' ? 'info' : 'secondary'));
            
            const resultClass = lot.remarks === 'Early' ? 'success' :
                               (lot.remarks === 'Delayed' ? 'danger' : 'primary');
            
            modalContent.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-0 bg-light">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Basic Information</h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-2">
                                    <div class="col-4"><strong>Lot ID:</strong></div>
                                    <div class="col-8">${lot.lot_id}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>Model:</strong></div>
                                    <div class="col-8">${lot.model_15 || '-'}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>Size:</strong></div>
                                    <div class="col-8"><span class="badge bg-secondary">${lot.lot_size || '-'}</span></div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>Quantity:</strong></div>
                                    <div class="col-8">${lot.lot_qty ? parseInt(lot.lot_qty).toLocaleString() + ' PCS' : '-'}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>Work Type:</strong></div>
                                    <div class="col-8"><span class="badge bg-${lot.work_type === 'PROD' ? 'primary' : 'secondary'}">${lot.work_type}</span></div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>Lot Type:</strong></div>
                                    <div class="col-8"><span class="badge bg-info">${lot.lot_type}</span></div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>LIPAS:</strong></div>
                                    <div class="col-8"><span class="badge bg-${lot.lipas_yn === 'Y' ? 'success' : 'secondary'}">${lot.lipas_yn === 'Y' ? 'Yes' : 'No'}</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-0 bg-light">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Equipment & Status</h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-2">
                                    <div class="col-4"><strong>Equipment:</strong></div>
                                    <div class="col-8"><small>${equipmentList || 'No equipment assigned'}</small></div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>Line/Area:</strong></div>
                                    <div class="col-8">${lot.eqp_line || '-'} / ${lot.eqp_area || '-'}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>Est. End Time:</strong></div>
                                    <div class="col-8">
                                        <strong>${lot.est_endtime_formatted || '-'}</strong>
                                        ${lot.est_endtime_relative ? '<br><small class="text-muted">' + lot.est_endtime_relative + '</small>' : ''}
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>Status:</strong></div>
                                    <div class="col-8">
                                        <span class="badge bg-${statusClass}">${lot.status}</span>
                                        ${lot.actual_submitted_at ? '<br><small class="text-muted">Submitted: ' + lot.actual_submitted_at_formatted + '</small>' : ''}
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>Result:</strong></div>
                                    <div class="col-8">
                                        ${lot.remarks ? '<span class="badge bg-' + resultClass + '">' + lot.remarks + '</span>' : '<span class="text-muted">-</span>'}
                                        ${lot.remarks && lot.submission_notes ? '<br><small class="text-muted">' + lot.submission_notes + '</small>' : (lot.remarks ? '<br><small class="text-muted">No notes provided</small>' : '')}
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-4"><strong>Created:</strong></div>
                                    <div class="col-8"><small class="text-muted">${lot.created_at_formatted || '-'}</small></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        /**
         * Edit lot - fetch data and redirect to create page
         */
        function editLot(lotId) {
            
            // Show loading indicator (optional - you can remove this if too much)
            const loadingToast = document.createElement('div');
            loadingToast.className = 'toast align-items-center text-white bg-primary border-0';
            loadingToast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-spinner fa-spin me-2"></i>Loading lot data for editing...
                    </div>
                </div>
            `;
            
            // Fetch lot details first to store in sessionStorage
            fetch(`/api/endtime/lot/${lotId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Check if lot status allows editing
                    if (data.lot.status !== 'Ongoing') {
                        showToast(`Cannot edit lot ${data.lot.lot_id} - Only lots with 'Ongoing' status can be edited. Current status: ${data.lot.status}`, 'error');
                        return;
                    }
                    
                    // Store lot data in sessionStorage for the create page to use
                    sessionStorage.setItem('editLotData', JSON.stringify(data.lot));
                    
                    // Redirect to create page with edit parameter
                    window.location.href = `/endtime/create?edit=${lotId}`;
                } else {
                    showToast(data.message || 'Failed to load lot data for editing', 'error');
                }
            })
            .catch(error => {
                showToast('Error loading lot data. Please try again.', 'error');
            });
        }

        /**
         * Delete lot
         */
        function deleteLot(lotId, lotIdString) {
            if (confirm(`Are you sure you want to delete lot ${lotIdString}?`)) {
                // Make AJAX call to delete endpoint
                fetch(`/endtime/${lotId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(data.message, 'success');
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        showToast(data.message || 'Failed to delete lot', 'error');
                    }
                })
                .catch(error => {
                    showToast('Failed to delete lot', 'error');
                });
            }
        }

        /**
         * Filter and utility functions from existing inline scripts
         */

        // Initialize existing filter functions if they don't exist
        if (typeof toggleFilters === 'undefined') {
            function toggleFilters() {
                const filterControls = document.getElementById('filter-controls');
                if (filterControls) {
                    if (filterControls.classList.contains('show')) {
                        filterControls.classList.remove('show');
                    } else {
                        filterControls.classList.add('show');
                    }
                }
            }
        }

        if (typeof updateCutoffFilter === 'undefined') {
            function updateCutoffFilter() {
                // Update cutoff filter display
                const date = document.getElementById('cutoff_date_picker')?.value;
                const shift = document.getElementById('shift_selector')?.value;
                const period = document.getElementById('cutoff_period_selector')?.value;

                // Update hidden fields
                if (document.getElementById('cutoff_date')) document.getElementById('cutoff_date').value = date;
                if (document.getElementById('cutoff_shift')) document.getElementById('cutoff_shift').value = shift;
                if (document.getElementById('cutoff_period')) document.getElementById('cutoff_period').value = period;

                // Update range display
                updateShiftDescription();
            }
        }

        if (typeof updateShiftDescription === 'undefined') {
            function updateShiftDescription() {
                const shift = document.getElementById('shift_selector')?.value || 'day';
                const period = document.getElementById('cutoff_period_selector')?.value || '1';
                const shiftDesc = document.getElementById('shift_description');

                if (shiftDesc) {
                    let description = '';
                    if (shift === 'all') {
                        description = 'All Shifts';
                    } else if (shift === 'day') {
                        switch(period) {
                            case '1': description = 'Day - 1st (7AM-12PM)'; break;
                            case '2': description = 'Day - 2nd (12PM-4PM)'; break;
                            case '3': description = 'Day - 3rd (4PM-7PM)'; break;
                            case 'all': description = 'Day - All (7AM-7PM)'; break;
                        }
                    } else if (shift === 'night') {
                        switch(period) {
                            case '1': description = 'Night - 1st (7PM-12AM)'; break;
                            case '2': description = 'Night - 2nd (12AM-4AM)'; break;
                            case '3': description = 'Night - 3rd (4AM-7AM)'; break;
                            case 'all': description = 'Night - All (7PM-7AM)'; break;
                        }
                    }
                    shiftDesc.textContent = description;
                }
            }
        }

        if (typeof applyCutoffFilter === 'undefined') {
            function applyCutoffFilter() {
                updateCutoffFilter();
                
                // Update indicators before submitting form
                updateIndicators();
                
                document.getElementById('endtime-filters-form')?.submit();
            }
        }

        if (typeof resetAllFilters === 'undefined') {
            function resetAllFilters() {
                // Reset all filter inputs to defaults
                const form = document.getElementById('endtime-filters-form');
                if (form) {
                    form.reset();
                    
                    // Calculate current shift and period based on Manila time
                    const now = new Date();
                    const manilaTime = new Date(now.toLocaleString("en-US", { timeZone: "Asia/Manila" }));
                    const hours = manilaTime.getHours();
                    
                    let shift, period, cutoffDate;
                    
                    if (hours >= 7 && hours <= 18) {
                        // Day Shift: 07:00 - 18:59
                        shift = 'day';
                        cutoffDate = manilaTime; // Same day
                        
                        if (hours >= 7 && hours < 12) {
                            period = '1'; // 07:00 - 11:59
                        } else if (hours >= 12 && hours < 16) {
                            period = '2'; // 12:00 - 15:59
                        } else {
                            period = '3'; // 16:00 - 18:59
                        }
                    } else {
                        // Night Shift: 19:00 - 06:59
                        shift = 'night';
                        
                        if (hours >= 19) {
                            // 19:00 - 23:59 (same day)
                            period = '1';
                            cutoffDate = manilaTime; // Same day
                        } else if (hours >= 0 && hours < 4) {
                            // 00:00 - 03:59 (next day, but shift started previous day)
                            period = '2';
                            cutoffDate = new Date(manilaTime);
                            cutoffDate.setDate(cutoffDate.getDate() - 1); // Previous day
                        } else {
                            // 04:00 - 06:59 (next day, but shift started previous day)
                            period = '3';
                            cutoffDate = new Date(manilaTime);
                            cutoffDate.setDate(cutoffDate.getDate() - 1); // Previous day
                        }
                    }
                    
                    // Format date for input
                    const dateString = cutoffDate.getFullYear() + '-' +
                        String(cutoffDate.getMonth() + 1).padStart(2, '0') + '-' +
                        String(cutoffDate.getDate()).padStart(2, '0');
                    
                    // Set calculated current values
                    if (document.getElementById('cutoff_date_picker')) document.getElementById('cutoff_date_picker').value = dateString;
                    if (document.getElementById('shift_selector')) document.getElementById('shift_selector').value = shift;
                    if (document.getElementById('cutoff_period_selector')) document.getElementById('cutoff_period_selector').value = period;

                    updateCutoffFilter();
                    
                    // Update indicators before submitting
                    updateIndicators();
                    
                    form.submit();
                }
            }
        }

        // Initialize page when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Set current cutoff as default
            setCurrentCutoffDefaults();

            // Initialize shift description
            updateShiftDescription();

            // Update cutoff range badge
            updateCutoffRangeBadge();
            
            // Initialize table search
            initializeTableSearch();
            
            // Initialize filter change listeners for indicators
            initializeFilterIndicators();
            
            // Global fix for Bootstrap modal aria-hidden warnings
            document.addEventListener('hide.bs.modal', function(e) {
                // Remove focus from any element inside the modal that's about to be hidden
                const modal = e.target;
                if (document.activeElement && modal.contains(document.activeElement)) {
                    document.activeElement.blur();
                }
                // Also remove focus from modal itself
                if (document.activeElement === modal) {
                    modal.blur();
                }
                // As a final fallback, move focus to body to prevent any lingering focus issues
                setTimeout(() => {
                    if (document.activeElement && modal.contains(document.activeElement)) {
                        document.body.focus();
                    }
                }, 0);
            });
        });
        
        /**
         * Initialize filter change listeners for dynamic indicator updates
         */
        function initializeFilterIndicators() {
            // The indicators will be updated when filters are applied via applyCutoffFilter()
            // or when the form is reset via resetAllFilters()
            // This provides better performance and user experience
        }
        
        /**
         * Update indicators via AJAX based on current filters
         */
        function updateIndicators() {
            // Show loading state
            const indicators = document.querySelectorAll('.indicator-badge');
            const loadingIndicator = document.getElementById('indicators-loading');
            
            indicators.forEach(indicator => {
                indicator.classList.add('indicator-loading');
            });
            
            if (loadingIndicator) {
                loadingIndicator.classList.remove('d-none');
            }
            
            // Gather current filter values
            const filters = {
                status: document.getElementById('status')?.value || 'all',
                eqp_line: document.getElementById('eqp_line')?.value || 'all',
                eqp_area: document.getElementById('eqp_area')?.value || 'all',
                work_type: document.getElementById('work_type')?.value || 'all',
                lot_type: document.getElementById('lot_type')?.value || 'all',
                cutoff_date: document.getElementById('cutoff_date')?.value || '',
                cutoff_shift: document.getElementById('cutoff_shift')?.value || 'day',
                cutoff_period: document.getElementById('cutoff_period')?.value || '1',
                search: document.getElementById('tableSearch')?.value || ''
            };
            
            // Convert filters to URL parameters
            const queryParams = new URLSearchParams(filters);
            const url = `{{ route("endtime.index") }}?${queryParams.toString()}`;
            
            // Make AJAX request to get updated statistics
            fetch(url, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.stats) {
                    updateIndicatorValues(data.stats);
                }
            })
            .catch(error => {
                console.error('Error updating indicators:', error);
                showToast('Failed to update indicators', 'error');
            })
            .finally(() => {
                // Remove loading state
                indicators.forEach(indicator => {
                    indicator.classList.remove('indicator-loading');
                });
                
                if (loadingIndicator) {
                    loadingIndicator.classList.add('d-none');
                }
            });
        }
        
        /**
         * Update indicator values with new data
         */
        function updateIndicatorValues(stats) {
            // Update equipment count
            const eqpCountElement = document.getElementById('eqp-count-value');
            if (eqpCountElement && stats.ideal_equipment_count !== undefined) {
                animateCounterUpdate(eqpCountElement, parseInt(eqpCountElement.textContent) || 0, stats.ideal_equipment_count);
            }
            
            // Update ongoing lots count
            const ongoingCountElement = document.getElementById('ongoing-count-value');
            if (ongoingCountElement && stats.ongoing_lots_count !== undefined) {
                animateCounterUpdate(ongoingCountElement, parseInt(ongoingCountElement.textContent) || 0, stats.ongoing_lots_count);
            }
            
            // Update total lots count
            const totalLotsElement = document.getElementById('total-lots-indicator');
            if (totalLotsElement && stats.total_filtered_lots !== undefined) {
                animateCounterUpdate(totalLotsElement, parseInt(totalLotsElement.textContent) || 0, stats.total_filtered_lots);
            }
            
            // Update utilization percentage
            const utilizationElement = document.getElementById('utilization-percentage');
            if (utilizationElement && stats.utilization_percentage !== undefined) {
                const currentValue = parseFloat(utilizationElement.textContent.replace('%', '')) || 0;
                animateCounterUpdate(utilizationElement, currentValue, stats.utilization_percentage, true);
            }
            
            // Update total quantity indicator
            const totalQtyElement = document.getElementById('total-qty-indicator');
            if (totalQtyElement && stats.total_endtime_quantity !== undefined) {
                // Format the quantity number with commas
                const currentValue = parseInt(totalQtyElement.textContent.replace(/,/g, '')) || 0;
                animateCounterUpdate(totalQtyElement, currentValue, stats.total_endtime_quantity, false, true);
            }
            
            // Update equipment without ongoing indicator
            const withoutOngoingElement = document.getElementById('without-ongoing-indicator');
            if (withoutOngoingElement && stats.equipment_without_ongoing !== undefined) {
                const currentValue = parseInt(withoutOngoingElement.textContent) || 0;
                animateCounterUpdate(withoutOngoingElement, currentValue, stats.equipment_without_ongoing);
            }
        }
        
        /**
         * Animate counter updates for better UX
         */
        function animateCounterUpdate(element, fromValue, toValue, isPercentage = false, formatWithCommas = false) {
            const duration = 500; // Animation duration in ms
            const steps = 20; // Number of animation steps
            const stepValue = (toValue - fromValue) / steps;
            const stepDuration = duration / steps;
            
            let currentStep = 0;
            
            const animation = setInterval(() => {
                currentStep++;
                const currentValue = fromValue + (stepValue * currentStep);
                
                if (currentStep >= steps) {
                    if (isPercentage) {
                        element.textContent = `${toValue}%`;
                    } else if (formatWithCommas) {
                        element.textContent = parseInt(toValue).toLocaleString();
                    } else {
                        element.textContent = toValue;
                    }
                    clearInterval(animation);
                } else {
                    const displayValue = Math.round(currentValue);
                    if (isPercentage) {
                        element.textContent = `${displayValue}%`;
                    } else if (formatWithCommas) {
                        element.textContent = displayValue.toLocaleString();
                    } else {
                        element.textContent = displayValue;
                    }
                }
            }, stepDuration);
        }

        function updateCutoffRangeBadge() {
            const badge = document.getElementById('cutoff_range_badge');
            if (badge) {
                const date = document.getElementById('cutoff_date_picker')?.value || new Date().toISOString().split('T')[0];
                const shift = document.getElementById('shift_selector')?.value || 'day';
                const period = document.getElementById('cutoff_period_selector')?.value || '1';

                let badgeText = '';
                const dateObj = new Date(date);
                const formattedDate = dateObj.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });

                if (shift === 'all') {
                    badgeText = `${formattedDate} - All Shifts`;
                } else {
                    const shiftText = shift.charAt(0).toUpperCase() + shift.slice(1);
                    const periodText = period === 'all' ? 'All' : `${period}${period === '1' ? 'st' : period === '2' ? 'nd' : 'rd'}`;
                    badgeText = `${formattedDate} - ${shiftText} ${periodText}`;
                }

                badge.textContent = badgeText;
            }
        }

        /**
         * Calculate and set current cutoff defaults based on Asia/Manila timezone
         * Shift Schedule:
         * - Day Shift: 07:00 - 18:59 (3 cutoffs: 7-12, 12-16, 16-19)
         * - Night Shift: 19:00 - 06:59 (3 cutoffs: 19-24, 0-4, 4-7)
         */
        function setCurrentCutoffDefaults() {
            // Get current time in Asia/Manila timezone
            const now = new Date();
            const manilaTime = new Date(now.toLocaleString("en-US", { timeZone: "Asia/Manila" }));
            const hours = manilaTime.getHours();
            const minutes = manilaTime.getMinutes();

            let shift, cutoff, cutoffDate;

            if (hours >= 7 && hours <= 18) {
                // Day Shift: 07:00 - 18:59
                shift = 'day';
                cutoffDate = manilaTime; // Same day

                if (hours >= 7 && hours < 12) {
                    cutoff = '1'; // 07:00 - 11:59
                } else if (hours >= 12 && hours < 16) {
                    cutoff = '2'; // 12:00 - 15:59
                } else {
                    cutoff = '3'; // 16:00 - 18:59
                }
            } else {
                // Night Shift: 19:00 - 06:59
                shift = 'night';

                if (hours >= 19) {
                    // 19:00 - 23:59 (same day)
                    cutoff = '1';
                    cutoffDate = manilaTime; // Same day
                } else if (hours >= 0 && hours < 4) {
                    // 00:00 - 03:59 (next day, but shift started previous day)
                    cutoff = '2';
                    cutoffDate = new Date(manilaTime);
                    cutoffDate.setDate(cutoffDate.getDate() - 1); // Previous day
                } else {
                    // 04:00 - 06:59 (next day, but shift started previous day)
                    cutoff = '3';
                    cutoffDate = new Date(manilaTime);
                    cutoffDate.setDate(cutoffDate.getDate() - 1); // Previous day
                }
            }

            // Format date for input
            const dateString = cutoffDate.getFullYear() + '-' +
                String(cutoffDate.getMonth() + 1).padStart(2, '0') + '-' +
                String(cutoffDate.getDate()).padStart(2, '0');


            // Only set defaults if no URL parameters are present (fresh page load)
            const urlParams = new URLSearchParams(window.location.search);
            const hasDateParam = urlParams.has('cutoff_date');
            const hasShiftParam = urlParams.has('cutoff_shift');
            const hasPeriodParam = urlParams.has('cutoff_period');

            if (!hasDateParam && !hasShiftParam && !hasPeriodParam) {
                // Set the calculated values
                const dateInput = document.getElementById('cutoff_date_picker');
                const shiftSelect = document.getElementById('shift_selector');
                const periodSelect = document.getElementById('cutoff_period_selector');
                const hiddenDate = document.getElementById('cutoff_date');
                const hiddenShift = document.getElementById('cutoff_shift');
                const hiddenPeriod = document.getElementById('cutoff_period');

                if (dateInput) dateInput.value = dateString;
                if (shiftSelect) shiftSelect.value = shift;
                if (periodSelect) periodSelect.value = cutoff;
                if (hiddenDate) hiddenDate.value = dateString;
                if (hiddenShift) hiddenShift.value = shift;
                if (hiddenPeriod) hiddenPeriod.value = cutoff;

            }
        }
        
        /**
         * Show toast notification
         */
        function showToast(message, type = 'info') {
            // Create or update toast element
            let toast = document.getElementById('endtime-toast');

            if (!toast) {
                toast = document.createElement('div');
                toast.id = 'endtime-toast';
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 9999;
                    max-width: 300px;
                    padding: 12px 16px;
                    border-radius: 4px;
                    color: white;
                    font-weight: 500;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    opacity: 0;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                `;
                document.body.appendChild(toast);
            }

            // Set background color based on type
            const colors = {
                success: '#22c55e',
                error: '#ef4444',
                warning: '#f59e0b',
                info: '#3b82f6',
            };

            toast.style.backgroundColor = colors[type] || colors.info;
            toast.textContent = message;

            // Show toast
            requestAnimationFrame(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            });

            // Hide after delay
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
            }, type === 'error' ? 5000 : 3000);
        }
        
        /**
         * Initialize table search functionality
         */
        function initializeTableSearch() {
            const searchInput = document.getElementById('tableSearch');
            const clearButton = document.getElementById('clearSearch');
            const hiddenSearchField = document.getElementById('hidden_search');
            const form = document.getElementById('endtime-filters-form');
            
            if (!searchInput || !form) return;
            
            let searchTimeout;
            
            // Search input handler with debounce
            searchInput.addEventListener('input', function() {
                const searchValue = this.value.trim();
                
                // Show/hide clear button
                if (clearButton) {
                    clearButton.style.display = searchValue ? 'block' : 'none';
                }
                
                // Clear existing timeout
                if (searchTimeout) {
                    clearTimeout(searchTimeout);
                }
                
                // Set new timeout for search
                searchTimeout = setTimeout(() => {
                    performTableSearch(searchValue);
                }, 300); // 300ms debounce
            });
            
            // Enter key handler
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const searchValue = this.value.trim();
                    performTableSearch(searchValue);
                }
            });
            
            // Clear button handler
            if (clearButton) {
                clearButton.addEventListener('click', function() {
                    searchInput.value = '';
                    this.style.display = 'none';
                    performTableSearch('');
                    searchInput.focus();
                });
            }
        }
        
        /**
         * Perform table search by updating form and submitting
         */
        function performTableSearch(searchValue) {
            const hiddenSearchField = document.getElementById('hidden_search');
            const form = document.getElementById('endtime-filters-form');
            
            if (hiddenSearchField && form) {
                hiddenSearchField.value = searchValue;
                
                // Show loading state
                const searchInput = document.getElementById('tableSearch');
                if (searchInput) {
                    searchInput.disabled = true;
                    searchInput.placeholder = 'Searching...';
                }
                
                // Submit form to filter results
                form.submit();
            }
        }
        
        // Global variables for sorting
        let currentSortBy = 'updated_at';
        let currentSortOrder = 'desc';
        let currentEquipmentData = [];
        
        /**
         * Show equipment without ongoing lots modal
         */
        function showEquipmentWithoutOngoingModal() {
            const modalElement = document.getElementById('equipmentWithoutOngoingModal');
            const modal = new bootstrap.Modal(modalElement);
            const modalContent = document.getElementById('equipmentWithoutOngoingContent');
            
            // Reset sorting to default
            currentSortBy = 'updated_at';
            currentSortOrder = 'desc';
            
            // Handle modal events to fix focus issues
            modalElement.addEventListener('hide.bs.modal', function () {
                // Remove focus from modal and all its children BEFORE Bootstrap sets aria-hidden
                if (document.activeElement && modalElement.contains(document.activeElement)) {
                    document.activeElement.blur();
                }
                // Also blur the modal itself if it has focus
                if (document.activeElement === modalElement) {
                    modalElement.blur();
                }
            }, { once: true });
            
            modalElement.addEventListener('hidden.bs.modal', function () {
                // Return focus to the trigger button after modal is fully hidden
                const triggerButton = document.querySelector('.without-ongoing-badge');
                if (triggerButton) {
                    triggerButton.focus();
                }
            }, { once: true });
            
            // Show loading state
            modalContent.innerHTML = `
                <div class="d-flex justify-content-center align-items-center py-4">
                    <div class="spinner-border text-danger" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span class="ms-3">Loading equipment data...</span>
                </div>
            `;
            
            // Show the modal
            modal.show();
            
            // Load equipment data
            loadEquipmentData();
        }
        
        /**
         * Load equipment data with current filters and sorting
         */
        function loadEquipmentData() {
            const modalContent = document.getElementById('equipmentWithoutOngoingContent');
            
            // Get current filter values
            const filters = {
                eqp_line: document.getElementById('eqp_line')?.value || 'all',
                eqp_area: document.getElementById('eqp_area')?.value || 'all',
                sort_by: currentSortBy,
                sort_order: currentSortOrder
            };
            
            // Make AJAX request to fetch equipment data
            fetch('/api/endtime/equipment-without-ongoing?' + new URLSearchParams(filters), {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    currentEquipmentData = data.equipment;
                    renderEquipmentWithoutOngoingTable(data.equipment, data.filters_applied, data.sort_by, data.sort_order);
                } else {
                    modalContent.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Error loading equipment data: ${data.message || 'Unknown error'}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error fetching equipment without ongoing:', error);
                modalContent.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Failed to load equipment data. Please try again.
                    </div>
                `;
            });
        }
        
        /**
         * Handle column sorting
         */
        function sortEquipmentTable(column) {
            // Toggle sort order if same column, otherwise default to ascending
            if (currentSortBy === column) {
                currentSortOrder = currentSortOrder === 'asc' ? 'desc' : 'asc';
            } else {
                currentSortBy = column;
                currentSortOrder = column === 'updated_at' ? 'desc' : 'asc'; // Default elapsed time to desc (longest first)
            }
            
            // Reload data with new sorting
            loadEquipmentData();
        }
        
        /**
         * Close equipment modal with proper focus handling
         */
        function closeEquipmentModal() {
            const modalElement = document.getElementById('equipmentWithoutOngoingModal');
            const modal = bootstrap.Modal.getInstance(modalElement);
            
            // Remove focus from any focused element inside the modal first
            if (document.activeElement && modalElement.contains(document.activeElement)) {
                document.activeElement.blur();
            }
            
            if (modal) {
                modal.hide();
            }
        }
        
        /**
         * Render equipment without ongoing lots table
         */
        function renderEquipmentWithoutOngoingTable(equipment, filtersApplied, sortBy = 'updated_at', sortOrder = 'desc') {
            const modalContent = document.getElementById('equipmentWithoutOngoingContent');
            
            if (!equipment || equipment.length === 0) {
                modalContent.innerHTML = `
                    <div class="text-center py-4">
                        <div class="mb-3">
                            <i class="fas fa-check-circle text-success" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="text-success">Great! All equipment has ongoing lots</h5>
                        <p class="text-muted">No equipment found without ongoing lots matching the current filters.</p>
                        <div class="mt-3">
                            <small class="text-muted">
                                <strong>Applied Filters:</strong> 
                                Line: <span class="badge bg-light text-dark">${filtersApplied.eqp_line}</span>, 
                                Area: <span class="badge bg-light text-dark">${filtersApplied.eqp_area}</span>
                            </small>
                        </div>
                    </div>
                `;
                return;
            }
            
            // Helper function to get sort icon
            function getSortIcon(column) {
                if (sortBy !== column) {
                    return '<i class="fas fa-sort text-muted ms-1"></i>';
                }
                return sortOrder === 'asc' 
                    ? '<i class="fas fa-sort-up text-primary ms-1"></i>' 
                    : '<i class="fas fa-sort-down text-primary ms-1"></i>';
            }
            
            // Helper function to format elapsed time with color coding
            function formatElapsedTimeWithColor(elapsedTime, elapsedMinutes) {
                let colorClass = 'text-success'; // Default green for short times
                
                if (elapsedMinutes > 1440) { // More than 1 day
                    colorClass = 'text-danger fw-bold';
                } else if (elapsedMinutes > 480) { // More than 8 hours
                    colorClass = 'text-warning fw-bold';
                } else if (elapsedMinutes > 120) { // More than 2 hours
                    colorClass = 'text-info';
                }
                
                return `<span class="${colorClass}">${elapsedTime}</span>`;
            }
            
            let tableRows = '';
            equipment.forEach(eqp => {
                tableRows += `
                    <tr>
                        <td class="fw-bold">${eqp.eqp_no}</td>
                        <td>
                            <span class="badge bg-primary">${eqp.eqp_line}</span>
                        </td>
                        <td>
                            <span class="badge bg-info">${eqp.eqp_area}</span>
                        </td>
                        <td>
                            <small class="text-muted">${eqp.eqp_type || 'N/A'}</small>
                        </td>
                        <td>
                            <small class="text-muted">${eqp.eqp_maker || 'N/A'}</small>
                        </td>
                        <td>
                            <span class="badge bg-${
                                eqp.alloc_type === 'NORMAL' ? 'success' : 
                                eqp.alloc_type === 'PROCESS RW' ? 'warning' : 'secondary'
                            }">${eqp.alloc_type || 'N/A'}</span>
                        </td>
                        <td>
                            <small class="text-dark">${parseFloat(eqp.eqp_oee || 0).toFixed(2)}</small>
                        </td>
                        <td class="text-end">
                            <strong class="text-primary">${parseInt(eqp.oee_capa || 0).toLocaleString()}</strong>
                        </td>
                        <td class="text-center">
                            ${formatElapsedTimeWithColor(eqp.elapsed_time || 'Unknown', eqp.elapsed_minutes || 0)}
                        </td>
                    </tr>
                `;
            });
            
            modalContent.innerHTML = `
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">
                                <i class="fas fa-cogs text-danger me-1"></i>
                                Equipment List (${equipment.length} items)
                            </h6>
                        </div>
                        <div>
                            <small class="text-muted">
                                <strong>Filters:</strong> 
                                Line: <span class="badge bg-light text-dark">${filtersApplied.eqp_line}</span>, 
                                Area: <span class="badge bg-light text-dark">${filtersApplied.eqp_area}</span>
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="table-responsive" style="max-height: 450px; overflow-y: auto;">
                    <table class="table table-hover table-sm mb-0">
                        <thead class="table-light sticky-top">
                            <tr>
                                <th class="sortable-header" onclick="sortEquipmentTable('eqp_no')" style="cursor: pointer;">
                                    Equipment No ${getSortIcon('eqp_no')}
                                </th>
                                <th class="sortable-header" onclick="sortEquipmentTable('eqp_line')" style="cursor: pointer;">
                                    Line ${getSortIcon('eqp_line')}
                                </th>
                                <th class="sortable-header" onclick="sortEquipmentTable('eqp_area')" style="cursor: pointer;">
                                    Area ${getSortIcon('eqp_area')}
                                </th>
                                <th>Type</th>
                                <th>Maker</th>
                                <th class="sortable-header" onclick="sortEquipmentTable('alloc_type')" style="cursor: pointer;">
                                    Alloc Type ${getSortIcon('alloc_type')}
                                </th>
                                <th>OEE</th>
                                <th class="sortable-header text-end" onclick="sortEquipmentTable('oee_capa')" style="cursor: pointer;">
                                    Capacity ${getSortIcon('oee_capa')}
                                </th>
                                <th class="sortable-header text-center" onclick="sortEquipmentTable('updated_at')" style="cursor: pointer;" title="Time since equipment last had an ongoing lot">
                                    Elapsed Time ${getSortIcon('updated_at')}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            ${tableRows}
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        <span class="text-success">Green</span>: ≤2h, 
                        <span class="text-info">Blue</span>: 2-8h, 
                        <span class="text-warning">Yellow</span>: 8-24h, 
                        <span class="text-danger">Red</span>: >1d
                        | Click column headers to sort
                    </small>
                </div>
            `;
        }
    </script>
    
    <!-- Lot Details Modal -->
    <div class="modal fade" id="lotDetailsModal" tabindex="-1" aria-labelledby="lotDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="lotDetailsModalLabel">
                        <i class="fas fa-info-circle me-2"></i>Lot Details
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="lotDetailsContent">
                    <div class="d-flex justify-content-center align-items-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span class="ms-3">Loading lot details...</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Close
                    </button>
                    <button type="button" class="btn btn-warning" id="editFromModalBtn" style="display: none;">
                        <i class="fas fa-edit me-1"></i>Edit/Modify
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Equipment Without Ongoing Modal -->
    <div class="modal fade" id="equipmentWithoutOngoingModal" tabindex="-1" aria-labelledby="equipmentWithoutOngoingModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="equipmentWithoutOngoingModalLabel">
                        <i class="fas fa-pause-circle me-2"></i>Equipment Without Ongoing Lots
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="equipmentWithoutOngoingContent">
                    <div class="d-flex justify-content-center align-items-center py-4">
                        <div class="spinner-border text-danger" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span class="ms-3">Loading equipment data...</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="d-flex justify-content-between w-100 align-items-center">
                        <div>
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Equipment with OPERATIONAL status but no ongoing lots assigned
                            </small>
                        </div>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="closeEquipmentModal()">
                            <i class="fas fa-times me-1"></i>Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
