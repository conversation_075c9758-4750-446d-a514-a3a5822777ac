<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class EquipmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing data
        DB::table('equipment')->truncate();

        $csvFile = base_path('equipment.csv');
        
        if (File::exists($csvFile)) {
            $csv = array_map('str_getcsv', file($csvFile));
            
            // Remove the header row
            $header = array_shift($csv);
            
            foreach ($csv as $row) {
                if (count($row) >= 23 && !empty($row[0])) {
                    // Helper function to clean numeric values
                    $cleanNumeric = function($value) {
                        if ($value === null || $value === '') {
                            return 0;
                        }
                        // Remove commas and quotes, then trim
                        $cleaned = str_replace([',', '"', "'"], '', trim((string)$value));
                        return $cleaned === '' ? 0 : (int)$cleaned;
                    };
                    
                    // Helper function to clean decimal values
                    $cleanDecimal = function($value) {
                        if ($value === null || $value === '') {
                            return 0.0;
                        }
                        $cleaned = str_replace([',', '"', "'"], '', trim((string)$value));
                        return $cleaned === '' ? 0.0 : (float)$cleaned;
                    };
                    
                    DB::table('equipment')->insert([
                        'eqp_no' => trim($row[0]),
                        'eqp_line' => trim($row[1]),
                        'eqp_area' => trim($row[2]),
                        'eqp_maker' => trim($row[3]),
                        'cam_class' => trim($row[4]),
                        'insp_type' => trim($row[5]),
                        'linear_type' => trim($row[6]),
                        'eqp_type' => trim($row[7]),
                        'size' => trim($row[8]),
                        'alloc_type' => !empty(trim($row[9])) ? trim($row[9]) : null,
                        'eqp_status' => trim($row[10]),
                        'loading_speed' => $cleanNumeric($row[11]),
                        'eqp_oee' => $cleanDecimal($row[12]),
                        'eqp_passing' => $cleanDecimal($row[13]),
                        'eqp_yield' => $cleanDecimal($row[14]),
                        'operation_time' => $cleanNumeric($row[15]),
                        'ideal_capa' => $cleanNumeric($row[16]),
                        'oee_capa' => $cleanNumeric($row[17]),
                        'output_capa' => $cleanNumeric($row[18]),
                        'ongoing_lot' => !empty(trim($row[19])) ? trim($row[19]) : null,
                        'modified_by' => !empty(trim($row[20])) ? trim($row[20]) : null,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }
    }
}
