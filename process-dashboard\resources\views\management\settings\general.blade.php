<x-app-layout>
    <x-slot name="header">
        General Settings
    </x-slot>

    <!-- Breadcrumb -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('management.settings.index') }}">Settings</a></li>
                    <li class="breadcrumb-item active" aria-current="page">General</li>
                </ol>
            </nav>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>General Application Settings
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('management.settings.update.general') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row g-4">
                            <div class="col-md-6">
                                <label for="app_name" class="form-label">Application Name <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('app_name') is-invalid @enderror" 
                                       id="app_name" 
                                       name="app_name" 
                                       value="{{ old('app_name', $settings['app_name']) }}" 
                                       required>
                                @error('app_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="contact_email" class="form-label">Contact Email <span class="text-danger">*</span></label>
                                <input type="email" 
                                       class="form-control @error('contact_email') is-invalid @enderror" 
                                       id="contact_email" 
                                       name="contact_email" 
                                       value="{{ old('contact_email', $settings['contact_email']) }}" 
                                       required>
                                @error('contact_email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-12">
                                <label for="app_description" class="form-label">Application Description</label>
                                <textarea class="form-control @error('app_description') is-invalid @enderror" 
                                          id="app_description" 
                                          name="app_description" 
                                          rows="3" 
                                          placeholder="Brief description of your application">{{ old('app_description', $settings['app_description']) }}</textarea>
                                @error('app_description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="timezone" class="form-label">Timezone <span class="text-danger">*</span></label>
                                <select class="form-select @error('timezone') is-invalid @enderror" 
                                        id="timezone" 
                                        name="timezone" 
                                        required>
                                    <option value="">Select Timezone</option>
                                    @php
                                        $timezones = [
                                            'Asia/Tokyo' => 'Japan (JST)',
                                            'Asia/Seoul' => 'South Korea (KST)',
                                            'Asia/Shanghai' => 'China (CST)',
                                            'Asia/Hong_Kong' => 'Hong Kong (HKT)',
                                            'Asia/Taipei' => 'Taiwan (CST)',
                                            'Asia/Singapore' => 'Singapore (SGT)',
                                            'Asia/Bangkok' => 'Thailand (ICT)',
                                            'Asia/Jakarta' => 'Indonesia (WIB)',
                                            'Asia/Manila' => 'Philippines (PHT)',
                                            'Asia/Kuala_Lumpur' => 'Malaysia (MYT)',
                                            'Asia/Kolkata' => 'India (IST)',
                                            'Asia/Dhaka' => 'Bangladesh (BST)',
                                            'Asia/Karachi' => 'Pakistan (PKT)',
                                        ];
                                    @endphp
                                    @foreach($timezones as $value => $label)
                                        <option value="{{ $value }}" {{ old('timezone', $settings['timezone']) == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('timezone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="unit" class="form-label">Unit <span class="text-danger">*</span></label>
                                <select class="form-select @error('unit') is-invalid @enderror" 
                                        id="unit" 
                                        name="unit" 
                                        required>
                                    <option value="">Select Unit</option>
                                    @php
                                        $units = [
                                            'PCS' => 'PCS (Pieces)',
                                            'K PCS' => 'K PCS (Thousand Pieces)',
                                            'M PCS' => 'M PCS (Million Pieces)',
                                        ];
                                    @endphp
                                    @foreach($units as $value => $label)
                                        <option value="{{ $value }}" {{ old('unit', $settings['unit'] ?? 'PCS') == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('unit')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Submit Button -->
                            <div class="col-12 mt-4">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('management.settings.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Settings
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Save Changes
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">
                        <i class="fas fa-eye me-2"></i>Current Settings
                    </h6>
                </div>
                <div class="card-body">
                    <div class="setting-preview">
                        <div class="mb-3">
                            <label class="form-label text-muted small"><i class="fas fa-tag me-1"></i>Application Name</label>
                            <p class="mb-0 fw-semibold">{{ $settings['app_name'] }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small"><i class="fas fa-envelope me-1"></i>Contact Email</label>
                            <p class="mb-0">{{ $settings['contact_email'] }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small"><i class="fas fa-align-left me-1"></i>Description</label>
                            <p class="mb-0">{{ $settings['app_description'] ?: 'No description' }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small"><i class="fas fa-globe me-1"></i>Timezone</label>
                            <p class="mb-0">{{ $settings['timezone'] }}</p>
                        </div>
                        <div class="mb-0">
                            <label class="form-label text-muted small"><i class="fas fa-cube me-1"></i>Unit</label>
                            <p class="mb-0">{{ $settings['unit'] ?? 'PCS' }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Settings Help
                    </h6>
                </div>
                <div class="card-body">
                    <div class="help-tips">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Tip:</strong> Changes to general settings will take effect immediately for all users.
                        </div>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-tag text-primary me-2"></i><strong>Application Name:</strong> Appears in the browser title and header</li>
                            <li><i class="fas fa-envelope text-success me-2"></i><strong>Contact Email:</strong> Used for system notifications and support</li>
                            <li><i class="fas fa-align-left text-info me-2"></i><strong>Description:</strong> Brief overview of your application purpose</li>
                            <li><i class="fas fa-globe text-warning me-2"></i><strong>Timezone:</strong> Affects all date/time displays across the system</li>
                            <li><i class="fas fa-cube text-secondary me-2"></i><strong>Unit:</strong> Default unit for quantity measurements (PCS, K PCS, M PCS)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>