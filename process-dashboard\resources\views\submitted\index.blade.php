<x-app-layout>
    <x-slot name="header">
        Submitted Lots
    </x-slot>

    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Submitted Lots</h4>
                <div class="d-flex gap-2">
                    <span class="badge bg-info">{{ $stats['total_submitted'] ?? 0 }} Total Submitted</span>
                    <span class="badge bg-success">{{ $stats['submitted_today'] ?? 0 }} Today</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header bg-light">
            <h6 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('submitted.index') }}" class="row g-3">
                <div class="col-md-2">
                    <label for="date_range" class="form-label">Date Range</label>
                    <select name="date_range" id="date_range" class="form-select">
                        <option value="today" {{ $filters['date_range'] === 'today' ? 'selected' : '' }}>Today</option>
                        <option value="yesterday" {{ $filters['date_range'] === 'yesterday' ? 'selected' : '' }}>Yesterday</option>
                        <option value="this_week" {{ $filters['date_range'] === 'this_week' ? 'selected' : '' }}>This Week</option>
                        <option value="this_month" {{ $filters['date_range'] === 'this_month' ? 'selected' : '' }}>This Month</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="eqp_line" class="form-label">Equipment Line</label>
                    <select name="eqp_line" id="eqp_line" class="form-select">
                        <option value="all">All Lines</option>
                        @foreach($filterOptions['equipment_lines'] as $line)
                            <option value="{{ $line }}" {{ $filters['eqp_line'] === $line ? 'selected' : '' }}>{{ $line }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="eqp_area" class="form-label">Equipment Area</label>
                    <select name="eqp_area" id="eqp_area" class="form-select">
                        <option value="all">All Areas</option>
                        @foreach($filterOptions['equipment_areas'] as $area)
                            <option value="{{ $area }}" {{ $filters['eqp_area'] === $area ? 'selected' : '' }}>{{ $area }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="lot_type" class="form-label">Lot Type</label>
                    <select name="lot_type" id="lot_type" class="form-select">
                        <option value="all">All Lot Types</option>
                        @foreach($filterOptions['lot_types'] as $type)
                            <option value="{{ $type }}" {{ $filters['lot_type'] === $type ? 'selected' : '' }}>{{ $type }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="submitted_by" class="form-label">Submitted By</label>
                    <select name="submitted_by" id="submitted_by" class="form-select">
                        <option value="all">All Submitters</option>
                        @foreach($filterOptions['submitters'] as $submitter)
                            <option value="{{ $submitter }}" {{ $filters['submitted_by'] === $submitter ? 'selected' : '' }}>{{ $submitter }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" name="search" id="search" class="form-control" 
                           placeholder="Lot ID, Equipment, Line..." 
                           value="{{ $filters['search'] }}">
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Statistics Row -->
    <div class="row mb-4">
        @if(!empty($stats['line_breakdown']))
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-industry me-2"></i>Equipment Line Breakdown</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($stats['line_breakdown'] as $line => $count)
                        <div class="col-md-6 mb-2">
                            <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                                <span class="fw-semibold">{{ $line }}</span>
                                <span class="badge bg-success">{{ $count }} lots</span>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
        @endif
        @if(!empty($stats['area_breakdown']))
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>Equipment Area Breakdown</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($stats['area_breakdown'] as $area => $count)
                        <div class="col-md-6 mb-2">
                            <div class="d-flex justify-content-between align-items-center p-2 bg-light rounded">
                                <span class="fw-semibold">{{ $area }}</span>
                                <span class="badge bg-primary">{{ $count }} lots</span>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Submitted Lots Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-success text-white">
            <h6 class="mb-0"><i class="fas fa-check-circle me-2"></i>Submitted Lots</h6>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Lot ID</th>
                            <th>Model</th>
                            <th>Size/Qty</th>
                            <th>Work Type</th>
                            <th>Lot Type</th>
                            <th>Equipment</th>
                            <th>Line/Area</th>
                            <th>Submitted At</th>
                            <th>Submitted By</th>
                            <th>LIPAS</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($submittedLots as $lot)
                        <tr>
                            <td>
                                <strong>{{ $lot->lot_id }}</strong>
                            </td>
                            <td>{{ $lot->model_15 }}</td>
                            <td>
                                <span class="badge bg-secondary">{{ $lot->lot_size }}</span>
                                <br><small class="text-muted">Qty: {{ number_format($lot->lot_qty) }}</small>
                            </td>
                            <td>
                                <span class="badge bg-{{ $lot->work_type === 'PROD' ? 'primary' : 'secondary' }}">
                                    {{ $lot->work_type }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{{ $lot->lot_type === 'HOT' ? 'danger' : ($lot->lot_type === 'SUPER_HOT' ? 'warning' : 'info') }}">
                                    {{ $lot->lot_type }}
                                </span>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ $lot->eqp_1 }}
                                    @if($lot->eqp_2), {{ $lot->eqp_2 }}@endif
                                    @if($lot->eqp_3), {{ $lot->eqp_3 }}@endif
                                </small>
                            </td>
                            <td>
                                <strong>{{ $lot->eqp_line }}</strong>
                                <br><small class="text-muted">{{ $lot->eqp_area }}</small>
                            </td>
                            <td>
                                <div class="text-primary">
                                    <strong>{{ $lot->submitted_at->format('M d, H:i') }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $lot->submitted_at->diffForHumans() }}</small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $lot->submitted_by }}</span>
                            </td>
                            <td>
                                <span class="badge bg-{{ $lot->lipas_yn === 'Y' ? 'success' : 'secondary' }}">
                                    {{ $lot->lipas_yn === 'Y' ? 'Yes' : 'No' }}
                                </span>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="10" class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">No submitted lots found for the selected criteria</h6>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        @if($submittedLots->hasPages())
        <div class="card-footer bg-light">
            {{ $submittedLots->links() }}
        </div>
        @endif
    </div>

    <style>
        .table-responsive {
            border-radius: 0.5rem;
        }
        
        .badge {
            font-size: 0.75rem;
        }
        
        .table > :not(caption) > * > * {
            padding: 0.75rem 0.5rem;
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }
    </style>
</x-app-layout>
