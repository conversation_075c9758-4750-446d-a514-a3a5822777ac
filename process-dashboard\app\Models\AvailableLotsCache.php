<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Builder;

class AvailableLotsCache extends Model
{
    use HasFactory;
    
    protected $table = 'available_lots_cache';

    protected $fillable = [
        'lot_code',
        'lot_size',
        'eqp_type',
        'eqp_class',
        'work_type',
        'lipas_yn',
        'stagnant_tat',
        'lot_location',
        'available_quantity',
        'is_available',
'last_updated',
        'model_15',
        'qty_class'
    ];

    protected $casts = [
        'lipas_yn' => 'boolean',
        'is_available' => 'boolean',
'stagnant_tat' => 'decimal:2',
        'available_quantity' => 'integer',
        'last_updated' => 'datetime'
    ];

    /**
     * Find lots matching equipment criteria
     */
    public static function findMatchingLots($equipment)
    {
        return self::where('is_available', true)
            ->where('available_quantity', '>', 0)
            ->when($equipment->size, function ($query, $size) {
                return $query->where('lot_size', $size);
            })
            ->when($equipment->cam_class, function ($query, $camClass) {
                return $query->where('eqp_type', $camClass);
            })
            ->when($equipment->insp_type, function ($query, $inspType) {
                return $query->where('eqp_class', $inspType);
            })
            ->when($equipment->alloc_type, function ($query, $allocType) {
                return $query->where('work_type', $allocType);
            });
    }

    /**
     * Get lots with priority sorting (LIPAS_YN and stagnant TAT)
     */
    public function scopePriorityOrder(Builder $query)
    {
        return $query->orderByDesc('lipas_yn')    // LIPAS lots first
                    ->orderByDesc('stagnant_tat') // High stagnant TAT first
                    ->orderBy('created_at');       // FIFO for same priority
    }

    /**
     * Get priority score for sorting
     */
    public function getPriorityScoreAttribute()
    {
        $score = 0;
        
        // LIPAS gets highest priority
        if ($this->lipas_yn) {
            $score += 1000;
        }
        
        // Add stagnant TAT score
        $score += ($this->stagnant_tat ?? 0);
        
        return $score;
    }

    /**
     * Get priority level description
     */
    public function getPriorityLevelAttribute()
    {
        if ($this->lipas_yn) {
            return 'LIPAS';
        }
        
        $tat = $this->stagnant_tat ?? 0;
        if ($tat >= 72) return 'Critical';
        if ($tat >= 48) return 'High';
        if ($tat >= 24) return 'Medium';
        return 'Normal';
    }

    /**
     * Get priority badge class
     */
    public function getPriorityBadgeClassAttribute()
    {
        return match($this->priority_level) {
            'LIPAS' => 'bg-purple text-white',
            'Critical' => 'bg-danger',
            'High' => 'bg-warning',
            'Medium' => 'bg-info',
            default => 'bg-secondary'
        };
    }

    /**
     * Check if lot matches equipment requirements
     */
    public function matchesEquipment($equipment)
    {
        $matches = true;
        
        // Check size matching
        if ($equipment->size && $this->lot_size !== $equipment->size) {
            $matches = false;
        }
        
        // Check cam_class (COLOR/MONO) matching
        if ($equipment->cam_class && $this->eqp_type !== $equipment->cam_class) {
            $matches = false;
        }
        
        // Check insp_type (6S/4S) matching
        if ($equipment->insp_type && $this->eqp_class !== $equipment->insp_type) {
            $matches = false;
        }
        
        // Check alloc_type (NORMAL/PROCESS RW/OI REWORK/WH REWORK) matching
        if ($equipment->alloc_type && $this->work_type !== $equipment->alloc_type) {
            $matches = false;
        }
        
        return $matches;
    }

    /**
     * Reserve lot quantity for assignment
     */
    public function reserveQuantity($quantity = 1)
    {
        if ($this->available_quantity >= $quantity) {
            $this->decrement('available_quantity', $quantity);
            
            if ($this->available_quantity <= 0) {
                $this->update(['is_available' => false]);
            }
            
            return true;
        }
        
        return false;
    }

    /**
     * Release reserved quantity back to available pool
     */
    public function releaseQuantity($quantity = 1)
    {
        $this->increment('available_quantity', $quantity);
        $this->update(['is_available' => true]);
    }
}