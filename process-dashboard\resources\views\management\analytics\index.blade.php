<x-app-layout>
    <x-slot name="header">
        Advanced Analytics
    </x-slot>

    <!-- Overview Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success bg-opacity-10 text-success me-3">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">${{ number_format($analytics['overview']['total_revenue'], 2) }}</h3>
                            <p class="text-muted mb-0">Total Revenue</p>
                            <small class="text-{{ $analytics['overview']['revenue_growth'] >= 0 ? 'success' : 'danger' }}">
                                <i class="fas fa-arrow-{{ $analytics['overview']['revenue_growth'] >= 0 ? 'up' : 'down' }}"></i>
                                {{ abs($analytics['overview']['revenue_growth']) }}% vs last month
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary bg-opacity-10 text-primary me-3">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ number_format($analytics['overview']['total_orders']) }}</h3>
                            <p class="text-muted mb-0">Total Orders</p>
                            <small class="text-{{ $analytics['overview']['order_growth'] >= 0 ? 'success' : 'danger' }}">
                                <i class="fas fa-arrow-{{ $analytics['overview']['order_growth'] >= 0 ? 'up' : 'down' }}"></i>
                                {{ abs($analytics['overview']['order_growth']) }}% vs last month
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-info bg-opacity-10 text-info me-3">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ number_format($analytics['overview']['total_customers']) }}</h3>
                            <p class="text-muted mb-0">Total Customers</p>
                            <small class="text-muted">Active user base</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-warning bg-opacity-10 text-warning me-3">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">${{ number_format($analytics['overview']['avg_order_value'], 2) }}</h3>
                            <p class="text-muted mb-0">Avg Order Value</p>
                            <small class="text-muted">Per transaction</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Navigation -->
    <div class="row g-4 mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="mb-3">Analytics Sections</h5>
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="{{ route('management.analytics.sales') }}" class="btn btn-outline-primary w-100 h-100">
                                <i class="fas fa-chart-bar fa-2x mb-2 d-block"></i>
                                <strong>Sales Analytics</strong>
                                <small class="d-block text-muted">Revenue & performance</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('management.analytics.customers') }}" class="btn btn-outline-success w-100 h-100">
                                <i class="fas fa-users fa-2x mb-2 d-block"></i>
                                <strong>Customer Analytics</strong>
                                <small class="d-block text-muted">Behavior & segments</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('management.analytics.products') }}" class="btn btn-outline-info w-100 h-100">
                                <i class="fas fa-boxes fa-2x mb-2 d-block"></i>
                                <strong>Product Analytics</strong>
                                <small class="d-block text-muted">Inventory & performance</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('management.analytics.financial') }}" class="btn btn-outline-warning w-100 h-100">
                                <i class="fas fa-dollar-sign fa-2x mb-2 d-block"></i>
                                <strong>Financial Analytics</strong>
                                <small class="d-block text-muted">Revenue & profitability</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Data -->
    <div class="row g-4">
        <!-- Sales Trends -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Sales Trends (Last 30 Days)</h5>
                </div>
                <div class="card-body">
                    <div style="height: 300px;">
                        <canvas id="salesTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Products -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Top Products</h5>
                </div>
                <div class="card-body">
                    @if($analytics['product_performance']['best_sellers']->count() > 0)
                        @foreach($analytics['product_performance']['best_sellers'] as $product)
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h6 class="mb-0">{{ $product->name }}</h6>
                                <small class="text-muted">{{ $product->total_sold ?? 0 }} sold</small>
                            </div>
                            <div class="text-end">
                                <strong>${{ number_format($product->total_revenue ?? 0, 2) }}</strong>
                            </div>
                        </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-box fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No sales data available</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="row g-4 mt-2">
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Recent Orders</h5>
                </div>
                <div class="card-body">
                    @if($analytics['recent_activities']['recent_orders']->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Order</th>
                                        <th>Customer</th>
                                        <th>Amount</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($analytics['recent_activities']['recent_orders']->take(5) as $order)
                                    <tr>
                                        <td><strong>{{ $order->order_number }}</strong></td>
                                        <td>{{ $order->user->name }}</td>
                                        <td>${{ number_format($order->total_amount, 2) }}</td>
                                        <td>{{ $order->created_at->format('M d') }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No recent orders</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">New Customers</h5>
                </div>
                <div class="card-body">
                    @if($analytics['recent_activities']['new_users']->count() > 0)
                        @foreach($analytics['recent_activities']['new_users'] as $user)
                        <div class="d-flex align-items-center mb-3">
                            <div class="avatar-circle bg-primary text-white me-3">
                                {{ strtoupper(substr($user->name, 0, 2)) }}
                            </div>
                            <div>
                                <h6 class="mb-0">{{ $user->name }}</h6>
                                <small class="text-muted">{{ $user->email }}</small>
                                <small class="text-muted d-block">Joined {{ $user->created_at->diffForHumans() }}</small>
                            </div>
                        </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No new customers today</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <style>
        .stats-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }
        
        .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Sales Trend Chart
            const ctx = document.getElementById('salesTrendChart');
            if (ctx && window.Chart) {
                const salesData = @json($analytics['sales_trends']['daily_trends']->pluck('revenue'));
                const salesLabels = @json($analytics['sales_trends']['daily_trends']->pluck('date'));
                
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: salesLabels,
                        datasets: [{
                            label: 'Revenue ($)',
                            data: salesData,
                            borderColor: 'rgb(99, 102, 241)',
                            backgroundColor: 'rgba(99, 102, 241, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.1)'
                                },
                                ticks: {
                                    callback: function(value) {
                                        return '$' + value.toLocaleString();
                                    }
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }
        });
    </script>
</x-app-layout>