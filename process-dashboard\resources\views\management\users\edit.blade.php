<x-app-layout>
    <x-slot name="header">
        Edit User
    </x-slot>

    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Edit User - {{ $user->emp_name }}</h4>
                <div class="d-flex gap-2">
                    <a href="{{ route('management.users.show', $user) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to User
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">User Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('management.users.update', $user) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="emp_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('emp_name') is-invalid @enderror" 
                                       id="emp_name" 
                                       name="emp_name" 
                                       value="{{ old('emp_name', $user->emp_name) }}" 
                                       required>
                                @error('emp_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="emp_no" class="form-label">Employee Number <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('emp_no') is-invalid @enderror" 
                                       id="emp_no" 
                                       name="emp_no" 
                                       value="{{ old('emp_no', $user->emp_no) }}" 
                                       required>
                                @error('emp_no')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">New Password</label>
                                <input type="password" 
                                       class="form-control @error('password') is-invalid @enderror" 
                                       id="password" 
                                       name="password" 
                                       placeholder="Leave blank to keep current password">
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Leave blank to keep the current password</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="password_confirmation" class="form-label">Confirm New Password</label>
                                <input type="password" 
                                       class="form-control" 
                                       id="password_confirmation" 
                                       name="password_confirmation" 
                                       placeholder="Confirm new password">
                            </div>
                        </div>

                        <!-- Employee Information -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="position" class="form-label">Position</label>
                                <input type="text" 
                                       class="form-control @error('position') is-invalid @enderror" 
                                       id="position" 
                                       name="position" 
                                       value="{{ old('position', $user->position) }}">
                                @error('position')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="title_class" class="form-label">Title Class</label>
                                <input type="text" 
                                       class="form-control @error('title_class') is-invalid @enderror" 
                                       id="title_class" 
                                       name="title_class" 
                                       value="{{ old('title_class', $user->title_class) }}">
                                @error('title_class')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="rank" class="form-label">Rank</label>
                                <input type="text" 
                                       class="form-control @error('rank') is-invalid @enderror" 
                                       id="rank" 
                                       name="rank" 
                                       value="{{ old('rank', $user->rank) }}">
                                @error('rank')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="hr_job_name" class="form-label">HR Job Name</label>
                                <input type="text" 
                                       class="form-control @error('hr_job_name') is-invalid @enderror" 
                                       id="hr_job_name" 
                                       name="hr_job_name" 
                                       value="{{ old('hr_job_name', $user->hr_job_name) }}">
                                @error('hr_job_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="job_assigned" class="form-label">Job Assigned</label>
                                <input type="text" 
                                       class="form-control @error('job_assigned') is-invalid @enderror" 
                                       id="job_assigned" 
                                       name="job_assigned" 
                                       value="{{ old('job_assigned', $user->job_assigned) }}">
                                @error('job_assigned')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="role" class="form-label">User Role <span class="text-danger">*</span></label>
                            <select class="form-select @error('role') is-invalid @enderror" 
                                    id="role" 
                                    name="role" 
                                    required>
                                <option value="">Select Role</option>
                                <option value="USER" {{ old('role', $user->role) === 'USER' ? 'selected' : '' }}>Regular User</option>
                                <option value="MANAGER" {{ old('role', $user->role) === 'MANAGER' ? 'selected' : '' }}>Manager</option>
                                <option value="ADMIN" {{ old('role', $user->role) === 'ADMIN' ? 'selected' : '' }}>Administrator</option>
                            </select>
                            @error('role')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">
                                <strong>Regular User:</strong> Can access basic system features and manage their own profile.<br>
                                <strong>Manager:</strong> Has access to lot requests and limited admin features.<br>
                                <strong>Administrator:</strong> Has full access to all system features and management functions.
                            </div>
                        </div>

                        <!-- User Stats Display -->
                        <div class="alert alert-info">
                            <h6 class="mb-2">Current User Statistics</h6>
                            <div class="row text-center">
                                <div class="col-md-6">
                                    <strong>{{ $user->created_at->format('M d, Y') }}</strong>
                                    <br><small>Member Since</small>
                                </div>
                                <div class="col-md-6">
                                    <strong>{{ $user->emp_verified_at ? 'Active' : 'Inactive' }}</strong>
                                    <br><small>Current Status</small>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('management.users.show', $user) }}" class="btn btn-outline-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>