<x-app-layout>
    <x-slot name="header">
        Email Settings
    </x-slot>

    <!-- Breadcrumb -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('management.settings.index') }}">Settings</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Email</li>
                </ol>
            </nav>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-envelope me-2"></i>Email Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('management.settings.update.email') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row g-4">
                            <!-- Email Identity -->
                            <div class="col-12">
                                <h6 class="text-muted border-bottom pb-2">Email Identity</h6>
                            </div>

                            <div class="col-md-6">
                                <label for="mail_from_address" class="form-label">From Address <span class="text-danger">*</span></label>
                                <input type="email" 
                                       class="form-control @error('mail_from_address') is-invalid @enderror" 
                                       id="mail_from_address" 
                                       name="mail_from_address" 
                                       value="{{ old('mail_from_address', $settings['mail_from_address']) }}" 
                                       required>
                                @error('mail_from_address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Email address that appears in the "From" field</div>
                            </div>

                            <div class="col-md-6">
                                <label for="mail_from_name" class="form-label">From Name <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('mail_from_name') is-invalid @enderror" 
                                       id="mail_from_name" 
                                       name="mail_from_name" 
                                       value="{{ old('mail_from_name', $settings['mail_from_name']) }}" 
                                       required>
                                @error('mail_from_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Name that appears in the "From" field</div>
                            </div>

                            <!-- SMTP Configuration -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">SMTP Configuration</h6>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Note:</strong> Leave SMTP fields empty to use the default mail driver.
                                </div>
                            </div>

                            <div class="col-md-6">
                                <label for="smtp_host" class="form-label">SMTP Host</label>
                                <input type="text" 
                                       class="form-control @error('smtp_host') is-invalid @enderror" 
                                       id="smtp_host" 
                                       name="smtp_host" 
                                       value="{{ old('smtp_host', $settings['smtp_host']) }}" 
                                       placeholder="e.g., smtp.gmail.com">
                                @error('smtp_host')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="smtp_port" class="form-label">SMTP Port</label>
                                <select class="form-select @error('smtp_port') is-invalid @enderror" 
                                        id="smtp_port" 
                                        name="smtp_port">
                                    <option value="">Select Port</option>
                                    <option value="25" {{ old('smtp_port', $settings['smtp_port']) == '25' ? 'selected' : '' }}>25 (Non-encrypted)</option>
                                    <option value="587" {{ old('smtp_port', $settings['smtp_port']) == '587' ? 'selected' : '' }}>587 (STARTTLS)</option>
                                    <option value="465" {{ old('smtp_port', $settings['smtp_port']) == '465' ? 'selected' : '' }}>465 (SSL/TLS)</option>
                                </select>
                                @error('smtp_port')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="smtp_username" class="form-label">SMTP Username</label>
                                <input type="text" 
                                       class="form-control @error('smtp_username') is-invalid @enderror" 
                                       id="smtp_username" 
                                       name="smtp_username" 
                                       value="{{ old('smtp_username', $settings['smtp_username']) }}" 
                                       placeholder="Your email username">
                                @error('smtp_username')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="smtp_password" class="form-label">SMTP Password</label>
                                <input type="password" 
                                       class="form-control @error('smtp_password') is-invalid @enderror" 
                                       id="smtp_password" 
                                       name="smtp_password" 
                                       value="{{ old('smtp_password', $settings['smtp_password']) }}" 
                                       placeholder="Your email password">
                                @error('smtp_password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Use app-specific password for Gmail</div>
                            </div>

                            <div class="col-md-6">
                                <label for="smtp_encryption" class="form-label">Encryption</label>
                                <select class="form-select @error('smtp_encryption') is-invalid @enderror" 
                                        id="smtp_encryption" 
                                        name="smtp_encryption">
                                    <option value="">No Encryption</option>
                                    <option value="tls" {{ old('smtp_encryption', $settings['smtp_encryption']) == 'tls' ? 'selected' : '' }}>TLS</option>
                                    <option value="ssl" {{ old('smtp_encryption', $settings['smtp_encryption']) == 'ssl' ? 'selected' : '' }}>SSL</option>
                                </select>
                                @error('smtp_encryption')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Test Email Section -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">Test Configuration</h6>
                            </div>

                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6><i class="fas fa-paper-plane me-2"></i>Send Test Email</h6>
                                        <p class="text-muted">Send a test email to verify your SMTP configuration.</p>
                                        <div class="row">
                                            <div class="col-md-8">
                                                <input type="email" 
                                                       class="form-control" 
                                                       id="test_email" 
                                                       placeholder="Enter test email address"
                                                       value="{{ auth()->user()->email }}">
                                            </div>
                                            <div class="col-md-4">
                                                <button type="button" class="btn btn-outline-primary w-100" id="sendTestEmail">
                                                    <i class="fas fa-paper-plane me-2"></i>Send Test
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="col-12 mt-4">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('management.settings.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Settings
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Save Changes
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Current Configuration</h6>
                </div>
                <div class="card-body">
                    <div class="setting-preview">
                        <div class="mb-3">
                            <label class="form-label text-muted small">From Address</label>
                            <p class="mb-0">{{ $settings['mail_from_address'] }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">From Name</label>
                            <p class="mb-0">{{ $settings['mail_from_name'] }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">SMTP Host</label>
                            <p class="mb-0">{{ $settings['smtp_host'] ?: 'Not configured' }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">SMTP Port</label>
                            <p class="mb-0">{{ $settings['smtp_port'] ?: 'Not configured' }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">Encryption</label>
                            <p class="mb-0">{{ $settings['smtp_encryption'] ?: 'None' }}</p>
                        </div>
                        <div class="mb-0">
                            <label class="form-label text-muted small">Username</label>
                            <p class="mb-0">{{ $settings['smtp_username'] ?: 'Not configured' }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Popular SMTP Providers</h6>
                </div>
                <div class="card-body">
                    <div class="smtp-providers">
                        <div class="mb-3">
                            <h6 class="small">Gmail</h6>
                            <ul class="list-unstyled small text-muted">
                                <li>Host: smtp.gmail.com</li>
                                <li>Port: 587</li>
                                <li>Encryption: TLS</li>
                            </ul>
                        </div>
                        <div class="mb-3">
                            <h6 class="small">Outlook</h6>
                            <ul class="list-unstyled small text-muted">
                                <li>Host: smtp-mail.outlook.com</li>
                                <li>Port: 587</li>
                                <li>Encryption: TLS</li>
                            </ul>
                        </div>
                        <div class="mb-0">
                            <h6 class="small">Yahoo</h6>
                            <ul class="list-unstyled small text-muted">
                                <li>Host: smtp.mail.yahoo.com</li>
                                <li>Port: 587</li>
                                <li>Encryption: TLS</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    document.getElementById('sendTestEmail').addEventListener('click', function() {
        const testEmail = document.getElementById('test_email').value;
        if (!testEmail) {
            
            return;
        }
        
        // Here you would implement the test email functionality
        // For now, just show a placeholder message
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
        this.disabled = true;
        
        setTimeout(() => {
            this.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Send Test';
            this.disabled = false;
            
        }, 2000);
    });
    </script>
</x-app-layout>
