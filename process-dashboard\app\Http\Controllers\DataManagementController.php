<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;

class DataManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:ADMIN');
    }

    /**
     * Display data management dashboard.
     */
    public function index()
    {
        $stats = [
            'total_users' => User::count(),
            'storage_used' => $this->getStorageUsage(),
            'last_backup' => $this->getLastBackupDate(),
        ];

        return view('management.data.index', compact('stats'));
    }

    /**
     * Export users data.
     */
    public function exportUsers()
    {
        $users = User::all();
        
        $csvData = "ID,Employee No,Employee Name,Role,Position,Title Class,Rank,HR Job Name,Job Assigned,Verified,Created At\n";
        
        foreach ($users as $user) {
            $csvData .= sprintf(
                "%d,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
                $user->id,
                $this->escapeCsv($user->emp_no),
                $this->escapeCsv($user->emp_name),
                $user->role,
                $this->escapeCsv($user->position ?? ''),
                $this->escapeCsv($user->title_class ?? ''),
                $this->escapeCsv($user->rank ?? ''),
                $this->escapeCsv($user->hr_job_name ?? ''),
                $this->escapeCsv($user->job_assigned ?? ''),
                $user->emp_verified_at ? 'Yes' : 'No',
                $user->created_at->format('Y-m-d H:i:s')
            );
        }

        return Response::make($csvData, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename=users_export_' . date('Y-m-d_H-i-s') . '.csv',
        ]);
    }


    /**
     * Create database backup.
     */
    public function createBackup()
    {
        try {
            $timestamp = date('Y-m-d_H-i-s');
            $backupData = [
                'created_at' => now(),
                'users' => User::all(),
            ];
            
            $backupJson = json_encode($backupData, JSON_PRETTY_PRINT);
            $filename = "backup_{$timestamp}.json";
            
            Storage::disk('local')->put("backups/{$filename}", $backupJson);
            
            return redirect()->route('management.data.index')
                           ->with('success', "Backup created successfully: {$filename}");
        } catch (\Exception $e) {
            return redirect()->route('management.data.index')
                           ->with('error', 'Failed to create backup: ' . $e->getMessage());
        }
    }

    /**
     * Download backup file.
     */
    public function downloadBackup($filename)
    {
        if (!Storage::disk('local')->exists("backups/{$filename}")) {
            abort(404);
        }
        
        return Storage::disk('local')->download("backups/{$filename}");
    }

    /**
     * List available backups.
     */
    public function listBackups()
    {
        $backups = [];
        $files = Storage::disk('local')->files('backups');
        
        foreach ($files as $file) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'json') {
                $backups[] = [
                    'filename' => basename($file),
                    'size' => Storage::disk('local')->size($file),
                    'created_at' => Storage::disk('local')->lastModified($file),
                ];
            }
        }
        
        // Sort by creation time, newest first
        usort($backups, function($a, $b) {
            return $b['created_at'] - $a['created_at'];
        });
        
        return view('management.data.backups', compact('backups'));
    }

    /**
     * Delete backup file.
     */
    public function deleteBackup($filename)
    {
        if (Storage::disk('local')->exists("backups/{$filename}")) {
            Storage::disk('local')->delete("backups/{$filename}");
            return redirect()->route('management.data.backups')
                           ->with('success', 'Backup deleted successfully');
        }
        
        return redirect()->route('management.data.backups')
                       ->with('error', 'Backup file not found');
    }

    /**
     * Get storage usage in MB.
     */
    private function getStorageUsage()
    {
        $size = 0;
        $files = Storage::disk('local')->allFiles();
        
        foreach ($files as $file) {
            $size += Storage::disk('local')->size($file);
        }
        
        return round($size / 1024 / 1024, 2); // Convert to MB
    }

    /**
     * Get last backup date.
     */
    private function getLastBackupDate()
    {
        $files = Storage::disk('local')->files('backups');
        $lastModified = 0;
        
        foreach ($files as $file) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'json') {
                $modified = Storage::disk('local')->lastModified($file);
                if ($modified > $lastModified) {
                    $lastModified = $modified;
                }
            }
        }
        
        return $lastModified ? date('Y-m-d H:i:s', $lastModified) : 'Never';
    }

    /**
     * Escape CSV values.
     */
    private function escapeCsv($value)
    {
        if (strpos($value, ',') !== false || strpos($value, '"') !== false || strpos($value, "\n") !== false) {
            return '"' . str_replace('"', '""', $value) . '"';
        }
        return $value;
    }
}