<x-app-layout>
    <x-slot name="header">
        <div class="d-flex align-items-center justify-content-between w-100">
            <div class="d-flex align-items-center">
                <i class="fas fa-chart-line me-2 text-primary"></i>
                <span>Lot Request Dashboard</span>
            </div>
            
            <!-- Header Controls -->
            <div class="d-flex align-items-center gap-2 flex-shrink-0">
                <!-- Date Range Filter -->
                <div class="d-flex align-items-center gap-1">
                    <label class="form-label mb-0 small">Period:</label>
                    <select id="periodFilter" class="form-select form-select-sm" style="width: 120px;" onchange="updateDashboardData()">
                        <option value="today">Today</option>
                        <option value="yesterday">Yesterday</option>
                        <option value="week" selected>This Week</option>
                        <option value="month">This Month</option>
                        <option value="custom">Custom Range</option>
                    </select>
                </div>
                
                <!-- Status Filter -->
                <div class="d-flex align-items-center gap-1">
                    <label class="form-label mb-0 small">Status:</label>
                    <select id="statusFilter" class="form-select form-select-sm" style="width: 100px;" onchange="updateDashboardData()">
                        <option value="all">All</option>
                        <option value="pending">Pending</option>
                        <option value="in_process">In Process</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
                
                <!-- Auto Refresh -->
                <div class="d-flex align-items-center gap-1">
                    <label class="form-label mb-0 small">Auto:</label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" role="switch" id="autoRefreshToggle" onchange="toggleAutoRefresh()">
                    </div>
                    <span class="small">30s</span>
                </div>
                
                <!-- Action Buttons -->
                <div class="d-flex gap-1">
                    <button type="button" class="btn btn-light btn-sm px-2" onclick="updateDashboardData()" title="Refresh Data">
                        <i class="fas fa-sync"></i>
                    </button>
                    <button type="button" class="btn btn-outline-light btn-sm px-2" onclick="exportDashboardData()" title="Export Data">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        </div>
    </x-slot>

    <!-- Include CSS and JS -->
    <link rel="stylesheet" href="{{ asset('css/lot-request.css') }}">
    
    <!-- Start::main content -->
    <div class="lot-analytics-dashboard">
        
        <!-- Quick Navigation Cards -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="nav-cards-container">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="{{ route('lot-requests.index') }}" class="nav-card-link">
                                <div class="nav-card primary">
                                    <div class="nav-card-icon">
                                        <i class="fas fa-list"></i>
                                    </div>
                                    <div class="nav-card-content">
                        <h5>All Requests</h5>
                        <span class="nav-card-count" id="totalRequestsCount">25</span>
                                        <p>View & manage all lot requests</p>
                                    </div>
                                    <div class="nav-card-arrow">
                                        <i class="fas fa-chevron-right"></i>
                                    </div>
                                </div>
                            </a>
                        </div>
                        
                        <div class="col-md-3">
                            <a href="{{ route('lot-requests.create') }}" class="nav-card-link">
                                <div class="nav-card success">
                                    <div class="nav-card-icon">
                                        <i class="fas fa-plus-circle"></i>
                                    </div>
                                    <div class="nav-card-content">
                                        <h5>Create New</h5>
                                        <span class="nav-card-count">+</span>
                                        <p>Add new lot request</p>
                                    </div>
                                    <div class="nav-card-arrow">
                                        <i class="fas fa-chevron-right"></i>
                                    </div>
                                </div>
                            </a>
                        </div>
                        
                        <div class="col-md-3">
                            <a href="{{ route('lot-requests.index', ['status' => 'pending']) }}" class="nav-card-link">
                                <div class="nav-card warning">
                                    <div class="nav-card-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="nav-card-content">
                                        <h5>Pending</h5>
                        <span class="nav-card-count" id="pendingRequestsCount">8</span>
                                        <p>Awaiting processing</p>
                                    </div>
                                    <div class="nav-card-arrow">
                                        <i class="fas fa-chevron-right"></i>
                                    </div>
                                </div>
                            </a>
                        </div>
                        
                        <div class="col-md-3">
                            <a href="{{ route('lot-requests.index', ['status' => 'completed']) }}" class="nav-card-link">
                                <div class="nav-card info">
                                    <div class="nav-card-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="nav-card-content">
                                        <h5>Completed</h5>
                        <span class="nav-card-count" id="completedRequestsCount">17</span>
                                        <p>Successfully processed</p>
                                    </div>
                                    <div class="nav-card-arrow">
                                        <i class="fas fa-chevron-right"></i>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- KPI Summary Cards -->
        <div class="row mb-4">
            <div class="col-xl-3">
                <div class="analytics-card">
                    <div class="analytics-card-header primary">
                        <div class="analytics-card-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="analytics-card-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12.5%</span>
                        </div>
                    </div>
                    <div class="analytics-card-body">
                        <h3 id="totalRequests">25</h3>
                        <p>Total Requests</p>
                        <div class="analytics-card-progress">
                            <div class="progress">
                                <div class="progress-bar bg-primary" style="width: 85%"></div>
                            </div>
                            <small class="text-muted">vs last period</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3">
                <div class="analytics-card">
                    <div class="analytics-card-header success">
                        <div class="analytics-card-icon">
                            <i class="fas fa-microchip"></i>
                        </div>
                        <div class="analytics-card-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+8.3%</span>
                        </div>
                    </div>
                    <div class="analytics-card-body">
                        <h3 id="totalEquipment">45</h3>
                        <p>Equipment Items</p>
                        <div class="analytics-card-progress">
                            <div class="progress">
                                <div class="progress-bar bg-success" style="width: 72%"></div>
                            </div>
                            <small class="text-muted">unique equipment</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3">
                <div class="analytics-card">
                    <div class="analytics-card-header warning">
                        <div class="analytics-card-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="analytics-card-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+15.7%</span>
                        </div>
                    </div>
                    <div class="analytics-card-body">
                        <h3 id="totalLots">1,250</h3>
                        <p>Total Lots</p>
                        <div class="analytics-card-progress">
                            <div class="progress">
                                <div class="progress-bar bg-warning" style="width: 68%"></div>
                            </div>
                            <small class="text-muted">requested</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3">
                <div class="analytics-card">
                    <div class="analytics-card-header info">
                        <div class="analytics-card-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="analytics-card-trend down">
                            <i class="fas fa-arrow-down"></i>
                            <span>-5.2h</span>
                        </div>
                    </div>
                    <div class="analytics-card-body">
                        <h3 id="avgProcessingTime">2.5h</h3>
                        <p>Avg Processing Time</p>
                        <div class="analytics-card-progress">
                            <div class="progress">
                                <div class="progress-bar bg-info" style="width: 45%"></div>
                            </div>
                            <small class="text-muted">improvement</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 1 -->
        <div class="row mb-4">
            <!-- Request Volume Trend -->
            <div class="col-xl-8">
                <div class="chart-card">
                    <div class="chart-card-header">
                        <div class="chart-card-title">
                            <h5><i class="fas fa-chart-line me-2"></i>Request Volume Trend</h5>
                            <p>Daily request volume over time</p>
                        </div>
                        <div class="chart-card-controls">
                            <div class="btn-group btn-group-sm" role="group">
                                <input type="radio" class="btn-check" name="volumeChart" id="volume7d" checked>
                                <label class="btn btn-outline-primary" for="volume7d">7D</label>
                                
                                <input type="radio" class="btn-check" name="volumeChart" id="volume30d">
                                <label class="btn btn-outline-primary" for="volume30d">30D</label>
                                
                                <input type="radio" class="btn-check" name="volumeChart" id="volume90d">
                                <label class="btn btn-outline-primary" for="volume90d">90D</label>
                            </div>
                        </div>
                    </div>
                    <div class="chart-card-body">
                        <canvas id="volumeTrendChart" height="80"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- Status Distribution -->
            <div class="col-xl-4">
                <div class="chart-card">
                    <div class="chart-card-header">
                        <div class="chart-card-title">
                            <h5><i class="fas fa-pie-chart me-2"></i>Status Distribution</h5>
                            <p>Current request status breakdown</p>
                        </div>
                    </div>
                    <div class="chart-card-body">
                        <canvas id="statusDistributionChart" height="120"></canvas>
                        <div class="status-legend">
                            <div class="legend-item">
                                <span class="legend-color pending"></span>
                                <span>Pending <strong id="pendingCount">8</strong></span>
                            </div>
                            <div class="legend-item">
                                <span class="legend-color in-process"></span>
                                <span>In Process <strong id="inProcessCount">5</strong></span>
                            </div>
                            <div class="legend-item">
                                <span class="legend-color completed"></span>
                                <span>Completed <strong id="completedCount">17</strong></span>
                            </div>
                            <div class="legend-item">
                                <span class="legend-color cancelled"></span>
                                <span>Cancelled <strong id="cancelledCount">2</strong></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 2 -->
        <div class="row mb-4">
            <!-- Equipment Utilization -->
            <div class="col-xl-6">
                <div class="chart-card">
                    <div class="chart-card-header">
                        <div class="chart-card-title">
                            <h5><i class="fas fa-cogs me-2"></i>Equipment Utilization</h5>
                            <p>Top equipment by request frequency</p>
                        </div>
                        <div class="chart-card-controls">
                            <select class="form-select form-select-sm" id="equipmentPeriod" onchange="updateEquipmentChart()">
                                <option value="week">This Week</option>
                                <option value="month">This Month</option>
                                <option value="quarter">This Quarter</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-card-body">
                        <canvas id="equipmentUtilizationChart" height="100"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- Processing Time Analysis -->
            <div class="col-xl-6">
                <div class="chart-card">
                    <div class="chart-card-header">
                        <div class="chart-card-title">
                            <h5><i class="fas fa-stopwatch me-2"></i>Processing Time Analysis</h5>
                            <p>Average time from request to completion</p>
                        </div>
                    </div>
                    <div class="chart-card-body">
                        <canvas id="processingTimeChart" height="100"></canvas>
                        <div class="processing-stats">
                            <div class="stat-item">
                                <span class="stat-label">Fastest:</span>
                                <span class="stat-value text-success" id="fastestTime">0.5h</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Average:</span>
                                <span class="stat-value text-primary" id="averageTime">2.5h</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Slowest:</span>
                                <span class="stat-value text-warning" id="slowestTime">8.2h</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-time Activity & Recent Requests -->
        <div class="row mb-4">
            <!-- Real-time Activity Feed -->
            <div class="col-xl-4">
                <div class="activity-card">
                    <div class="activity-card-header">
                        <h5><i class="fas fa-bell me-2"></i>Real-time Activity</h5>
                        <div class="activity-status">
                            <div class="status-indicator active"></div>
                            <span>Live</span>
                        </div>
                    </div>
                    <div class="activity-card-body" id="activityFeed">
                        <!-- Activity items will be loaded here via JavaScript -->
                        <div class="activity-item">
                            <div class="activity-icon new-request">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="activity-content">
                                <p><strong>New Request</strong> #LR-2024-0156</p>
                                <small>2 minutes ago</small>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon completed">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="activity-content">
                                <p><strong>Completed</strong> #LR-2024-0155</p>
                                <small>5 minutes ago</small>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon in-process">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="activity-content">
                                <p><strong>Processing</strong> #LR-2024-0154</p>
                                <small>8 minutes ago</small>
                            </div>
                        </div>
                    </div>
                    <div class="activity-card-footer">
                        <a href="{{ route('lot-requests.index') }}" class="btn btn-sm btn-outline-primary w-100">
                            View All Activities
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Recent Requests Table -->
            <div class="col-xl-8">
                <div class="table-card">
                    <div class="table-card-header">
                        <h5><i class="fas fa-history me-2"></i>Recent Requests</h5>
                        <a href="{{ route('lot-requests.index') }}" class="btn btn-sm btn-primary">
                            View All <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                    <div class="table-card-body">
                        <div class="table-responsive">
                            <table class="table table-hover recent-requests-table">
                                <thead>
                                    <tr>
                                        <th>Request #</th>
                                        <th>Requestor</th>
                                        <th>Equipment</th>
                                        <th>Lots</th>
                                        <th>Status</th>
                                        <th>Time</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="recentRequestsTable">
                                    <tr>
                                        <td>
                                            <a href="#" class="request-link">
                                                LR-2024-0156
                                            </a>
                                        </td>
                                        <td>
                                            <div class="user-info">
                                                <strong>John Smith</strong>
                                                <small>EMP001</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="equipment-count">3 items</span>
                                        </td>
                                        <td>
                                            <span class="lot-count">25</span>
                                        </td>
                                        <td>
                                            <span class="status-badge pending">
                                                Pending
                                            </span>
                                        </td>
                                        <td>
                                            <div class="time-info">
                                                <small>2 minutes ago</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="#" class="btn btn-sm btn-outline-info" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <a href="#" class="request-link">
                                                LR-2024-0155
                                            </a>
                                        </td>
                                        <td>
                                            <div class="user-info">
                                                <strong>Jane Doe</strong>
                                                <small>EMP002</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="equipment-count">2 items</span>
                                        </td>
                                        <td>
                                            <span class="lot-count">15</span>
                                        </td>
                                        <td>
                                            <span class="status-badge completed">
                                                Completed
                                            </span>
                                        </td>
                                        <td>
                                            <div class="time-info">
                                                <small>15 minutes ago</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="#" class="btn btn-sm btn-outline-info" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="row">
            <!-- Requestor Performance -->
            <div class="col-xl-4">
                <div class="metrics-card">
                    <div class="metrics-card-header">
                        <h5><i class="fas fa-users me-2"></i>Top Requestors</h5>
                        <span class="badge bg-info">This Month</span>
                    </div>
                    <div class="metrics-card-body">
                        <div class="requestor-list" id="topRequestors">
                            <div class="requestor-item">
                                <div class="requestor-rank">#1</div>
                                <div class="requestor-info">
                                    <strong>John Smith</strong>
                                    <small>EMP001</small>
                                </div>
                                <div class="requestor-stats">
                                    <span class="count">12</span>
                                    <small>requests</small>
                                </div>
                            </div>
                            <div class="requestor-item">
                                <div class="requestor-rank">#2</div>
                                <div class="requestor-info">
                                    <strong>Jane Doe</strong>
                                    <small>EMP002</small>
                                </div>
                                <div class="requestor-stats">
                                    <span class="count">8</span>
                                    <small>requests</small>
                                </div>
                            </div>
                            <div class="requestor-item">
                                <div class="requestor-rank">#3</div>
                                <div class="requestor-info">
                                    <strong>Mike Johnson</strong>
                                    <small>EMP003</small>
                                </div>
                                <div class="requestor-stats">
                                    <span class="count">5</span>
                                    <small>requests</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Area Station Analysis -->
            <div class="col-xl-4">
                <div class="metrics-card">
                    <div class="metrics-card-header">
                        <h5><i class="fas fa-map-marker-alt me-2"></i>Area Stations</h5>
                        <span class="badge bg-warning">Active</span>
                    </div>
                    <div class="metrics-card-body">
                        <canvas id="areaStationChart" height="120"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- System Health -->
            <div class="col-xl-4">
                <div class="metrics-card">
                    <div class="metrics-card-header">
                        <h5><i class="fas fa-heartbeat me-2"></i>System Health</h5>
                        <div class="health-indicator good">
                            <div class="health-dot"></div>
                            <span>Optimal</span>
                        </div>
                    </div>
                    <div class="metrics-card-body">
                        <div class="health-metrics">
                            <div class="health-metric">
                                <div class="metric-label">Response Time</div>
                                <div class="metric-value">
                                    <span class="value text-success">1.2s</span>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: 85%; background: var(--success);"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="health-metric">
                                <div class="metric-label">Success Rate</div>
                                <div class="metric-value">
                                    <span class="value text-success">98.7%</span>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: 98.7%; background: var(--success);"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="health-metric">
                                <div class="metric-label">Queue Length</div>
                                <div class="metric-value">
                                    <span class="value text-warning">3</span>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: 30%; background: var(--warning);"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="health-metric">
                                <div class="metric-label">Error Rate</div>
                                <div class="metric-value">
                                    <span class="value text-success">0.3%</span>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: 3%; background: var(--danger);"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Load external libraries -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    
    <!-- Load custom JavaScript -->
    <script src="{{ asset('js/lot-request.js') }}"></script>
    
    <!-- Pass data to JavaScript -->
    <script>
        window.lotRequestAnalytics = {
            total_requests: 25,
            total_equipment: 45,
            total_lots: 1250,
            avg_processing_time: '2.5h'
        };
        window.realtimeEndpoint = '/api/lot-requests/realtime';
    </script>
</x-app-layout>
