<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Product;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class ProductTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');
    }

    public function test_admin_can_view_products_index(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        $response = $this->actingAs($admin)->get(route('products.index'));
        
        $response->assertStatus(200);
        $response->assertViewIs('products.index');
    }

    public function test_regular_user_can_view_products_index(): void
    {
        $user = User::factory()->create(['role' => 'user']);
        
        $response = $this->actingAs($user)->get(route('products.index'));
        
        $response->assertStatus(200);
        $response->assertViewIs('products.index');
    }

    public function test_admin_can_create_product(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        $productData = [
            'name' => 'Test Product',
            'description' => 'Test Description',
            'price' => 99.99,
            'stock' => 10,
            'category' => 'Electronics',
            'status' => 'active',
        ];
        
        $response = $this->actingAs($admin)->post(route('products.store'), $productData);
        
        $response->assertRedirect(route('products.index'));
        $response->assertSessionHas('success');
        
        $this->assertDatabaseHas('products', [
            'name' => 'Test Product',
            'price' => 99.99,
            'created_by' => $admin->id,
        ]);
    }

    public function test_regular_user_cannot_create_product(): void
    {
        $user = User::factory()->create(['role' => 'user']);
        
        $response = $this->actingAs($user)->get(route('products.create'));
        
        $response->assertStatus(403);
    }

    public function test_admin_can_create_product_with_image(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        $file = UploadedFile::fake()->image('product.jpg');
        
        $productData = [
            'name' => 'Test Product with Image',
            'description' => 'Test Description',
            'price' => 149.99,
            'stock' => 5,
            'category' => 'Electronics',
            'status' => 'active',
            'image' => $file,
        ];
        
        $response = $this->actingAs($admin)->post(route('products.store'), $productData);
        
        $response->assertRedirect(route('products.index'));
        
        $product = Product::where('name', 'Test Product with Image')->first();
        $this->assertNotNull($product);
        $this->assertNotNull($product->image);
        
        Storage::disk('public')->assertExists($product->image);
    }

    public function test_product_validation_rules(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        // Test missing required fields
        $response = $this->actingAs($admin)->post(route('products.store'), []);
        
        $response->assertSessionHasErrors(['name', 'price', 'stock', 'status']);
    }

    public function test_admin_can_update_product(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $product = Product::factory()->create(['created_by' => $admin->id]);
        
        $updateData = [
            'name' => 'Updated Product Name',
            'description' => 'Updated Description',
            'price' => 199.99,
            'stock' => 15,
            'category' => 'Updated Category',
            'status' => 'active',
        ];
        
        $response = $this->actingAs($admin)->put(route('products.update', $product), $updateData);
        
        $response->assertRedirect(route('products.index'));
        
        $product->refresh();
        $this->assertEquals('Updated Product Name', $product->name);
        $this->assertEquals(199.99, $product->price);
    }

    public function test_admin_can_delete_product(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $product = Product::factory()->create(['created_by' => $admin->id]);
        
        $response = $this->actingAs($admin)->delete(route('products.destroy', $product));
        
        $response->assertRedirect(route('products.index'));
        $this->assertDatabaseMissing('products', ['id' => $product->id]);
    }

    public function test_product_show_page_displays_correctly(): void
    {
        $user = User::factory()->create();
        $product = Product::factory()->create();
        
        $response = $this->actingAs($user)->get(route('products.show', $product));
        
        $response->assertStatus(200);
        $response->assertViewIs('products.show');
        $response->assertSee($product->name);
        $response->assertSee(number_format($product->price, 2));
    }

    public function test_product_stock_and_status_methods(): void
    {
        $inStockProduct = Product::factory()->create(['stock' => 10, 'status' => 'active']);
        $outOfStockProduct = Product::factory()->create(['stock' => 0, 'status' => 'active']);
        $inactiveProduct = Product::factory()->create(['stock' => 5, 'status' => 'inactive']);
        
        $this->assertTrue($inStockProduct->inStock());
        $this->assertTrue($inStockProduct->isActive());
        
        $this->assertFalse($outOfStockProduct->inStock());
        $this->assertTrue($outOfStockProduct->isActive());
        
        $this->assertTrue($inactiveProduct->inStock());
        $this->assertFalse($inactiveProduct->isActive());
    }
}
