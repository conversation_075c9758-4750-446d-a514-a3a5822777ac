<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class LotRequestPriority extends Model
{
    use HasFactory;

    protected $fillable = [
        'lot_request_id',
        'fifo_order',
        'manual_priority',
        'calculated_priority',
        'priority_notes',
        'priority_set_by',
        'priority_updated_at'
    ];

    protected $casts = [
        'priority_updated_at' => 'datetime',
        'fifo_order' => 'integer',
        'manual_priority' => 'integer',
        'calculated_priority' => 'integer'
    ];

    /**
     * Get the lot request that owns this priority record
     */
    public function lotRequest()
    {
        return $this->belongsTo(LotRequest::class);
    }

    /**
     * Calculate the final priority score
     * Lower score = Higher priority
     */
    public function getFinalPriorityAttribute()
    {
        // Manual priority overrides calculated priority
        if ($this->manual_priority > 0) {
            return $this->manual_priority;
        }
        
        // Use FIFO order as base, then calculated priority
        return $this->fifo_order + $this->calculated_priority;
    }

    /**
     * Get priority level description
     */
    public function getPriorityLevelAttribute()
    {
        $priority = $this->final_priority;
        
        if ($priority <= 10) return 'Critical';
        if ($priority <= 50) return 'High';
        if ($priority <= 100) return 'Medium';
        return 'Normal';
    }

    /**
     * Get priority badge class for display
     */
    public function getPriorityBadgeClassAttribute()
    {
        return match($this->priority_level) {
            'Critical' => 'bg-danger',
            'High' => 'bg-warning',
            'Medium' => 'bg-info',
            default => 'bg-secondary'
        };
    }
}