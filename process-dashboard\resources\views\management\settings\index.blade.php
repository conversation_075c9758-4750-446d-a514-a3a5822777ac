<x-app-layout>
    <x-slot name="header">
        System Settings
    </x-slot>

    <!-- Settings Navigation -->
    <div class="row g-4 mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="mb-3">Settings Categories</h5>
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="{{ route('management.settings.general') }}" class="btn btn-outline-primary w-100 h-100">
                                <i class="fas fa-cog fa-2x mb-2 d-block"></i>
                                <strong>General Settings</strong>
                                <small class="d-block text-muted">App name, timezone, unit</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('management.settings.email') }}" class="btn btn-outline-success w-100 h-100">
                                <i class="fas fa-envelope fa-2x mb-2 d-block"></i>
                                <strong>Email Settings</strong>
                                <small class="d-block text-muted">SMTP configuration</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('management.settings.system') }}" class="btn btn-outline-info w-100 h-100">
                                <i class="fas fa-server fa-2x mb-2 d-block"></i>
                                <strong>System Settings</strong>
                                <small class="d-block text-muted">Performance & maintenance</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('management.settings.security') }}" class="btn btn-outline-warning w-100 h-100">
                                <i class="fas fa-shield-alt fa-2x mb-2 d-block"></i>
                                <strong>Security Settings</strong>
                                <small class="d-block text-muted">Passwords & authentication</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Settings Overview -->
    <div class="row g-4">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Current Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <!-- General Settings -->
                        <div class="col-md-6">
                            <h6 class="text-muted border-bottom pb-2">General Settings</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-tag text-primary me-2"></i>
                                    <strong>Name:</strong> {{ $settings['app_name'] }}
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-envelope text-success me-2"></i>
                                    <strong>Contact:</strong> {{ $settings['contact_email'] }}
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-globe text-info me-2"></i>
                                    <strong>Timezone:</strong> {{ $settings['timezone'] }}
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-cube text-secondary me-2"></i>
                                    <strong>Unit:</strong> {{ $settings['unit'] ?? 'PCS' }}
                                </li>
                            </ul>
                        </div>
                        
                        <!-- System Settings -->
                        <div class="col-md-6">
                            <h6 class="text-muted border-bottom pb-2">System Status</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-bug text-warning me-2"></i>
                                    <strong>Debug Mode:</strong> 
                                    <span class="badge {{ $settings['debug_mode'] ? 'bg-warning' : 'bg-success' }}">
                                        {{ $settings['debug_mode'] ? 'ON' : 'OFF' }}
                                    </span>
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-memory text-info me-2"></i>
                                    <strong>Cache:</strong> 
                                    <span class="badge {{ $settings['cache_enabled'] ? 'bg-success' : 'bg-warning' }}">
                                        {{ $settings['cache_enabled'] ? 'ON' : 'OFF' }}
                                    </span>
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-clock text-secondary me-2"></i>
                                    <strong>Session:</strong> {{ $settings['session_lifetime'] }} min
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-upload text-primary me-2"></i>
                                    <strong>Max Upload:</strong> {{ $settings['max_upload_size'] }} MB
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-tools me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <a href="{{ route('management.settings.general') }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>Edit General Settings
                        </a>
                        
                        <form action="{{ route('management.settings.clear-cache') }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-outline-warning w-100">
                                <i class="fas fa-broom me-2"></i>Clear Application Cache
                            </button>
                        </form>
                        
                        <form action="{{ route('management.settings.optimize') }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-outline-success w-100">
                                <i class="fas fa-tachometer-alt me-2"></i>Optimize Application
                            </button>
                        </form>
                    </div>
                    
                    <hr class="my-3">
                    
                    <div class="d-grid gap-2">
                        <a href="{{ route('management.settings.download-logs') }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-download me-2"></i>Download Logs
                        </a>
                        
                        <form action="{{ route('management.settings.clear-logs') }}" 
                              method="POST" 
                              class="d-inline"
                              onsubmit="return confirm('Are you sure you want to clear all logs?')">
                            @csrf
                            <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                <i class="fas fa-trash me-2"></i>Clear Logs
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Security & Email Overview -->
    <div class="row g-4 mt-2">
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>Security Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 text-center">
                            <div class="security-metric">
                                <i class="fas fa-lock fa-2x {{ $settings['password_min_length'] >= 8 ? 'text-success' : 'text-warning' }} mb-2"></i>
                                <h6>Password Security</h6>
                                <small class="text-muted">Min {{ $settings['password_min_length'] }} chars</small>
                            </div>
                        </div>
                        <div class="col-6 text-center">
                            <div class="security-metric">
                                <i class="fas fa-user-shield fa-2x {{ $settings['two_factor_enabled'] ? 'text-success' : 'text-muted' }} mb-2"></i>
                                <h6>Two-Factor Auth</h6>
                                <small class="text-muted">{{ $settings['two_factor_enabled'] ? 'Enabled' : 'Disabled' }}</small>
                            </div>
                        </div>
                        <div class="col-6 text-center">
                            <div class="security-metric">
                                <i class="fas fa-ban fa-2x text-info mb-2"></i>
                                <h6>Login Attempts</h6>
                                <small class="text-muted">Max {{ $settings['login_attempts_limit'] }} tries</small>
                            </div>
                        </div>
                        <div class="col-6 text-center">
                            <div class="security-metric">
                                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                <h6>Lockout Duration</h6>
                                <small class="text-muted">{{ $settings['lockout_duration'] }} minutes</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-envelope me-2"></i>Email Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <div class="email-config">
                        <div class="mb-3">
                            <label class="form-label text-muted small">From Address</label>
                            <p class="mb-0">{{ $settings['mail_from_address'] }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">From Name</label>
                            <p class="mb-0">{{ $settings['mail_from_name'] }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">SMTP Host</label>
                            <p class="mb-0">{{ $settings['smtp_host'] ?: 'Not configured' }}</p>
                        </div>
                        <div class="mb-0">
                            <label class="form-label text-muted small">SMTP Port</label>
                            <p class="mb-0">{{ $settings['smtp_port'] ?: 'Not configured' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Help -->
    <div class="row g-4 mt-2">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb text-warning me-2"></i>Settings Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="info-item mb-3">
                                <i class="fas fa-cog text-primary mb-2 fa-2x"></i>
                                <h6>General Settings</h6>
                                <p class="text-muted small mb-0">
                                    Configure app name, description, contact email, timezone, and measurement units.
                                </p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-item mb-3">
                                <i class="fas fa-envelope text-success mb-2 fa-2x"></i>
                                <h6>Email Settings</h6>
                                <p class="text-muted small mb-0">
                                    Configure SMTP settings for email notifications and user communications.
                                </p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-item mb-3">
                                <i class="fas fa-server text-info mb-2 fa-2x"></i>
                                <h6>System Settings</h6>
                                <p class="text-muted small mb-0">
                                    Manage performance, maintenance mode, caching, and system optimization.
                                </p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-item mb-3">
                                <i class="fas fa-shield-alt text-warning mb-2 fa-2x"></i>
                                <h6>Security Settings</h6>
                                <p class="text-muted small mb-0">
                                    Configure password requirements, two-factor authentication, and login security.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .security-metric {
            padding: 1rem;
        }
        
        .info-item {
            text-align: center;
            padding: 1rem;
        }
        
        .info-item i {
            display: block;
            margin: 0 auto;
        }
    </style>
</x-app-layout>