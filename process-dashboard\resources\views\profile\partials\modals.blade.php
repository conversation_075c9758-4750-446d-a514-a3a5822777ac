<!-- Avatar Upload Modal -->
<div class="modal fade" id="avatarModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Change Profile Picture</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="avatarForm" enctype="multipart/form-data">
                    @csrf
                    <div class="text-center mb-3">
                        @if(Auth::user()->avatar)
                            <img id="avatarPreview" src="{{ asset('storage/' . Auth::user()->avatar) }}" alt="Avatar Preview" class="rounded-circle" width="150" height="150" style="object-fit: cover;">
                        @else
                            <div id="avatarPreview" class="rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 150px; height: 150px; background-color: #6366f1; color: white; font-weight: 600; font-size: 48px;">
                                {{ strtoupper(substr(Auth::user()->emp_name, 0, 1)) }}
                            </div>
                        @endif
                    </div>
                    <div class="mb-3">
                        <label for="avatar" class="form-label">Choose Image</label>
                        <input type="file" class="form-control" id="avatar" name="avatar" accept="image/*">
                        <div class="form-text">Use JPEG, PNG, or GIF. Best size: 200x200 pixels. Keep it under 5MB</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="removeAvatar">Remove</button>
                <button type="button" class="btn btn-primary" id="uploadAvatar">Change Image</button>
            </div>
        </div>
    </div>
</div>

<!-- Deactivate Account Modal -->
<div class="modal fade" id="deactivateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header border-danger">
                <h5 class="modal-title text-danger">Deactivate Account</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> This action cannot be undone. All your data will be permanently deleted.
                </div>
                <p>Are you sure you want to deactivate your account? This will:</p>
                <ul>
                    <li>Permanently delete your profile</li>
                    <li>Remove all your orders and data</li>
                    <li>Revoke access to all features</li>
                </ul>
                <form method="post" action="{{ route('profile.destroy') }}" id="deactivateForm">
                    @csrf
                    @method('delete')
                    <div class="mb-3">
                        <label for="deactivatePassword" class="form-label">Enter your password to confirm:</label>
                        <input type="password" class="form-control" id="deactivatePassword" name="password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="deactivateForm" class="btn btn-danger">Deactivate Account</button>
            </div>
        </div>
    </div>
</div>

<!-- Success Messages -->
@if (session('status') === 'profile-updated')
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 1050">
        <div class="toast show" role="alert">
            <div class="toast-header">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong class="me-auto">Success</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">Profile updated successfully!</div>
        </div>
    </div>
@endif

@if (session('status') === 'password-updated')
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 1050">
        <div class="toast show" role="alert">
            <div class="toast-header">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong class="me-auto">Success</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">Password updated successfully!</div>
        </div>
    </div>
@endif

@if (session('status') === 'avatar-updated')
    <div class="position-fixed top-0 end-0 p-3" style="z-index: 1050">
        <div class="toast show" role="alert">
            <div class="toast-header">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong class="me-auto">Success</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">Profile picture updated successfully!</div>
        </div>
    </div>
@endif