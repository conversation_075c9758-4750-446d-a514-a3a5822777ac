<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EqpCapaRef extends Model
{
    use HasFactory;

    protected $table = 'eqp_capa_ref';

    protected $fillable = [
        'work_type',
        'size',
        'oee',
        'passing',
        'yield',
    ];

    protected $casts = [
        'oee' => 'decimal:4',
        'passing' => 'decimal:4',
        'yield' => 'decimal:4',
    ];

    /**
     * Get equipment capacity reference by work type and size
     */
    public static function getByWorkTypeAndSize($workType, $size)
    {
        return self::where('work_type', $workType)
                   ->where('size', $size)
                   ->first();
    }

    /**
     * Get all unique work types
     */
    public static function getUniqueWorkTypes()
    {
        return self::distinct()->pluck('work_type')->sort();
    }

    /**
     * Get all unique sizes
     */
    public static function getUniqueSizes()
    {
        return self::distinct()->pluck('size')->sort();
    }

    /**
     * Get all reference combinations
     */
    public static function getAllCombinations()
    {
        return self::select('work_type', 'size', 'oee', 'passing', 'yield')
                   ->orderBy('work_type')
                   ->orderBy('size')
                   ->get();
    }
}