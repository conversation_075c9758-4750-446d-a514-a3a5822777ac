<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Auth;
use App\Auth\EmpNoUserProvider;

class EmpNoUserServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        Auth::provider('emp_no_eloquent', function ($app, array $config) {
            return new EmpNoUserProvider($app['hash'], $config['model']);
        });
    }
}
