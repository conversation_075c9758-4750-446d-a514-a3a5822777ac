#!/usr/bin/env pwsh
Write-Host "Clearing Laravel caches..." -ForegroundColor Green
Write-Host ""

Write-Host "[1/6] Clearing view cache..." -ForegroundColor Yellow
php artisan view:clear

Write-Host "[2/6] Clearing application cache..." -ForegroundColor Yellow
php artisan cache:clear

Write-Host "[3/6] Clearing configuration cache..." -ForegroundColor Yellow
php artisan config:clear

Write-Host "[4/6] Clearing route cache..." -ForegroundColor Yellow
php artisan route:clear

Write-Host "[5/6] Manually clearing compiled views..." -ForegroundColor Yellow
if (Test-Path "storage/framework/views") {
    Remove-Item "storage/framework/views/*" -Force -Recurse -ErrorAction SilentlyContinue
    Write-Host "Views directory cleared" -ForegroundColor Green
}

Write-Host "[6/6] Optimizing application..." -ForegroundColor Yellow
php artisan optimize:clear

Write-Host ""
Write-Host "All caches cleared successfully!" -ForegroundColor Green
Write-Host "You can now access your application without cache issues." -ForegroundColor Cyan