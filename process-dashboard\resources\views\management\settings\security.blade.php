<x-app-layout>
    <x-slot name="header">
        Security Settings
    </x-slot>

    <!-- Breadcrumb -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('management.settings.index') }}">Settings</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Security</li>
                </ol>
            </nav>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>Security Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('management.settings.update.security') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row g-4">
                            <!-- Password Requirements -->
                            <div class="col-12">
                                <h6 class="text-muted border-bottom pb-2">Password Requirements</h6>
                            </div>

                            <div class="col-md-6">
                                <label for="password_min_length" class="form-label">Minimum Length <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control @error('password_min_length') is-invalid @enderror" 
                                           id="password_min_length" 
                                           name="password_min_length" 
                                           value="{{ old('password_min_length', $settings['password_min_length']) }}" 
                                           min="6" 
                                           max="32" 
                                           required>
                                    <span class="input-group-text">characters</span>
                                </div>
                                <div class="form-text">Minimum 6, maximum 32 characters</div>
                                @error('password_min_length')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">Password Complexity</label>
                                <div class="password-requirements">
                                    <div class="form-check">
                                        <input class="form-check-input @error('password_require_uppercase') is-invalid @enderror" 
                                               type="checkbox" 
                                               id="password_require_uppercase" 
                                               name="password_require_uppercase" 
                                               value="1"
                                               {{ old('password_require_uppercase', $settings['password_require_uppercase']) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="password_require_uppercase">
                                            Require uppercase letters (A-Z)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input @error('password_require_lowercase') is-invalid @enderror" 
                                               type="checkbox" 
                                               id="password_require_lowercase" 
                                               name="password_require_lowercase" 
                                               value="1"
                                               {{ old('password_require_lowercase', $settings['password_require_lowercase']) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="password_require_lowercase">
                                            Require lowercase letters (a-z)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input @error('password_require_numbers') is-invalid @enderror" 
                                               type="checkbox" 
                                               id="password_require_numbers" 
                                               name="password_require_numbers" 
                                               value="1"
                                               {{ old('password_require_numbers', $settings['password_require_numbers']) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="password_require_numbers">
                                            Require numbers (0-9)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input @error('password_require_symbols') is-invalid @enderror" 
                                               type="checkbox" 
                                               id="password_require_symbols" 
                                               name="password_require_symbols" 
                                               value="1"
                                               {{ old('password_require_symbols', $settings['password_require_symbols']) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="password_require_symbols">
                                            Require symbols (!@#$%^&*)
                                        </label>
                                    </div>
                                </div>
                                @error('password_require_uppercase')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                                @error('password_require_lowercase')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                                @error('password_require_numbers')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                                @error('password_require_symbols')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Login Security -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">Login Security</h6>
                            </div>

                            <div class="col-md-6">
                                <label for="login_attempts_limit" class="form-label">Max Login Attempts <span class="text-danger">*</span></label>
                                <select class="form-select @error('login_attempts_limit') is-invalid @enderror" 
                                        id="login_attempts_limit" 
                                        name="login_attempts_limit" 
                                        required>
                                    <option value="">Select Max Attempts</option>
                                    @for($i = 3; $i <= 10; $i++)
                                        <option value="{{ $i }}" {{ old('login_attempts_limit', $settings['login_attempts_limit']) == $i ? 'selected' : '' }}>
                                            {{ $i }} attempts
                                        </option>
                                    @endfor
                                </select>
                                <div class="form-text">Number of failed attempts before lockout</div>
                                @error('login_attempts_limit')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="lockout_duration" class="form-label">Lockout Duration <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <select class="form-select @error('lockout_duration') is-invalid @enderror" 
                                            id="lockout_duration" 
                                            name="lockout_duration" 
                                            required>
                                        <option value="">Select Duration</option>
                                        @php
                                            $durations = [
                                                1 => '1 minute',
                                                5 => '5 minutes',
                                                10 => '10 minutes',
                                                15 => '15 minutes',
                                                30 => '30 minutes',
                                                60 => '1 hour',
                                            ];
                                        @endphp
                                        @foreach($durations as $value => $label)
                                            <option value="{{ $value }}" {{ old('lockout_duration', $settings['lockout_duration']) == $value ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-text">How long accounts remain locked</div>
                                @error('lockout_duration')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Session & Access Control -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">
                                    <i class="fas fa-clock me-2"></i>Session & Access Control
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <label for="session_timeout" class="form-label">Session Timeout</label>
                                <div class="input-group">
                                    <select class="form-select @error('session_timeout') is-invalid @enderror" 
                                            id="session_timeout" 
                                            name="session_timeout">
                                        @php
                                            $timeouts = [
                                                15 => '15 minutes',
                                                30 => '30 minutes', 
                                                60 => '1 hour',
                                                120 => '2 hours',
                                                240 => '4 hours',
                                                480 => '8 hours',
                                                1440 => '24 hours'
                                            ];
                                        @endphp
                                        @foreach($timeouts as $value => $label)
                                            <option value="{{ $value }}" {{ old('session_timeout', $settings['session_timeout'] ?? 120) == $value ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-text">Automatic logout after inactivity</div>
                                @error('session_timeout')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="max_concurrent_sessions" class="form-label">Max Concurrent Sessions</label>
                                <select class="form-select @error('max_concurrent_sessions') is-invalid @enderror" 
                                        id="max_concurrent_sessions" 
                                        name="max_concurrent_sessions">
                                    @for($i = 1; $i <= 10; $i++)
                                        <option value="{{ $i }}" {{ old('max_concurrent_sessions', $settings['max_concurrent_sessions'] ?? 3) == $i ? 'selected' : '' }}>
                                            {{ $i }} {{ $i == 1 ? 'session' : 'sessions' }}
                                        </option>
                                    @endfor
                                    <option value="unlimited" {{ old('max_concurrent_sessions', $settings['max_concurrent_sessions'] ?? 3) == 'unlimited' ? 'selected' : '' }}>Unlimited</option>
                                </select>
                                <div class="form-text">Maximum simultaneous logins per user</div>
                                @error('max_concurrent_sessions')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input @error('force_logout_on_password_change') is-invalid @enderror" 
                                           type="checkbox" 
                                           id="force_logout_on_password_change" 
                                           name="force_logout_on_password_change" 
                                           value="1"
                                           {{ old('force_logout_on_password_change', $settings['force_logout_on_password_change'] ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="force_logout_on_password_change">
                                        <strong>Force Logout on Password Change</strong>
                                    </label>
                                    <div class="form-text">End all sessions when password is changed</div>
                                    @error('force_logout_on_password_change')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input @error('remember_me_enabled') is-invalid @enderror" 
                                           type="checkbox" 
                                           id="remember_me_enabled" 
                                           name="remember_me_enabled" 
                                           value="1"
                                           {{ old('remember_me_enabled', $settings['remember_me_enabled'] ?? true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="remember_me_enabled">
                                        <strong>Allow "Remember Me" Option</strong>
                                    </label>
                                    <div class="form-text">Users can choose to stay logged in</div>
                                    @error('remember_me_enabled')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- IP & Access Restrictions -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">
                                    <i class="fas fa-globe me-2"></i>IP & Access Restrictions
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input @error('ip_whitelist_enabled') is-invalid @enderror" 
                                           type="checkbox" 
                                           id="ip_whitelist_enabled" 
                                           name="ip_whitelist_enabled" 
                                           value="1"
                                           {{ old('ip_whitelist_enabled', $settings['ip_whitelist_enabled'] ?? false) ? 'checked' : '' }}
                                           onchange="toggleIpWhitelist()">
                                    <label class="form-check-label" for="ip_whitelist_enabled">
                                        <strong>Enable IP Whitelist</strong>
                                    </label>
                                    <div class="form-text">Restrict access to specific IP addresses</div>
                                    @error('ip_whitelist_enabled')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input @error('security_notifications') is-invalid @enderror" 
                                           type="checkbox" 
                                           id="security_notifications" 
                                           name="security_notifications" 
                                           value="1"
                                           {{ old('security_notifications', $settings['security_notifications'] ?? true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="security_notifications">
                                        <strong>Security Notifications</strong>
                                    </label>
                                    <div class="form-text">Email notifications for security events</div>
                                    @error('security_notifications')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-12" id="ip_whitelist_section" style="display: {{ old('ip_whitelist_enabled', $settings['ip_whitelist_enabled'] ?? false) ? 'block' : 'none' }};">
                                <label for="allowed_ips" class="form-label">Allowed IP Addresses</label>
                                <textarea class="form-control @error('allowed_ips') is-invalid @enderror" 
                                          id="allowed_ips" 
                                          name="allowed_ips" 
                                          rows="4" 
                                          placeholder="***********&#10;***********/24&#10;10.0.0.0/8">{{ old('allowed_ips', $settings['allowed_ips'] ?? '') }}</textarea>
                                <div class="form-text">One IP address or CIDR range per line. Current IP: <strong>{{ request()->ip() }}</strong></div>
                                @error('allowed_ips')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Two-Factor Authentication -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">
                                    <i class="fas fa-mobile-alt me-2"></i>Two-Factor Authentication
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input @error('two_factor_enabled') is-invalid @enderror" 
                                           type="checkbox" 
                                           id="two_factor_enabled" 
                                           name="two_factor_enabled" 
                                           value="1"
                                           {{ old('two_factor_enabled', $settings['two_factor_enabled']) ? 'checked' : '' }}
                                           onchange="toggle2FAOptions()">
                                    <label class="form-check-label" for="two_factor_enabled">
                                        <strong>Enable Two-Factor Authentication</strong>
                                    </label>
                                    <div class="form-text">Require users to verify their identity with a second factor</div>
                                    @error('two_factor_enabled')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6" id="two_factor_method" style="display: {{ old('two_factor_enabled', $settings['two_factor_enabled']) ? 'block' : 'none' }};">
                                <label for="two_factor_method_select" class="form-label">2FA Method</label>
                                <select class="form-select @error('two_factor_method_select') is-invalid @enderror" 
                                        id="two_factor_method_select" 
                                        name="two_factor_method_select">
                                    <option value="app" {{ old('two_factor_method_select', $settings['two_factor_method'] ?? 'app') == 'app' ? 'selected' : '' }}>Authenticator App</option>
                                    <option value="sms" {{ old('two_factor_method_select', $settings['two_factor_method'] ?? 'app') == 'sms' ? 'selected' : '' }}>SMS (Text Message)</option>
                                    <option value="email" {{ old('two_factor_method_select', $settings['two_factor_method'] ?? 'app') == 'email' ? 'selected' : '' }}>Email</option>
                                </select>
                                <div class="form-text">Default 2FA method for new users</div>
                                @error('two_factor_method_select')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-12" id="two_factor_status">
                                @if($settings['two_factor_enabled'])
                                <div class="alert alert-success mt-3">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    <strong>Two-Factor Authentication is enabled.</strong>
                                    Users will be prompted to set up 2FA on their next login.
                                </div>
                                @else
                                <div class="alert alert-warning mt-3">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Two-Factor Authentication is disabled.</strong>
                                    Enable it for enhanced account security.
                                </div>
                                @endif
                            </div>

                            <!-- Security Actions -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">
                                    <i class="fas fa-tools me-2"></i>Security Actions
                                </h6>
                            </div>

                            <div class="col-12">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <button type="button" class="btn btn-outline-warning w-100" onclick="logoutAllUsers()">
                                            <i class="fas fa-sign-out-alt me-2"></i>Logout All Users
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button type="button" class="btn btn-outline-info w-100" onclick="viewActiveSessions()">
                                            <i class="fas fa-users me-2"></i>Active Sessions
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button type="button" class="btn btn-outline-primary w-100" onclick="viewSecurityLog()">
                                            <i class="fas fa-history me-2"></i>Security Log
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button type="button" class="btn btn-outline-success w-100" onclick="testSecuritySettings()">
                                            <i class="fas fa-check-circle me-2"></i>Test Settings
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Security Recommendations -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">Security Status</h6>
                            </div>

                            <div class="col-12">
                                <div class="security-checklist">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="security-item d-flex align-items-center mb-2">
                                                <i class="fas fa-{{ $settings['password_min_length'] >= 8 ? 'check-circle text-success' : 'times-circle text-danger' }} me-2"></i>
                                                <span>Password length {{ $settings['password_min_length'] >= 8 ? 'adequate' : 'too short' }}</span>
                                            </div>
                                            <div class="security-item d-flex align-items-center mb-2">
                                                <i class="fas fa-{{ ($settings['password_require_uppercase'] && $settings['password_require_lowercase']) ? 'check-circle text-success' : 'times-circle text-danger' }} me-2"></i>
                                                <span>Mixed case {{ ($settings['password_require_uppercase'] && $settings['password_require_lowercase']) ? 'required' : 'not required' }}</span>
                                            </div>
                                            <div class="security-item d-flex align-items-center mb-2">
                                                <i class="fas fa-{{ $settings['password_require_numbers'] ? 'check-circle text-success' : 'times-circle text-danger' }} me-2"></i>
                                                <span>Numbers {{ $settings['password_require_numbers'] ? 'required' : 'not required' }}</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="security-item d-flex align-items-center mb-2">
                                                <i class="fas fa-{{ $settings['password_require_symbols'] ? 'check-circle text-success' : 'times-circle text-danger' }} me-2"></i>
                                                <span>Symbols {{ $settings['password_require_symbols'] ? 'required' : 'not required' }}</span>
                                            </div>
                                            <div class="security-item d-flex align-items-center mb-2">
                                                <i class="fas fa-{{ $settings['login_attempts_limit'] <= 5 ? 'check-circle text-success' : 'times-circle text-warning' }} me-2"></i>
                                                <span>Login attempts {{ $settings['login_attempts_limit'] <= 5 ? 'appropriately limited' : 'could be stricter' }}</span>
                                            </div>
                                            <div class="security-item d-flex align-items-center mb-2">
                                                <i class="fas fa-{{ $settings['two_factor_enabled'] ? 'check-circle text-success' : 'times-circle text-warning' }} me-2"></i>
                                                <span>Two-factor auth {{ $settings['two_factor_enabled'] ? 'enabled' : 'disabled' }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="col-12 mt-4">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('management.settings.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Settings
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Save Changes
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Current Security Settings</h6>
                </div>
                <div class="card-body">
                    <div class="security-overview">
                        <div class="mb-3">
                            <label class="form-label text-muted small">Password Length</label>
                            <p class="mb-0">{{ $settings['password_min_length'] }} characters minimum</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">Password Requirements</label>
                            <ul class="list-unstyled small">
                                <li>{{ $settings['password_require_uppercase'] ? '✓' : '✗' }} Uppercase letters</li>
                                <li>{{ $settings['password_require_lowercase'] ? '✓' : '✗' }} Lowercase letters</li>
                                <li>{{ $settings['password_require_numbers'] ? '✓' : '✗' }} Numbers</li>
                                <li>{{ $settings['password_require_symbols'] ? '✓' : '✗' }} Symbols</li>
                            </ul>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">Login Security</label>
                            <p class="mb-0">{{ $settings['login_attempts_limit'] }} attempts, {{ $settings['lockout_duration'] }} min lockout</p>
                        </div>
                        <div class="mb-0">
                            <label class="form-label text-muted small">Two-Factor Auth</label>
                            <p class="mb-0">
                                <span class="badge {{ $settings['two_factor_enabled'] ? 'bg-success' : 'bg-warning' }}">
                                    {{ $settings['two_factor_enabled'] ? 'Enabled' : 'Disabled' }}
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Security Recommendations</h6>
                </div>
                <div class="card-body">
                    <div class="security-tips">
                        <div class="alert alert-info">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>Tip:</strong> Enable all password requirements for maximum security.
                        </div>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-shield text-primary me-2"></i>Use at least 8 character passwords</li>
                            <li><i class="fas fa-shield text-primary me-2"></i>Require mixed case letters</li>
                            <li><i class="fas fa-shield text-primary me-2"></i>Include numbers and symbols</li>
                            <li><i class="fas fa-shield text-primary me-2"></i>Enable two-factor authentication</li>
                            <li><i class="fas fa-shield text-primary me-2"></i>Limit login attempts to 5 or fewer</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Security Score</h6>
                </div>
                <div class="card-body text-center">
                    @php
                        $score = 0;
                        if ($settings['password_min_length'] >= 8) $score += 20;
                        if ($settings['password_require_uppercase'] && $settings['password_require_lowercase']) $score += 20;
                        if ($settings['password_require_numbers']) $score += 15;
                        if ($settings['password_require_symbols']) $score += 15;
                        if ($settings['login_attempts_limit'] <= 5) $score += 15;
                        if ($settings['two_factor_enabled']) $score += 15;
                        
                        $scoreColor = $score >= 80 ? 'success' : ($score >= 60 ? 'warning' : 'danger');
                    @endphp
                    <div class="security-score">
                        <div class="display-4 text-{{ $scoreColor }}">{{ $score }}%</div>
                        <p class="text-muted">Security Level</p>
                        <div class="progress">
                            <div class="progress-bar bg-{{ $scoreColor }}" 
                                 role="progressbar" 
                                 style="--progress-width: {{ $score }}%; width: var(--progress-width);" 
                                 aria-valuenow="{{ $score }}" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Dynamic Security Settings -->
    <script>
        // Toggle IP Whitelist section
        function toggleIpWhitelist() {
            const checkbox = document.getElementById('ip_whitelist_enabled');
            const section = document.getElementById('ip_whitelist_section');
            section.style.display = checkbox.checked ? 'block' : 'none';
        }
        
        // Toggle 2FA options
        function toggle2FAOptions() {
            const checkbox = document.getElementById('two_factor_enabled');
            const methodDiv = document.getElementById('two_factor_method');
            const statusDiv = document.getElementById('two_factor_status');
            
            methodDiv.style.display = checkbox.checked ? 'block' : 'none';
            
            // Update status message
            if (checkbox.checked) {
                statusDiv.innerHTML = `
                    <div class="alert alert-success mt-3">
                        <i class="fas fa-shield-alt me-2"></i>
                        <strong>Two-Factor Authentication will be enabled.</strong>
                        Users will be prompted to set up 2FA on their next login.
                    </div>`;
            } else {
                statusDiv.innerHTML = `
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Two-Factor Authentication will be disabled.</strong>
                        Enable it for enhanced account security.
                    </div>`;
            }
        }
        
        // Security action functions
        function logoutAllUsers() {
            if (confirm('Are you sure you want to logout all users? This will end all active sessions except yours.')) {
                // Make AJAX request to logout all users
                fetch('/management/settings/security/logout-all-users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('All users have been logged out successfully.');
                    } else {
                        alert('Failed to logout all users: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    alert('Error: ' + error.message);
                });
            }
        }
        
        function viewActiveSessions() {
            // Open active sessions in a modal or new window
            const sessionsWindow = window.open('/management/settings/security/active-sessions', 'ActiveSessions', 'width=800,height=600,scrollbars=yes');
            if (!sessionsWindow) {
                alert('Please allow popups to view active sessions.');
            }
        }
        
        function viewSecurityLog() {
            // Open security log in a modal or new window
            const logWindow = window.open('/management/settings/security/security-log', 'SecurityLog', 'width=1000,height=700,scrollbars=yes');
            if (!logWindow) {
                alert('Please allow popups to view security log.');
            }
        }
        
        function testSecuritySettings() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Testing...';
            btn.disabled = true;
            
            // Simulate security test
            setTimeout(() => {
                // Get current settings for testing
                const passwordLength = document.getElementById('password_min_length').value;
                const loginAttempts = document.getElementById('login_attempts_limit').value;
                const twoFactorEnabled = document.getElementById('two_factor_enabled').checked;
                const ipWhitelistEnabled = document.getElementById('ip_whitelist_enabled').checked;
                
                let testResults = 'Security Settings Test Results:\n\n';
                
                // Test password strength
                if (passwordLength >= 12) {
                    testResults += '✓ Password Length: Excellent (12+ chars)\n';
                } else if (passwordLength >= 8) {
                    testResults += '✓ Password Length: Good (8+ chars)\n';
                } else {
                    testResults += '⚠ Password Length: Weak (<8 chars)\n';
                }
                
                // Test login attempts
                if (loginAttempts <= 3) {
                    testResults += '✓ Login Attempts: Secure (\u2264 3)\n';
                } else if (loginAttempts <= 5) {
                    testResults += '✓ Login Attempts: Moderate (\u2264 5)\n';
                } else {
                    testResults += '⚠ Login Attempts: Lenient (>5)\n';
                }
                
                // Test 2FA
                testResults += twoFactorEnabled ? '✓ Two-Factor Auth: Enabled\n' : '⚠ Two-Factor Auth: Disabled\n';
                
                // Test IP restriction
                testResults += ipWhitelistEnabled ? '✓ IP Whitelist: Active\n' : 'ℹ IP Whitelist: Not configured\n';
                
                testResults += '\nRecommendation: ';
                if (passwordLength >= 8 && loginAttempts <= 5 && twoFactorEnabled) {
                    testResults += 'Your security settings are well configured!';
                } else {
                    testResults += 'Consider strengthening some security settings.';
                }
                
                alert(testResults);
                
                btn.innerHTML = originalText;
                btn.disabled = false;
            }, 2000);
        }
        
        // Initialize settings on page load
        document.addEventListener('DOMContentLoaded', function() {
            toggleIpWhitelist();
            toggle2FAOptions();
        });
    </script>
</x-app-layout>
